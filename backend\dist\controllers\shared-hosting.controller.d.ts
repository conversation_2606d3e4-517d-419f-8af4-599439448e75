import { FastifyRequest, FastifyReply } from 'fastify';
export interface SharedHostingUser {
    id: string;
    user_id: string;
    username: string;
    linux_username: string;
    home_directory: string;
    plan: string;
    status: 'active' | 'suspended' | 'disabled';
    server_id: string;
    server_ip: string;
    port: number;
    ssh_port: number;
    ftp_port: number;
    resource_limits: {
        cpu_quota: number;
        memory_max: number;
        bandwidth_limit: number;
        storage_limit: number;
    };
    usage: {
        cpu_usage: number;
        memory_usage: number;
        bandwidth_used: number;
        storage_used: number;
    };
    created_at: string;
    last_login?: string;
    applications: SharedHostingApplication[];
}
export interface SharedHostingApplication {
    id: string;
    name: string;
    type: 'static-website' | 'web-service' | 'nodejs-app' | 'php-app';
    domain?: string;
    subdomain: string;
    directory: string;
    status: 'running' | 'stopped' | 'building' | 'error';
    port?: number;
    ssl_enabled: boolean;
    created_at: string;
    last_deployed?: string;
}
export interface CreateSharedUserRequest {
    user_id: string;
    username: string;
    plan: string;
    server_id?: string;
}
export interface CreateApplicationRequest {
    name: string;
    type: 'static-website' | 'web-service' | 'nodejs-app' | 'php-app';
    domain?: string;
    git_repo?: string;
    build_command?: string;
    start_command?: string;
}
export declare function createSharedUserController(request: FastifyRequest<{
    Body: CreateSharedUserRequest;
}>, reply: FastifyReply): Promise<FastifyReply>;
export declare function getSharedUserController(request: FastifyRequest<{
    Params: {
        userId: string;
    };
}>, reply: FastifyReply): Promise<FastifyReply>;
export declare function createApplicationController(request: FastifyRequest<{
    Params: {
        userId: string;
    };
    Body: CreateApplicationRequest;
}>, reply: FastifyReply): Promise<FastifyReply>;
export declare function deployApplicationController(request: FastifyRequest<{
    Params: {
        userId: string;
        appId: string;
    };
}>, reply: FastifyReply): Promise<FastifyReply>;
export declare function getServerStatusController(request: FastifyRequest<{
    Params: {
        serverId: string;
    };
}>, reply: FastifyReply): Promise<FastifyReply>;
export declare function listServerUsersController(request: FastifyRequest<{
    Params: {
        serverId: string;
    };
    Querystring: {
        status?: string;
        limit?: string;
        offset?: string;
    };
}>, reply: FastifyReply): Promise<FastifyReply>;
export declare function toggleUserStatusController(request: FastifyRequest<{
    Params: {
        userId: string;
    };
    Body: {
        action: 'suspend' | 'unsuspend' | 'disable';
        reason?: string;
    };
}>, reply: FastifyReply): Promise<FastifyReply>;
export declare function getUserFileStructureController(request: FastifyRequest<{
    Params: {
        userId: string;
    };
    Querystring: {
        path?: string;
    };
}>, reply: FastifyReply): Promise<FastifyReply>;
//# sourceMappingURL=shared-hosting.controller.d.ts.map