"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.cloudflareDNS = exports.CloudflareDNSService = void 0;
const logger_1 = require("../utils/logger");
const config_1 = require("../config");
class CloudflareDNSService {
    apiToken;
    zoneId;
    baseUrl = 'https://api.cloudflare.com/client/v4';
    constructor() {
        this.apiToken = config_1.appConfig.cloudflare.apiToken;
        this.zoneId = config_1.appConfig.cloudflare.zoneId;
    }
    async makeRequest(endpoint, method = 'GET', data) {
        const url = `${this.baseUrl}${endpoint}`;
        const options = {
            method,
            headers: {
                'Authorization': `Bearer ${this.apiToken}`,
                'Content-Type': 'application/json',
            },
        };
        if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
            options.body = JSON.stringify(data);
        }
        try {
            const response = await fetch(url, options);
            const result = await response.json();
            if (!result.success) {
                throw new Error(`Cloudflare API error: ${result.errors?.map((e) => e.message).join(', ')}`);
            }
            return result.result;
        }
        catch (error) {
            logger_1.logger.error(`Cloudflare API request failed: ${endpoint}`, error);
            throw error;
        }
    }
    async createDNSRecord(record) {
        try {
            const result = await this.makeRequest(`/zones/${this.zoneId}/dns_records`, 'POST', {
                type: record.type,
                name: record.name,
                content: record.content,
                ttl: record.ttl || 300,
                priority: record.priority,
                proxied: record.proxied || false
            });
            logger_1.logger.info(`Created DNS record: ${record.name} -> ${record.content}`);
            return result.id;
        }
        catch (error) {
            logger_1.logger.error(`Failed to create DNS record for ${record.name}:`, error);
            throw error;
        }
    }
    async updateDNSRecord(recordId, updates) {
        try {
            await this.makeRequest(`/zones/${this.zoneId}/dns_records/${recordId}`, 'PATCH', updates);
            logger_1.logger.info(`Updated DNS record ${recordId}`);
        }
        catch (error) {
            logger_1.logger.error(`Failed to update DNS record ${recordId}:`, error);
            throw error;
        }
    }
    async deleteDNSRecord(recordId) {
        try {
            await this.makeRequest(`/zones/${this.zoneId}/dns_records/${recordId}`, 'DELETE');
            logger_1.logger.info(`Deleted DNS record ${recordId}`);
        }
        catch (error) {
            logger_1.logger.error(`Failed to delete DNS record ${recordId}:`, error);
            throw error;
        }
    }
    async getDNSRecords(name, type) {
        try {
            let endpoint = `/zones/${this.zoneId}/dns_records`;
            const params = new URLSearchParams();
            if (name)
                params.append('name', name);
            if (type)
                params.append('type', type);
            if (params.toString()) {
                endpoint += `?${params.toString()}`;
            }
            const records = await this.makeRequest(endpoint);
            return records;
        }
        catch (error) {
            logger_1.logger.error('Failed to get DNS records:', error);
            throw error;
        }
    }
    async setupRoundRobinDNS(subdomain, serverIPs) {
        try {
            const recordIds = [];
            const fullDomain = `${subdomain}.${config_1.appConfig.domain.primary}`;
            const existingRecords = await this.getDNSRecords(fullDomain, 'A');
            for (const record of existingRecords) {
                if (record.id) {
                    await this.deleteDNSRecord(record.id);
                }
            }
            for (const ip of serverIPs) {
                const recordId = await this.createDNSRecord({
                    type: 'A',
                    name: fullDomain,
                    content: ip,
                    ttl: 300,
                    proxied: false
                });
                recordIds.push(recordId);
            }
            logger_1.logger.info(`Setup round-robin DNS for ${fullDomain} with ${serverIPs.length} servers`);
            return recordIds;
        }
        catch (error) {
            logger_1.logger.error(`Failed to setup round-robin DNS for ${subdomain}:`, error);
            throw error;
        }
    }
    async createLoadBalancerPool(pool) {
        try {
            const result = await this.makeRequest('/load_balancers/pools', 'POST', {
                name: pool.name,
                origins: pool.origins,
                monitor: pool.monitor,
                enabled: pool.enabled,
                minimum_origins: 1,
                check_regions: ['WNAM', 'ENAM', 'WEU', 'EEU', 'SEAS', 'NEAS']
            });
            logger_1.logger.info(`Created load balancer pool: ${pool.name}`);
            return result.id;
        }
        catch (error) {
            logger_1.logger.error(`Failed to create load balancer pool ${pool.name}:`, error);
            throw error;
        }
    }
    async createLoadBalancer(name, hostname, poolIds) {
        try {
            const result = await this.makeRequest('/load_balancers', 'POST', {
                name,
                fallback_pool: poolIds[0],
                default_pools: poolIds,
                description: `Load balancer for ${hostname}`,
                ttl: 30,
                steering_policy: 'dynamic_latency',
                proxied: true,
                enabled: true,
                zone_id: this.zoneId,
                hostname
            });
            logger_1.logger.info(`Created load balancer: ${name} for ${hostname}`);
            return result.id;
        }
        catch (error) {
            logger_1.logger.error(`Failed to create load balancer ${name}:`, error);
            throw error;
        }
    }
    async setupLoadBalancing(subdomain, servers) {
        try {
            const fullDomain = `${subdomain}.${config_1.appConfig.domain.primary}`;
            const origins = servers.map((server, index) => ({
                name: `server-${index + 1}`,
                address: server.ip,
                enabled: true,
                weight: server.weight || 1
            }));
            const poolId = await this.createLoadBalancerPool({
                name: `${subdomain}-pool`,
                origins,
                enabled: true
            });
            const loadBalancerId = await this.createLoadBalancer(`${subdomain}-lb`, fullDomain, [poolId]);
            logger_1.logger.info(`Setup load balancing for ${fullDomain}`);
            return { poolId, loadBalancerId };
        }
        catch (error) {
            logger_1.logger.error(`Failed to setup load balancing for ${subdomain}:`, error);
            throw error;
        }
    }
    async updateServerHealth(poolId, serverAddress, enabled) {
        try {
            const pool = await this.makeRequest(`/load_balancers/pools/${poolId}`);
            const updatedOrigins = pool.origins.map((origin) => {
                if (origin.address === serverAddress) {
                    return { ...origin, enabled };
                }
                return origin;
            });
            await this.makeRequest(`/load_balancers/pools/${poolId}`, 'PATCH', {
                origins: updatedOrigins
            });
            logger_1.logger.info(`Updated server ${serverAddress} health to ${enabled ? 'enabled' : 'disabled'} in pool ${poolId}`);
        }
        catch (error) {
            logger_1.logger.error(`Failed to update server health for ${serverAddress}:`, error);
            throw error;
        }
    }
    async createHealthMonitor(name, path = '/health') {
        try {
            const result = await this.makeRequest('/load_balancers/monitors', 'POST', {
                type: 'http',
                description: `Health monitor for ${name}`,
                method: 'GET',
                path,
                header: {
                    'Host': [config_1.appConfig.domain.primary],
                    'User-Agent': ['Cloudflare-Health-Check']
                },
                timeout: 5,
                retries: 2,
                interval: 60,
                expected_codes: '200',
                follow_redirects: false,
                allow_insecure: false
            });
            logger_1.logger.info(`Created health monitor: ${name}`);
            return result.id;
        }
        catch (error) {
            logger_1.logger.error(`Failed to create health monitor ${name}:`, error);
            throw error;
        }
    }
    async getLoadBalancerAnalytics(loadBalancerId, since) {
        try {
            const sinceParam = since ? since.toISOString() : new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();
            const analytics = await this.makeRequest(`/load_balancers/${loadBalancerId}/analytics/events?since=${sinceParam}`);
            return analytics;
        }
        catch (error) {
            logger_1.logger.error(`Failed to get load balancer analytics for ${loadBalancerId}:`, error);
            throw error;
        }
    }
    async failoverToBackup(loadBalancerId, backupPoolId) {
        try {
            await this.makeRequest(`/load_balancers/${loadBalancerId}`, 'PATCH', {
                fallback_pool: backupPoolId,
                enabled: true
            });
            logger_1.logger.info(`Initiated failover for load balancer ${loadBalancerId} to backup pool ${backupPoolId}`);
        }
        catch (error) {
            logger_1.logger.error(`Failed to failover load balancer ${loadBalancerId}:`, error);
            throw error;
        }
    }
    async getZoneAnalytics(since) {
        try {
            const sinceParam = since ? since.toISOString() : new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();
            const analytics = await this.makeRequest(`/zones/${this.zoneId}/analytics/dashboard?since=${sinceParam}`);
            return analytics;
        }
        catch (error) {
            logger_1.logger.error('Failed to get zone analytics:', error);
            throw error;
        }
    }
    async purgeCache(urls) {
        try {
            await this.makeRequest(`/zones/${this.zoneId}/purge_cache`, 'POST', {
                files: urls
            });
            logger_1.logger.info(`Purged cache for ${urls.length} URLs`);
        }
        catch (error) {
            logger_1.logger.error('Failed to purge cache:', error);
            throw error;
        }
    }
    async getDNSStatus() {
        try {
            const zone = await this.makeRequest(`/zones/${this.zoneId}`);
            const records = await this.getDNSRecords();
            return {
                zone: {
                    name: zone.name,
                    status: zone.status,
                    nameServers: zone.name_servers
                },
                recordCount: records.length,
                lastChecked: new Date().toISOString()
            };
        }
        catch (error) {
            logger_1.logger.error('Failed to get DNS status:', error);
            throw error;
        }
    }
}
exports.CloudflareDNSService = CloudflareDNSService;
exports.cloudflareDNS = new CloudflareDNSService();
//# sourceMappingURL=cloudflare-dns.js.map