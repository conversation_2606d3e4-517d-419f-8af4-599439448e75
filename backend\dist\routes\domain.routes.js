"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.domainRoutes = domainRoutes;
const domain_controller_1 = require("../controllers/domain.controller");
const auth_1 = require("../middleware/auth");
async function domainRoutes(fastify) {
    fastify.get('/tlds/popular', domain_controller_1.getPopularTldsController);
    fastify.get('/suggestions', domain_controller_1.getDomainSuggestionsController);
    fastify.post('/availability', domain_controller_1.checkDomainAvailabilityController);
    fastify.register(async function (fastify) {
        fastify.addHook('preHandler', auth_1.authMiddleware);
        fastify.post('/transfer', domain_controller_1.transferDomainController);
        fastify.post('/whois', domain_controller_1.whoisLookupController);
    });
}
//# sourceMappingURL=domain.routes.js.map