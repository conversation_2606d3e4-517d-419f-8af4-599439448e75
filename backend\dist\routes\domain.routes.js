"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.domainRoutes = domainRoutes;
const domain_controller_1 = require("../controllers/domain.controller");
const auth_1 = require("../middleware/auth");
async function domainRoutes(fastify) {
    fastify.get('/tlds/popular', domain_controller_1.getPopularTldsController);
    fastify.get('/suggestions', domain_controller_1.getDomainSuggestionsController);
    fastify.register(async function (fastify) {
        fastify.addHook('preHandler', auth_1.authMiddleware);
        fastify.post('/availability', {
            schema: {
                description: 'Check domain availability for multiple domains and TLDs',
                tags: ['domains'],
                body: {
                    type: 'object',
                    required: ['domains', 'tlds'],
                    properties: {
                        domains: {
                            type: 'array',
                            items: { type: 'string' },
                            description: 'Array of domain names to check (without TLD)'
                        },
                        tlds: {
                            type: 'array',
                            items: { type: 'string' },
                            description: 'Array of TLDs to check against'
                        }
                    }
                },
                response: {
                    200: {
                        type: 'object',
                        properties: {
                            success: { type: 'boolean' },
                            data: {
                                type: 'object',
                                additionalProperties: {
                                    type: 'object',
                                    properties: {
                                        status: {
                                            type: 'string',
                                            enum: ['available', 'regthroughus', 'regthroughothers', 'unknown']
                                        },
                                        classkey: { type: 'string' },
                                        ispremiumname: { type: 'boolean' },
                                        premiumcost: { type: 'number' },
                                        eapfee: { type: 'number' }
                                    }
                                }
                            },
                            meta: {
                                type: 'object',
                                properties: {
                                    timestamp: { type: 'string' },
                                    request_id: { type: 'string' },
                                    trace_id: { type: 'string' },
                                    version: { type: 'string' },
                                    status_code: { type: 'number' }
                                }
                            }
                        }
                    }
                }
            }
        }, domain_controller_1.checkDomainAvailabilityController);
        fastify.post('/transfer', {
            schema: {
                description: 'Transfer a domain to your account',
                tags: ['domains'],
                body: {
                    type: 'object',
                    required: [
                        'domainName', 'customerId', 'regContactId', 'adminContactId',
                        'techContactId', 'billingContactId', 'invoiceOption', 'autoRenew'
                    ],
                    properties: {
                        domainName: {
                            type: 'string',
                            description: 'Full domain name to transfer'
                        },
                        authCode: {
                            type: 'string',
                            description: 'Authorization code for domain transfer'
                        },
                        customerId: {
                            type: 'number',
                            description: 'Customer ID for the domain owner'
                        },
                        regContactId: {
                            type: 'number',
                            description: 'Registrant contact ID'
                        },
                        adminContactId: {
                            type: 'number',
                            description: 'Administrative contact ID'
                        },
                        techContactId: {
                            type: 'number',
                            description: 'Technical contact ID'
                        },
                        billingContactId: {
                            type: 'number',
                            description: 'Billing contact ID'
                        },
                        invoiceOption: {
                            type: 'string',
                            enum: ['NoInvoice', 'PayInvoice', 'KeepInvoice', 'OnlyAdd'],
                            description: 'Invoice handling option'
                        },
                        autoRenew: {
                            type: 'boolean',
                            description: 'Enable auto-renewal for the domain'
                        },
                        purchasePrivacy: {
                            type: 'boolean',
                            description: 'Purchase domain privacy protection'
                        },
                        protectPrivacy: {
                            type: 'boolean',
                            description: 'Protect existing privacy settings'
                        },
                        nameServers: {
                            type: 'array',
                            items: { type: 'string' },
                            description: 'Custom name servers for the domain'
                        },
                        attributes: {
                            type: 'object',
                            description: 'Additional domain attributes (TLD-specific)'
                        },
                        purchasePremiumDns: {
                            type: 'boolean',
                            description: 'Purchase premium DNS service'
                        }
                    }
                },
                response: {
                    201: {
                        type: 'object',
                        properties: {
                            success: { type: 'boolean' },
                            data: {
                                type: 'object',
                                properties: {
                                    description: { type: 'string' },
                                    entityid: { type: 'string' },
                                    actiontype: { type: 'string' },
                                    actiontypedesc: { type: 'string' },
                                    eaqid: { type: 'string' },
                                    actionstatus: { type: 'string' },
                                    actionstatusdesc: { type: 'string' },
                                    invoiceid: { type: 'string' },
                                    sellingcurrencysymbol: { type: 'string' },
                                    sellingamount: { type: 'number' },
                                    customerid: { type: 'number' }
                                }
                            },
                            meta: {
                                type: 'object',
                                properties: {
                                    timestamp: { type: 'string' },
                                    request_id: { type: 'string' },
                                    trace_id: { type: 'string' },
                                    version: { type: 'string' },
                                    status_code: { type: 'number' }
                                }
                            }
                        }
                    }
                }
            }
        }, domain_controller_1.transferDomainController);
        fastify.post('/whois', {
            schema: {
                description: 'Perform WHOIS lookup for a domain',
                tags: ['domains'],
                body: {
                    type: 'object',
                    required: ['domainName'],
                    properties: {
                        domainName: {
                            type: 'string',
                            description: 'Domain name to lookup'
                        }
                    }
                },
                response: {
                    200: {
                        type: 'object',
                        properties: {
                            success: { type: 'boolean' },
                            data: {
                                type: 'object',
                                properties: {
                                    domain: { type: 'string' },
                                    registrar: { type: 'string' },
                                    registrant: {
                                        type: 'object',
                                        properties: {
                                            name: { type: 'string' },
                                            organization: { type: 'string' },
                                            email: { type: 'string' },
                                            phone: { type: 'string' },
                                            address: { type: 'string' },
                                            city: { type: 'string' },
                                            state: { type: 'string' },
                                            country: { type: 'string' },
                                            postalCode: { type: 'string' }
                                        }
                                    },
                                    nameServers: {
                                        type: 'array',
                                        items: { type: 'string' }
                                    },
                                    creationDate: { type: 'string' },
                                    expirationDate: { type: 'string' },
                                    updatedDate: { type: 'string' },
                                    status: {
                                        type: 'array',
                                        items: { type: 'string' }
                                    }
                                }
                            },
                            meta: {
                                type: 'object',
                                properties: {
                                    timestamp: { type: 'string' },
                                    request_id: { type: 'string' },
                                    trace_id: { type: 'string' },
                                    version: { type: 'string' },
                                    status_code: { type: 'number' }
                                }
                            }
                        }
                    }
                }
            }
        }, domain_controller_1.whoisLookupController);
    });
}
//# sourceMappingURL=domain.routes.js.map