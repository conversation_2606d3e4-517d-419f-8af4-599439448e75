"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.metricsCollector = exports.MetricsCollectorService = void 0;
const logger_1 = require("../utils/logger");
const config_1 = require("../config");
const shared_hosting_1 = require("../models/shared-hosting");
const shared_hosting_2 = require("./shared-hosting");
class MetricsCollectorService {
    collectionInterval = null;
    isCollecting = false;
    constructor() {
        if (config_1.appConfig.monitoring.enabled) {
            this.startCollection();
        }
    }
    startCollection() {
        if (this.collectionInterval) {
            clearInterval(this.collectionInterval);
        }
        const intervalMs = config_1.appConfig.monitoring.intervalSeconds * 1000;
        this.collectionInterval = setInterval(async () => {
            if (!this.isCollecting) {
                this.isCollecting = true;
                try {
                    await this.collectServerMetrics();
                    await this.collectUserUsageMetrics();
                }
                catch (error) {
                    logger_1.logger.error('Error collecting metrics:', error);
                }
                finally {
                    this.isCollecting = false;
                }
            }
        }, intervalMs);
        logger_1.logger.info(`Started metrics collection with ${config_1.appConfig.monitoring.intervalSeconds}s interval`);
    }
    stopCollection() {
        if (this.collectionInterval) {
            clearInterval(this.collectionInterval);
            this.collectionInterval = null;
        }
        logger_1.logger.info('Stopped metrics collection');
    }
    async collectServerMetrics() {
        const ssh = new shared_hosting_2.SSHConnection();
        try {
            logger_1.logger.info('🔌 Connecting to server for hourly metrics collection...');
            await ssh.connect();
            logger_1.logger.info('📊 Collecting server metrics...');
            const [cpuUsage, memoryUsage, diskUsage, loadAverage, networkStats] = await Promise.all([
                this.getCpuUsage(ssh),
                this.getMemoryUsage(ssh),
                this.getDiskUsage(ssh),
                this.getLoadAverage(ssh),
                this.getNetworkStats(ssh)
            ]);
            const activeUsers = await shared_hosting_1.SharedHostingUser.countDocuments({
                server_id: config_1.appConfig.ssh.host,
                status: 'active'
            });
            const activeApplications = await shared_hosting_1.SharedHostingUser.aggregate([
                { $match: { server_id: config_1.appConfig.ssh.host, status: 'active' } },
                { $lookup: { from: 'shared_hosting_applications', localField: 'user_id', foreignField: 'user_id', as: 'apps' } },
                { $unwind: '$apps' },
                { $match: { 'apps.status': 'running' } },
                { $count: 'total' }
            ]);
            const metrics = new shared_hosting_1.ServerMetrics({
                server_id: config_1.appConfig.ssh.host,
                server_ip: config_1.appConfig.ssh.host,
                timestamp: new Date(),
                cpu_usage: cpuUsage,
                memory_usage: memoryUsage,
                disk_usage: diskUsage,
                load_average: loadAverage,
                network_in: networkStats.bytesIn,
                network_out: networkStats.bytesOut,
                active_users: activeUsers,
                active_applications: activeApplications[0]?.total || 0
            });
            await metrics.save();
            logger_1.logger.info(`✅ Server metrics collected: CPU ${cpuUsage}%, Memory ${memoryUsage}%, Disk ${diskUsage}%, Users: ${activeUsers}, Apps: ${activeApplications[0]?.total || 0}`);
        }
        catch (error) {
            logger_1.logger.error('❌ Failed to collect server metrics:', error);
        }
        finally {
            logger_1.logger.info('🔌 Disconnecting from server after metrics collection');
            await ssh.disconnect();
        }
    }
    async collectUserUsageMetrics() {
        try {
            const activeUsers = await shared_hosting_1.SharedHostingUser.find({
                status: 'active',
                server_id: config_1.appConfig.ssh.host
            });
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            for (const user of activeUsers) {
                const existingAnalytics = await shared_hosting_1.UserUsageAnalytics.findOne({
                    user_id: user.user_id,
                    date: today
                });
                if (!existingAnalytics) {
                    const analytics = new shared_hosting_1.UserUsageAnalytics({
                        user_id: user.user_id,
                        date: today,
                        cpu_usage_avg: user.usage.cpu_usage,
                        cpu_usage_max: user.usage.cpu_usage,
                        memory_usage_avg: user.usage.memory_usage,
                        memory_usage_max: user.usage.memory_usage,
                        bandwidth_used: user.usage.bandwidth_used,
                        storage_used: user.usage.storage_used,
                        requests_count: 0,
                        uptime_percentage: 100
                    });
                    await analytics.save();
                }
                else {
                    existingAnalytics.cpu_usage_avg = (existingAnalytics.cpu_usage_avg + user.usage.cpu_usage) / 2;
                    existingAnalytics.cpu_usage_max = Math.max(existingAnalytics.cpu_usage_max, user.usage.cpu_usage);
                    existingAnalytics.memory_usage_avg = (existingAnalytics.memory_usage_avg + user.usage.memory_usage) / 2;
                    existingAnalytics.memory_usage_max = Math.max(existingAnalytics.memory_usage_max, user.usage.memory_usage);
                    existingAnalytics.bandwidth_used = user.usage.bandwidth_used;
                    existingAnalytics.storage_used = user.usage.storage_used;
                    await existingAnalytics.save();
                }
            }
            logger_1.logger.debug(`Updated usage analytics for ${activeUsers.length} users`);
        }
        catch (error) {
            logger_1.logger.error('Failed to collect user usage metrics:', error);
        }
    }
    async getCpuUsage(ssh) {
        try {
            const result = await ssh.executeCommand("top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | awk -F'%' '{print $1}'");
            return parseFloat(result.trim()) || 0;
        }
        catch (error) {
            logger_1.logger.error('Failed to get CPU usage:', error);
            return 0;
        }
    }
    async getMemoryUsage(ssh) {
        try {
            const result = await ssh.executeCommand("free | grep Mem | awk '{printf \"%.1f\", $3/$2 * 100.0}'");
            return parseFloat(result.trim()) || 0;
        }
        catch (error) {
            logger_1.logger.error('Failed to get memory usage:', error);
            return 0;
        }
    }
    async getDiskUsage(ssh) {
        try {
            const result = await ssh.executeCommand("df -h / | awk 'NR==2{printf \"%s\", $5}' | sed 's/%//'");
            return parseFloat(result.trim()) || 0;
        }
        catch (error) {
            logger_1.logger.error('Failed to get disk usage:', error);
            return 0;
        }
    }
    async getLoadAverage(ssh) {
        try {
            const result = await ssh.executeCommand("uptime | awk -F'load average:' '{print $2}' | awk -F',' '{print $1}' | xargs");
            return parseFloat(result.trim()) || 0;
        }
        catch (error) {
            logger_1.logger.error('Failed to get load average:', error);
            return 0;
        }
    }
    async getNetworkStats(ssh) {
        try {
            const result = await ssh.executeCommand("cat /proc/net/dev | grep eth0 | awk '{print $2, $10}'");
            const [bytesIn, bytesOut] = result.trim().split(' ').map(Number);
            return {
                bytesIn: bytesIn || 0,
                bytesOut: bytesOut || 0
            };
        }
        catch (error) {
            logger_1.logger.error('Failed to get network stats:', error);
            return { bytesIn: 0, bytesOut: 0 };
        }
    }
    async cleanupOldMetrics() {
        try {
            const retentionDate = new Date();
            retentionDate.setDate(retentionDate.getDate() - config_1.appConfig.monitoring.retentionDays);
            const deletedServerMetrics = await shared_hosting_1.ServerMetrics.deleteMany({
                timestamp: { $lt: retentionDate }
            });
            const deletedUserAnalytics = await shared_hosting_1.UserUsageAnalytics.deleteMany({
                date: { $lt: retentionDate }
            });
            logger_1.logger.info(`Cleaned up ${deletedServerMetrics.deletedCount} server metrics and ${deletedUserAnalytics.deletedCount} user analytics records older than ${config_1.appConfig.monitoring.retentionDays} days`);
        }
        catch (error) {
            logger_1.logger.error('Failed to cleanup old metrics:', error);
        }
    }
}
exports.MetricsCollectorService = MetricsCollectorService;
exports.metricsCollector = new MetricsCollectorService();
//# sourceMappingURL=metrics-collector.js.map