import { SSHConnection } from './shared-hosting';
interface AbuseAlert {
    userId: string;
    type: 'cpu' | 'memory' | 'bandwidth' | 'storage' | 'requests';
    severity: 'warning' | 'critical' | 'suspend';
    value: number;
    threshold: number;
    timestamp: Date;
    action: string;
}
export declare class AbusePreventionService {
    private monitoringInterval;
    private isMonitoring;
    private thresholds;
    constructor();
    startMonitoring(): void;
    stopMonitoring(): void;
    checkForAbuse(): Promise<void>;
    checkUserAbuse(user: any): Promise<void>;
    checkServerAbuse(): Promise<void>;
    private createAlert;
    private getActionForSeverity;
    processAlert(alert: AbuseAlert): Promise<void>;
    logAndNotify(alert: Abu<PERSON>Alert): Promise<void>;
    throttleUserResources(alert: AbuseAlert): Promise<void>;
    suspendUser(alert: AbuseAlert): Promise<void>;
    throttleCPU(ssh: SSHConnection, user: any, percentage: number): Promise<void>;
    throttleMemory(ssh: SSHConnection, user: any, percentage: number): Promise<void>;
    throttleBandwidth(ssh: SSHConnection, user: any, percentage: number): Promise<void>;
    throttleRequests(ssh: SSHConnection, user: any): Promise<void>;
    stopUserServices(userId: string): Promise<void>;
    handleServerOverload(type: string, usage: number): Promise<void>;
    getTopResourceConsumers(type: string, limit: number): Promise<any[]>;
    getAbuseStats(): Promise<any>;
}
export {};
//# sourceMappingURL=abuse-prevention.d.ts.map