import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { appConfig } from '../config';
import { logger } from '../utils/logger';

// Domain availability status types
export type DomainAvailabilityStatus = 'available' | 'regthroughus' | 'regthroughothers' | 'unknown';

// Domain availability interfaces
export interface DomainAvailabilityRequest {
  domains: string[];
  tlds: string[];
}

export interface DomainAvailabilityResult {
  [domain: string]: {
    status: DomainAvailabilityStatus;
    classkey?: string;
    ispremiumname?: boolean;
    premiumcost?: number;
    eapfee?: number;
    trademark?: {
      trademark_name: string;
      trademark_number: string;
      trademark_country: string;
      trademark_date: string;
    };
  };
}

// Domain transfer interfaces
export interface DomainTransferRequest {
  domainName: string;
  authCode?: string;
  customerId: number;
  regContactId: number;
  adminContactId: number;
  techContactId: number;
  billingContactId: number;
  invoiceOption: 'NoInvoice' | 'PayInvoice' | 'KeepInvoice' | 'OnlyAdd';
  autoRenew: boolean;
  purchasePrivacy?: boolean;
  protectPrivacy?: boolean;
  nameServers?: string[];
  attributes?: { [key: string]: string };
  purchasePremiumDns?: boolean;
}

export interface DomainTransferResult {
  description: string;
  entityid: string;
  actiontype: string;
  actiontypedesc: string;
  eaqid: string;
  actionstatus: string;
  actionstatusdesc: string;
  invoiceid?: string;
  sellingcurrencysymbol?: string;
  sellingamount?: number;
  customerid: number;
  privacydetails?: any;
}

// WHOIS lookup interfaces
export interface WhoisLookupRequest {
  domainName: string;
}

export interface WhoisLookupResult {
  domain: string;
  registrar: string;
  registrant: {
    name: string;
    organization?: string;
    email?: string;
    phone?: string;
    address?: string;
    city?: string;
    state?: string;
    country?: string;
    postalCode?: string;
  };
  admin?: {
    name: string;
    organization?: string;
    email?: string;
    phone?: string;
  };
  tech?: {
    name: string;
    organization?: string;
    email?: string;
    phone?: string;
  };
  nameServers: string[];
  creationDate?: string;
  expirationDate?: string;
  updatedDate?: string;
  status: string[];
}

// Product interfaces
export interface ProductDetails {
  [productKey: string]: {
    productKey?: string;
    name?: string;
    description?: string;
    type?: string;
    price?: number;
    currency?: string;
    features?: string[];
    fetchedAt?: string;
    [key: string]: any;
  };
}

export interface CategoryMapping {
  [category: string]: {
    category: string;
    productKeys: string[];
    productCount: number;
    fetchedAt: string;
  };
}

export interface AggregatedProductData {
  success: boolean;
  fetchedAt: string;
  summary: {
    totalProducts: number;
    totalCategories: number;
  };
  productsByCategory: {
    [category: string]: {
      category: string;
      productCount: number;
      products: ProductDetails;
    };
  };
  allProducts: ProductDetails;
  categoryMappings: CategoryMapping;
}

export class ResellerBizService {
  private client: AxiosInstance;
  private authUserId: string;
  private apiKey: string;

  constructor() {
    this.authUserId = appConfig.resellerBiz.authUserId;
    this.apiKey = appConfig.resellerBiz.apiKey;

    this.client = axios.create({
      baseURL: 'https://httpapi.com/api',
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Add request/response interceptors for logging
    this.client.interceptors.request.use(
      (config) => {
        logger.debug(`ResellerBiz API Request: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        logger.error('ResellerBiz API Request Error:', error);
        return Promise.reject(error);
      }
    );

    this.client.interceptors.response.use(
      (response) => {
        logger.debug(`ResellerBiz API Response: ${response.status} ${response.config.url}`);
        return response;
      },
      (error) => {
        logger.error('ResellerBiz API Response Error:', {
          status: error.response?.status,
          data: error.response?.data,
          url: error.config?.url,
        });
        return Promise.reject(error);
      }
    );
  }

  /**
   * Check domain availability
   */
  async checkDomainAvailability(request: DomainAvailabilityRequest): Promise<DomainAvailabilityResult> {
    try {
      logger.info('Starting domain availability check', {
        domains: request.domains,
        tlds: request.tlds,
        authUserId: this.authUserId ? 'SET' : 'NOT_SET',
        apiKey: this.apiKey ? 'SET' : 'NOT_SET'
      });

      const params = new URLSearchParams();
      params.append('auth-userid', this.authUserId);
      params.append('api-key', this.apiKey);

      // Add domain names - each domain as separate parameter
      request.domains.forEach(domain => {
        params.append('domain-name', domain);
      });

      // Add TLDs - each TLD as separate parameter
      request.tlds.forEach(tld => {
        params.append('tlds', tld);
      });

      const url = '/domains/available.json?' + params.toString();
      logger.info('Making ResellerBiz API request', {
        fullUrl: this.client.defaults.baseURL + url,
        params: params.toString()
      });

      const response: AxiosResponse<DomainAvailabilityResult> = await this.client.get(url);

      logger.info(`Domain availability check successful`, {
        status: response.status,
        domains: request.domains.length,
        tlds: request.tlds.length,
        responseKeys: Object.keys(response.data || {})
      });

      return response.data;
    } catch (error: any) {
      logger.error('Failed to check domain availability:', {
        message: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        responseData: error.response?.data,
        requestUrl: error.config?.url,
        fullUrl: this.client.defaults.baseURL + (error.config?.url || ''),
        requestParams: error.config?.params
      });

      throw new Error(`Failed to check domain availability: ${error.message}`);

    }
  }

  /**
   * Transfer domain
   */
  async transferDomain(request: DomainTransferRequest): Promise<DomainTransferResult> {
    try {
      const params = new URLSearchParams();
      params.append('auth-userid', this.authUserId);
      params.append('api-key', this.apiKey);
      params.append('domain-name', request.domainName);
      params.append('customer-id', request.customerId.toString());
      params.append('reg-contact-id', request.regContactId.toString());
      params.append('admin-contact-id', request.adminContactId.toString());
      params.append('tech-contact-id', request.techContactId.toString());
      params.append('billing-contact-id', request.billingContactId.toString());
      params.append('invoice-option', request.invoiceOption);
      params.append('auto-renew', request.autoRenew.toString());

      // Optional parameters
      if (request.authCode) {
        params.append('auth-code', request.authCode);
      }
      
      if (request.purchasePrivacy !== undefined) {
        params.append('purchase-privacy', request.purchasePrivacy.toString());
      }
      
      if (request.protectPrivacy !== undefined) {
        params.append('protect-privacy', request.protectPrivacy.toString());
      }
      
      if (request.nameServers && request.nameServers.length > 0) {
        request.nameServers.forEach(ns => {
          params.append('ns', ns);
        });
      }
      
      if (request.attributes) {
        let attrIndex = 1;
        Object.entries(request.attributes).forEach(([key, value]) => {
          params.append(`attr-name${attrIndex}`, key);
          params.append(`attr-value${attrIndex}`, value);
          attrIndex++;
        });
      }
      
      if (request.purchasePremiumDns !== undefined) {
        params.append('purchase-premium-dns', request.purchasePremiumDns.toString());
      }

      const response: AxiosResponse<DomainTransferResult> = await this.client.post(
        '/domains/transfer.json',
        params.toString(),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        }
      );

      logger.info(`Initiated domain transfer for ${request.domainName}`);
      
      return response.data;
    } catch (error) {
      logger.error(`Failed to transfer domain ${request.domainName}:`, error);
      throw new Error('Failed to transfer domain');
    }
  }

  /**
   * WHOIS lookup - Note: This is a placeholder implementation
   * The actual WHOIS API endpoint was not found in the documentation
   * This method provides a basic WHOIS lookup using a third-party service
   */
  async whoisLookup(request: WhoisLookupRequest): Promise<WhoisLookupResult> {
    try {
      // For now, we'll use a basic implementation that returns structured data
      // In a real implementation, you might use a third-party WHOIS service
      // or implement your own WHOIS client
      
      logger.info(`Performing WHOIS lookup for ${request.domainName}`);
      
      // This is a placeholder - in production you would implement actual WHOIS lookup
      const result: WhoisLookupResult = {
        domain: request.domainName,
        registrar: 'Unknown',
        registrant: {
          name: 'WHOIS data not available',
        },
        nameServers: [],
        status: ['WHOIS lookup not implemented'],
      };
      
      logger.warn('WHOIS lookup is not fully implemented - returning placeholder data');
      
      return result;
    } catch (error) {
      logger.error(`Failed to perform WHOIS lookup for ${request.domainName}:`, error);
      throw new Error('Failed to perform WHOIS lookup');
    }
  }

  /**
   * Get details of all active products
   */
  async getProductDetails(): Promise<{ success: boolean; totalProducts?: number; products?: ProductDetails; error?: string }> {
    try {
      logger.info('Fetching product details from ResellerBiz API');

      const params = new URLSearchParams();
      params.append('auth-userid', this.authUserId);
      params.append('api-key', this.apiKey);

      const response: AxiosResponse<ProductDetails> = await this.client.get(
        `/products/details.json?${params.toString()}`
      );

      const products = response.data;
      const processedProducts: ProductDetails = {};

      // Process and organize product data
      Object.keys(products).forEach(productKey => {
        const product = products[productKey];
        processedProducts[productKey] = {
          ...product,
          productKey,
          fetchedAt: new Date().toISOString()
        };
      });

      logger.info(`Successfully fetched ${Object.keys(processedProducts).length} products`);

      return {
        success: true,
        totalProducts: Object.keys(processedProducts).length,
        products: processedProducts
      };

    } catch (error: any) {
      logger.error('Failed to fetch product details:', {
        message: error.message,
        status: error.response?.status,
        data: error.response?.data
      });

      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get product category to product keys mapping
   */
  async getCategoryKeysMapping(): Promise<{ success: boolean; totalCategories?: number; mappings?: CategoryMapping; error?: string }> {
    try {
      logger.info('Fetching category-keys mapping from ResellerBiz API');

      const params = new URLSearchParams();
      params.append('auth-userid', this.authUserId);
      params.append('api-key', this.apiKey);

      const response: AxiosResponse<{ [category: string]: string[] }> = await this.client.get(
        `/products/category-keys-mapping.json?${params.toString()}`
      );

      const mappings = response.data;
      const processedMappings: CategoryMapping = {};

      Object.keys(mappings).forEach(category => {
        processedMappings[category] = {
          category,
          productKeys: mappings[category] || [],
          productCount: Array.isArray(mappings[category]) ? mappings[category].length : 0,
          fetchedAt: new Date().toISOString()
        };
      });

      logger.info(`Successfully fetched ${Object.keys(processedMappings).length} categories`);

      return {
        success: true,
        totalCategories: Object.keys(processedMappings).length,
        mappings: processedMappings
      };

    } catch (error: any) {
      logger.error('Failed to fetch category mappings:', {
        message: error.message,
        status: error.response?.status,
        data: error.response?.data
      });

      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get aggregated product data (details + category mappings)
   */
  async getAggregatedProductData(): Promise<AggregatedProductData | { success: false; error: string }> {
    try {
      logger.info('Fetching aggregated product data');

      // Fetch both product details and category mappings concurrently
      const [detailsResult, mappingsResult] = await Promise.all([
        this.getProductDetails(),
        this.getCategoryKeysMapping()
      ]);

      if (!detailsResult.success || !mappingsResult.success) {
        return {
          success: false,
          error: 'Failed to fetch complete product data'
        };
      }

      // Combine and organize the data
      const aggregatedData: AggregatedProductData = {
        success: true,
        fetchedAt: new Date().toISOString(),
        summary: {
          totalProducts: detailsResult.totalProducts || 0,
          totalCategories: mappingsResult.totalCategories || 0
        },
        productsByCategory: {},
        allProducts: detailsResult.products || {},
        categoryMappings: mappingsResult.mappings || {}
      };

      // Organize products by category
      Object.keys(mappingsResult.mappings || {}).forEach(category => {
        const categoryData = mappingsResult.mappings?.[category];
        if (categoryData) {
          aggregatedData.productsByCategory[category] = {
            category,
            productCount: categoryData.productCount,
            products: {}
          };

          // Add product details for each product in this category
          if (Array.isArray(categoryData.productKeys)) {
            categoryData.productKeys.forEach(productKey => {
              if (detailsResult.products?.[productKey]) {
                aggregatedData.productsByCategory[category].products[productKey] =
                  detailsResult.products[productKey];
              }
            });
          }
        }
      });

      logger.info('Successfully aggregated product data', {
        totalProducts: aggregatedData.summary.totalProducts,
        totalCategories: aggregatedData.summary.totalCategories
      });

      return aggregatedData;

    } catch (error: any) {
      logger.error('Failed to get aggregated product data:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get products by specific category
   */
  async getProductsByCategory(categoryName: string): Promise<{ success: boolean; category?: string; productCount?: number; products?: ProductDetails; availableCategories?: string[]; error?: string }> {
    try {
      logger.info(`Fetching products for category: ${categoryName}`);

      const aggregatedData = await this.getAggregatedProductData();

      if (!aggregatedData.success) {
        return {
          success: false,
          error: 'Failed to fetch product data'
        };
      }

      const categoryProducts = aggregatedData.productsByCategory[categoryName];

      if (!categoryProducts) {
        return {
          success: false,
          error: `Category '${categoryName}' not found`,
          availableCategories: Object.keys(aggregatedData.productsByCategory)
        };
      }

      return {
        success: true,
        category: categoryName,
        productCount: categoryProducts.productCount,
        products: categoryProducts.products
      };

    } catch (error: any) {
      logger.error(`Failed to get products for category ${categoryName}:`, error);
      return {
        success: false,
        error: error.message
      };
    }
  }
}

// Lazy singleton instance
let resellerBizServiceInstance: ResellerBizService | null = null;

export function getResellerBizService(): ResellerBizService {
  if (!resellerBizServiceInstance) {
    resellerBizServiceInstance = new ResellerBizService();
  }
  return resellerBizServiceInstance;
}
