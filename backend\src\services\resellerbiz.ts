import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { appConfig } from '../config';
import { logger } from '../utils/logger';

// Domain availability status types
export type DomainAvailabilityStatus = 'available' | 'regthroughus' | 'regthroughothers' | 'unknown';

// Domain availability interfaces
export interface DomainAvailabilityRequest {
  domains: string[];
  tlds: string[];
}

export interface DomainAvailabilityResult {
  [domain: string]: {
    status: DomainAvailabilityStatus;
    classkey?: string;
    ispremiumname?: boolean;
    premiumcost?: number;
    eapfee?: number;
    trademark?: {
      trademark_name: string;
      trademark_number: string;
      trademark_country: string;
      trademark_date: string;
    };
  };
}

// Domain transfer interfaces
export interface DomainTransferRequest {
  domainName: string;
  authCode?: string;
  customerId: number;
  regContactId: number;
  adminContactId: number;
  techContactId: number;
  billingContactId: number;
  invoiceOption: 'NoInvoice' | 'PayInvoice' | 'KeepInvoice' | 'OnlyAdd';
  autoRenew: boolean;
  purchasePrivacy?: boolean;
  protectPrivacy?: boolean;
  nameServers?: string[];
  attributes?: { [key: string]: string };
  purchasePremiumDns?: boolean;
}

export interface DomainTransferResult {
  description: string;
  entityid: string;
  actiontype: string;
  actiontypedesc: string;
  eaqid: string;
  actionstatus: string;
  actionstatusdesc: string;
  invoiceid?: string;
  sellingcurrencysymbol?: string;
  sellingamount?: number;
  customerid: number;
  privacydetails?: any;
}

// WHOIS lookup interfaces
export interface WhoisLookupRequest {
  domainName: string;
}

export interface WhoisLookupResult {
  domain: string;
  registrar: string;
  registrant: {
    name: string;
    organization?: string;
    email?: string;
    phone?: string;
    address?: string;
    city?: string;
    state?: string;
    country?: string;
    postalCode?: string;
  };
  admin?: {
    name: string;
    organization?: string;
    email?: string;
    phone?: string;
  };
  tech?: {
    name: string;
    organization?: string;
    email?: string;
    phone?: string;
  };
  nameServers: string[];
  creationDate?: string;
  expirationDate?: string;
  updatedDate?: string;
  status: string[];
}

export class ResellerBizService {
  private client: AxiosInstance;
  private authUserId: string;
  private apiKey: string;

  constructor() {
    this.authUserId = appConfig.resellerBiz.authUserId;
    this.apiKey = appConfig.resellerBiz.apiKey;

    this.client = axios.create({
      baseURL: 'https://domaincheck.httpapi.com/api',
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Add request/response interceptors for logging
    this.client.interceptors.request.use(
      (config) => {
        logger.debug(`ResellerBiz API Request: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        logger.error('ResellerBiz API Request Error:', error);
        return Promise.reject(error);
      }
    );

    this.client.interceptors.response.use(
      (response) => {
        logger.debug(`ResellerBiz API Response: ${response.status} ${response.config.url}`);
        return response;
      },
      (error) => {
        logger.error('ResellerBiz API Response Error:', {
          status: error.response?.status,
          data: error.response?.data,
          url: error.config?.url,
        });
        return Promise.reject(error);
      }
    );
  }

  /**
   * Check domain availability
   */
  async checkDomainAvailability(request: DomainAvailabilityRequest): Promise<DomainAvailabilityResult> {
    try {
      logger.info('Starting domain availability check', {
        domains: request.domains,
        tlds: request.tlds,
        authUserId: this.authUserId ? 'SET' : 'NOT_SET',
        apiKey: this.apiKey ? 'SET' : 'NOT_SET'
      });

      const params = new URLSearchParams();
      params.append('auth-userid', this.authUserId);
      params.append('api-key', this.apiKey);

      // Add domain names - each domain as separate parameter
      request.domains.forEach(domain => {
        params.append('domain-name', domain);
      });

      // Add TLDs - each TLD as separate parameter
      request.tlds.forEach(tld => {
        params.append('tlds', tld);
      });

      const url = '/domains/available.json?' + params.toString();
      logger.info('Making ResellerBiz API request', {
        fullUrl: this.client.defaults.baseURL + url,
        params: params.toString()
      });

      const response: AxiosResponse<DomainAvailabilityResult> = await this.client.get(url);

      logger.info(`Domain availability check successful`, {
        status: response.status,
        domains: request.domains.length,
        tlds: request.tlds.length,
        responseKeys: Object.keys(response.data || {})
      });

      return response.data;
    } catch (error: any) {
      logger.error('Failed to check domain availability:', {
        message: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        responseData: error.response?.data,
        requestUrl: error.config?.url,
        fullUrl: this.client.defaults.baseURL + (error.config?.url || ''),
        requestParams: error.config?.params
      });

      throw new Error(`Failed to check domain availability: ${error.message}`);

    }
  }

  /**
   * Transfer domain
   */
  async transferDomain(request: DomainTransferRequest): Promise<DomainTransferResult> {
    try {
      const params = new URLSearchParams();
      params.append('auth-userid', this.authUserId);
      params.append('api-key', this.apiKey);
      params.append('domain-name', request.domainName);
      params.append('customer-id', request.customerId.toString());
      params.append('reg-contact-id', request.regContactId.toString());
      params.append('admin-contact-id', request.adminContactId.toString());
      params.append('tech-contact-id', request.techContactId.toString());
      params.append('billing-contact-id', request.billingContactId.toString());
      params.append('invoice-option', request.invoiceOption);
      params.append('auto-renew', request.autoRenew.toString());

      // Optional parameters
      if (request.authCode) {
        params.append('auth-code', request.authCode);
      }
      
      if (request.purchasePrivacy !== undefined) {
        params.append('purchase-privacy', request.purchasePrivacy.toString());
      }
      
      if (request.protectPrivacy !== undefined) {
        params.append('protect-privacy', request.protectPrivacy.toString());
      }
      
      if (request.nameServers && request.nameServers.length > 0) {
        request.nameServers.forEach(ns => {
          params.append('ns', ns);
        });
      }
      
      if (request.attributes) {
        let attrIndex = 1;
        Object.entries(request.attributes).forEach(([key, value]) => {
          params.append(`attr-name${attrIndex}`, key);
          params.append(`attr-value${attrIndex}`, value);
          attrIndex++;
        });
      }
      
      if (request.purchasePremiumDns !== undefined) {
        params.append('purchase-premium-dns', request.purchasePremiumDns.toString());
      }

      const response: AxiosResponse<DomainTransferResult> = await this.client.post(
        '/domains/transfer.json',
        params.toString(),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        }
      );

      logger.info(`Initiated domain transfer for ${request.domainName}`);
      
      return response.data;
    } catch (error) {
      logger.error(`Failed to transfer domain ${request.domainName}:`, error);
      throw new Error('Failed to transfer domain');
    }
  }

  /**
   * WHOIS lookup - Note: This is a placeholder implementation
   * The actual WHOIS API endpoint was not found in the documentation
   * This method provides a basic WHOIS lookup using a third-party service
   */
  async whoisLookup(request: WhoisLookupRequest): Promise<WhoisLookupResult> {
    try {
      // For now, we'll use a basic implementation that returns structured data
      // In a real implementation, you might use a third-party WHOIS service
      // or implement your own WHOIS client
      
      logger.info(`Performing WHOIS lookup for ${request.domainName}`);
      
      // This is a placeholder - in production you would implement actual WHOIS lookup
      const result: WhoisLookupResult = {
        domain: request.domainName,
        registrar: 'Unknown',
        registrant: {
          name: 'WHOIS data not available',
        },
        nameServers: [],
        status: ['WHOIS lookup not implemented'],
      };
      
      logger.warn('WHOIS lookup is not fully implemented - returning placeholder data');
      
      return result;
    } catch (error) {
      logger.error(`Failed to perform WHOIS lookup for ${request.domainName}:`, error);
      throw new Error('Failed to perform WHOIS lookup');
    }
  }
}

// Lazy singleton instance
let resellerBizServiceInstance: ResellerBizService | null = null;

export function getResellerBizService(): ResellerBizService {
  if (!resellerBizServiceInstance) {
    resellerBizServiceInstance = new ResellerBizService();
  }
  return resellerBizServiceInstance;
}
