name: CI

on:
  pull_request:
  push:
    branches: [ master ]

env:
  CI_CHECK_FAIL: ssh2

jobs:
  tests-linux:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        node-version: [10.16.0, 10.x, 12.x, 14.x, 16.x, 18.x, 20.x, 22.x]
    steps:
      - uses: actions/checkout@v4
        with:
          persist-credentials: false
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
      - name: Check Node.js version
        run: node -pe process.versions
      - name: Install Python 2.7
        if: ${{ matrix.node-version == '10.16.0' }}
        run: |
          sudo apt install python2.7
          echo "PYTHON=$(which python2.7)" >> "$GITHUB_ENV"
      - name: Install module
        run: npm install
      - name: Run tests
        run: npm test
  tests-macos:
    runs-on: macos-latest
    strategy:
      fail-fast: false
      matrix:
        node-version: [16.x, 18.x, 20.x, 22.x]
    steps:
      - uses: actions/checkout@v4
        with:
          persist-credentials: false
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
      - name: Install Python 3.10
        uses: actions/setup-python@v5
        with:
          python-version: '3.10'
      - name: Check Node.js version
        run: node -pe process.versions
      - name: Install module
        run: npm install
      - name: Run tests
        run: npm test
  tests-macos-homebrew:
    runs-on: macos-latest
    steps:
      - uses: actions/checkout@v4
        with:
          persist-credentials: false
      - name: Use Node.js (latest)
        run: brew install node
      - name: Install Python 3.10
        uses: actions/setup-python@v5
        with:
          python-version: '3.10'
      - name: Check Node.js version
        run: node -pe process.versions
      - name: Install module
        run: npm install
      - name: Run tests
        run: npm test
  tests-windows:
    runs-on: windows-2019
    strategy:
      fail-fast: false
      matrix:
        node-version: [16.x, 18.x, 20.x, 22.x]
    steps:
      - uses: actions/checkout@v4
        with:
          persist-credentials: false
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
      - name: Check Node.js version
        run: node -pe process.versions
      - name: Install module
        run: npm install
      - name: Run tests
        run: npm test
