"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApplicationService = void 0;
exports.getApplicationService = getApplicationService;
const mongodb_1 = require("mongodb");
const connection_1 = require("../database/connection");
const config_1 = require("../config");
const models_1 = require("../models");
const logger_1 = require("../utils/logger");
const cloudflare_dns_1 = require("./cloudflare-dns");
class ApplicationService {
    applicationsCollection;
    constructor() {
        this.applicationsCollection = connection_1.databaseConnection.getCollection('applications');
    }
    generateSubdomain(applicationName) {
        return applicationName
            .toLowerCase()
            .replace(/[^a-z0-9-]/g, '-')
            .replace(/-+/g, '-')
            .replace(/^-|-$/g, '');
    }
    async generateUniqueSubdomain(applicationName, userId, excludeId) {
        const baseSubdomain = this.generateSubdomain(applicationName);
        if (await this.isSubdomainUnique(baseSubdomain, excludeId)) {
            return baseSubdomain;
        }
        const userIdentifier = userId.substring(0, 6).toLowerCase();
        const subdomainWithUser = `${baseSubdomain}-${userIdentifier}`;
        if (await this.isSubdomainUnique(subdomainWithUser, excludeId)) {
            return subdomainWithUser;
        }
        const timestamp = Date.now().toString().slice(-6);
        const subdomainWithTimestamp = `${baseSubdomain}-${userIdentifier}-${timestamp}`;
        if (await this.isSubdomainUnique(subdomainWithTimestamp, excludeId)) {
            return subdomainWithTimestamp;
        }
        const randomString = Math.random().toString(36).substring(2, 8);
        return `${baseSubdomain}-${userIdentifier}-${randomString}`;
    }
    async isSubdomainUnique(subdomain, excludeId) {
        const query = { subdomain };
        if (excludeId) {
            query._id = { $ne: new mongodb_1.ObjectId(excludeId) };
        }
        const existingApp = await this.applicationsCollection.findOne(query);
        return !existingApp;
    }
    async createApplication(userId, applicationData) {
        try {
            const subdomain = await this.generateUniqueSubdomain(applicationData.name, userId);
            const existingApp = await this.applicationsCollection.findOne({
                user_id: userId,
                name: applicationData.name
            });
            if (existingApp) {
                throw new Error('Application with this name already exists');
            }
            const now = new Date();
            const fullDomain = `${subdomain}.${config_1.appConfig.domain.primary}`;
            const newApplication = {
                user_id: userId,
                name: applicationData.name,
                description: applicationData.description,
                type: applicationData.type,
                status: models_1.ApplicationStatus.DRAFT,
                repository: applicationData.repository,
                environment: applicationData.environment,
                deployment: applicationData.deployment,
                resources: applicationData.resources || {},
                subdomain,
                domain: fullDomain,
                created_at: now,
                updated_at: now,
                stats: {
                    total_deployments: 0,
                    successful_deployments: 0,
                    failed_deployments: 0,
                    last_deployment_status: 'none',
                },
            };
            const result = await this.applicationsCollection.insertOne(newApplication);
            const createdApplication = await this.applicationsCollection.findOne({ _id: result.insertedId });
            if (!createdApplication) {
                throw new Error('Failed to create application');
            }
            logger_1.logger.info(`Application created: ${applicationData.name} for user: ${userId} with subdomain: ${subdomain}.${config_1.appConfig.domain.primary}`);
            return (0, models_1.toApplicationResponse)(createdApplication);
        }
        catch (error) {
            logger_1.logger.error('Create application error:', error);
            throw error;
        }
    }
    async getApplicationById(applicationId, userId) {
        try {
            const query = { _id: new mongodb_1.ObjectId(applicationId) };
            if (userId) {
                query.user_id = userId;
            }
            const application = await this.applicationsCollection.findOne(query);
            if (!application) {
                throw new Error('Application not found');
            }
            return (0, models_1.toApplicationResponse)(application);
        }
        catch (error) {
            logger_1.logger.error('Get application by ID error:', error);
            throw error;
        }
    }
    async getApplicationsByUser(userId, options = {}) {
        try {
            const { page = 1, limit = 10, sort = 'created_at', order = 'desc' } = options;
            const skip = (page - 1) * limit;
            const sortObj = {};
            sortObj[sort] = order === 'asc' ? 1 : -1;
            const applications = await this.applicationsCollection
                .find({ user_id: userId })
                .sort(sortObj)
                .skip(skip)
                .limit(limit)
                .toArray();
            const total = await this.applicationsCollection.countDocuments({ user_id: userId });
            const pages = Math.ceil(total / limit);
            return {
                data: applications.map(models_1.toApplicationResponse),
                pagination: {
                    page,
                    limit,
                    total,
                    pages,
                    has_next: page < pages,
                    has_prev: page > 1,
                },
            };
        }
        catch (error) {
            logger_1.logger.error('Get applications by user error:', error);
            throw error;
        }
    }
    async updateApplication(applicationId, userId, updateData) {
        try {
            const existingApp = await this.applicationsCollection.findOne({
                _id: new mongodb_1.ObjectId(applicationId),
                user_id: userId
            });
            if (!existingApp) {
                throw new Error('Application not found');
            }
            if (updateData.name && updateData.name !== existingApp.name) {
                const nameConflict = await this.applicationsCollection.findOne({
                    user_id: userId,
                    name: updateData.name,
                    _id: { $ne: new mongodb_1.ObjectId(applicationId) }
                });
                if (nameConflict) {
                    throw new Error('Application with this name already exists');
                }
            }
            const updateFields = {
                ...updateData,
                updated_at: new Date(),
            };
            const result = await this.applicationsCollection.updateOne({ _id: new mongodb_1.ObjectId(applicationId), user_id: userId }, { $set: updateFields });
            if (result.matchedCount === 0) {
                throw new Error('Application not found');
            }
            const updatedApplication = await this.applicationsCollection.findOne({
                _id: new mongodb_1.ObjectId(applicationId)
            });
            if (!updatedApplication) {
                throw new Error('Failed to fetch updated application');
            }
            logger_1.logger.info(`Application updated: ${applicationId} for user: ${userId}`);
            return (0, models_1.toApplicationResponse)(updatedApplication);
        }
        catch (error) {
            logger_1.logger.error('Update application error:', error);
            throw error;
        }
    }
    async deleteApplication(applicationId, userId) {
        try {
            const result = await this.applicationsCollection.deleteOne({
                _id: new mongodb_1.ObjectId(applicationId),
                user_id: userId
            });
            if (result.deletedCount === 0) {
                throw new Error('Application not found');
            }
            logger_1.logger.info(`Application deleted: ${applicationId} for user: ${userId}`);
        }
        catch (error) {
            logger_1.logger.error('Delete application error:', error);
            throw error;
        }
    }
    async updateApplicationStatus(applicationId, status, userId) {
        try {
            const query = { _id: new mongodb_1.ObjectId(applicationId) };
            if (userId) {
                query.user_id = userId;
            }
            const result = await this.applicationsCollection.updateOne(query, {
                $set: {
                    status,
                    updated_at: new Date()
                }
            });
            if (result.matchedCount === 0) {
                throw new Error('Application not found');
            }
            const updatedApplication = await this.applicationsCollection.findOne({
                _id: new mongodb_1.ObjectId(applicationId)
            });
            if (!updatedApplication) {
                throw new Error('Failed to fetch updated application');
            }
            logger_1.logger.info(`Application status updated: ${applicationId} to ${status}`);
            return (0, models_1.toApplicationResponse)(updatedApplication);
        }
        catch (error) {
            logger_1.logger.error('Update application status error:', error);
            throw error;
        }
    }
    async getApplicationStats(userId) {
        try {
            const pipeline = [
                { $match: { user_id: userId } },
                {
                    $group: {
                        _id: '$status',
                        count: { $sum: 1 }
                    }
                }
            ];
            const statusCounts = await this.applicationsCollection.aggregate(pipeline).toArray();
            const stats = {
                total: 0,
                draft: 0,
                building: 0,
                deployed: 0,
                failed: 0,
                stopped: 0,
                suspended: 0,
            };
            statusCounts.forEach(item => {
                stats.total += item['count'];
                stats[item['_id']] = item['count'];
            });
            return stats;
        }
        catch (error) {
            logger_1.logger.error('Get application stats error:', error);
            throw error;
        }
    }
    async createApplicationDNS(applicationId, serverIP) {
        try {
            const application = await this.applicationsCollection.findOne({ _id: new mongodb_1.ObjectId(applicationId) });
            if (!application || !application.subdomain) {
                throw new Error('Application not found or missing subdomain');
            }
            logger_1.logger.info(`DNS record creation skipped for development: ${application.domain} -> ${serverIP}`);
        }
        catch (error) {
            logger_1.logger.error(`Failed to create DNS record for application ${applicationId}:`, error);
            throw error;
        }
    }
    async updateApplicationDNS(applicationId, newServerIP) {
        try {
            const application = await this.applicationsCollection.findOne({ _id: new mongodb_1.ObjectId(applicationId) });
            if (!application || !application.domain) {
                throw new Error('Application not found or missing domain');
            }
            logger_1.logger.info(`DNS record update skipped for development: ${application.domain} -> ${newServerIP}`);
            logger_1.logger.info(`Updated DNS record for ${application.domain} -> ${newServerIP}`);
        }
        catch (error) {
            logger_1.logger.error(`Failed to update DNS record for application ${applicationId}:`, error);
            throw error;
        }
    }
    async deleteApplicationDNS(applicationId) {
        try {
            const application = await this.applicationsCollection.findOne({ _id: new mongodb_1.ObjectId(applicationId) });
            if (!application || !application.domain) {
                return;
            }
            const existingRecords = await cloudflare_dns_1.cloudflareDNS.getDNSRecords(application.domain, 'A');
            for (const record of existingRecords) {
                if (record.id) {
                    await cloudflare_dns_1.cloudflareDNS.deleteDNSRecord(record.id);
                }
            }
            logger_1.logger.info(`Deleted DNS records for ${application.domain}`);
        }
        catch (error) {
            logger_1.logger.error(`Failed to delete DNS record for application ${applicationId}:`, error);
            throw error;
        }
    }
    async deployApplication(applicationId, userId) {
        try {
            const application = await this.applicationsCollection.findOne({
                _id: new mongodb_1.ObjectId(applicationId),
                user_id: userId
            });
            if (!application) {
                throw new Error('Application not found');
            }
            await this.applicationsCollection.updateOne({ _id: new mongodb_1.ObjectId(applicationId) }, {
                $set: {
                    status: models_1.ApplicationStatus.BUILDING,
                    updated_at: new Date()
                }
            });
            const defaultServerIP = '*************';
            try {
                await this.createApplicationDNS(applicationId, defaultServerIP);
                await this.applicationsCollection.updateOne({ _id: new mongodb_1.ObjectId(applicationId) }, {
                    $set: {
                        status: models_1.ApplicationStatus.DEPLOYED,
                        updated_at: new Date()
                    }
                });
                logger_1.logger.info(`Successfully deployed application ${applicationId} with DNS record`);
            }
            catch (dnsError) {
                await this.applicationsCollection.updateOne({ _id: new mongodb_1.ObjectId(applicationId) }, {
                    $set: {
                        status: models_1.ApplicationStatus.FAILED,
                        updated_at: new Date()
                    }
                });
                logger_1.logger.error(`DNS creation failed for application ${applicationId}:`, dnsError);
                throw new Error('Deployment failed: Unable to create DNS record');
            }
            const updatedApplication = await this.applicationsCollection.findOne({ _id: new mongodb_1.ObjectId(applicationId) });
            return updatedApplication;
        }
        catch (error) {
            logger_1.logger.error(`Failed to deploy application ${applicationId}:`, error);
            throw error;
        }
    }
}
exports.ApplicationService = ApplicationService;
let applicationServiceInstance = null;
function getApplicationService() {
    if (!applicationServiceInstance) {
        applicationServiceInstance = new ApplicationService();
    }
    return applicationServiceInstance;
}
//# sourceMappingURL=application.js.map