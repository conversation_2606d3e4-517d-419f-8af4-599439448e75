{"name": "@types/ssh2", "version": "1.15.5", "description": "TypeScript definitions for ssh2", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/ssh2", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "githubUsername": "tkQubo", "url": "https://github.com/tkQubo"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/rbuckton"}, {"name": "<PERSON>", "githubUsername": "wrb<PERSON>ce", "url": "https://github.com/wrboyce"}, {"name": "<PERSON>", "githubUsername": "hengkx", "url": "https://github.com/hengkx"}, {"name": "<PERSON>", "githubUsername": "bragle", "url": "https://github.com/bragle"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/Lucian<PERSON>zo"}, {"name": "<PERSON>", "githubUsername": "d<PERSON><PERSON>", "url": "https://github.com/dhensby"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/ssh2"}, "scripts": {}, "dependencies": {"@types/node": "^18.11.18"}, "peerDependencies": {}, "typesPublisherContentHash": "a6738bdd9c3241b02a134380f84144e4446c67033342e921aad15a5bab5dcd44", "typeScriptVersion": "5.1"}