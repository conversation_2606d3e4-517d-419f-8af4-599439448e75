"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createApp = createApp;
exports.startServer = startServer;
const fastify_1 = __importDefault(require("fastify"));
const config_1 = require("./config");
const logger_1 = require("./utils/logger");
const connection_1 = require("./database/connection");
const metrics_collector_1 = require("./services/metrics-collector");
const cors_1 = require("./middleware/cors");
const rate_limit_1 = require("./middleware/rate-limit");
const error_1 = require("./middleware/error");
const routes_1 = require("./routes");
async function createApp() {
    const fastify = (0, fastify_1.default)({
        logger: logger_1.logger,
        requestIdLogLabel: 'request_id',
        requestIdHeader: 'x-request-id',
        genReqId: () => {
            return Math.random().toString(36).substring(2, 15) +
                Math.random().toString(36).substring(2, 15);
        },
    });
    try {
        await connection_1.databaseConnection.connect();
        logger_1.logger.info('Database connected successfully');
        await (0, cors_1.registerCors)(fastify);
        await (0, rate_limit_1.registerRateLimit)(fastify);
        (0, error_1.registerErrorHandler)(fastify);
        fastify.addHook('onRequest', async (request, _reply) => {
            logger_1.logger.info({
                method: request.method,
                url: request.url,
                ip: request.ip,
                userAgent: request.headers['user-agent'],
                requestId: request.id,
            }, 'Incoming request');
        });
        fastify.addHook('onResponse', async (request, reply) => {
            logger_1.logger.info({
                method: request.method,
                url: request.url,
                statusCode: reply.statusCode,
                responseTime: reply.elapsedTime,
                requestId: request.id,
            }, 'Request completed');
        });
        await (0, routes_1.registerRoutes)(fastify);
        logger_1.logger.info('Fastify application created successfully');
        return fastify;
    }
    catch (error) {
        logger_1.logger.error('Failed to create Fastify application:', error);
        throw error;
    }
}
async function startServer() {
    try {
        const app = await createApp();
        await app.listen({
            port: config_1.appConfig.server.port,
            host: config_1.appConfig.server.host,
        });
        logger_1.logger.info(`Server started on ${config_1.appConfig.server.host}:${config_1.appConfig.server.port}`);
        logger_1.logger.info(`Environment: ${config_1.appConfig.server.env}`);
        logger_1.logger.info(`API Documentation: http://${config_1.appConfig.server.host}:${config_1.appConfig.server.port}/health`);
        if (config_1.appConfig.monitoring.enabled) {
            logger_1.logger.info('Hourly metrics collection enabled');
        }
        logger_1.logger.info('🚀 Server successfully started and ready to accept connections!');
        const gracefulShutdown = async (signal) => {
            logger_1.logger.info(`Received ${signal}, shutting down gracefully`);
            try {
                if (config_1.appConfig.monitoring.enabled) {
                    metrics_collector_1.metricsCollector.stopCollection();
                }
                await app.close();
                await connection_1.databaseConnection.disconnect();
                logger_1.logger.info('Server shut down successfully');
                process.exit(0);
            }
            catch (error) {
                logger_1.logger.error('Error during shutdown:', error);
                process.exit(1);
            }
        };
        process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
        process.on('SIGINT', () => gracefulShutdown('SIGINT'));
    }
    catch (error) {
        logger_1.logger.error('Failed to start server:', error);
        process.exit(1);
    }
}
if (require.main === module) {
    startServer();
}
//# sourceMappingURL=app.js.map