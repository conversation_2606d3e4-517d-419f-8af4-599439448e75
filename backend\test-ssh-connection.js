#!/usr/bin/env node

/**
 * SSH Connection Test Script
 * 
 * This script tests the SSH connection to your shared hosting server
 * to verify that the credentials in .env are working correctly.
 */

const { Client } = require('ssh2');
require('dotenv').config({ path: '.env' });

// SSH Configuration from environment variables
const sshConfig = {
  host: process.env.SHARED_SERVER_IP || '*************',
  port: parseInt(process.env.SHARED_SERVER_SSH_PORT || '22'),
  username: process.env.SHARED_SERVER_SSH_USER || 'root',
  password: process.env.SHARED_SERVER_SSH_PASSWORD
};

console.log('🔧 SSH Connection Test Script');
console.log('==============================');
console.log(`Host: ${sshConfig.host}`);
console.log(`Port: ${sshConfig.port}`);
console.log(`Username: ${sshConfig.username}`);
console.log(`Password: ${sshConfig.password ? '***' + sshConfig.password.slice(-3) : 'NOT SET'}`);
console.log('');

// Validate configuration
if (!sshConfig.password) {
  console.error('❌ ERROR: SHARED_SERVER_SSH_PASSWORD is not set in .env file');
  console.log('Please set your root password in backend/.env:');
  console.log('SHARED_SERVER_SSH_PASSWORD=your_actual_root_password');
  process.exit(1);
}

const conn = new Client();

console.log('🔌 Attempting SSH connection...');

conn.on('ready', async () => {
  console.log('✅ SSH connection established successfully!');
  console.log('');
  
  try {
    // Test basic commands
    console.log('🧪 Running test commands...');
    
    // Test 1: Check server info
    await executeCommand(conn, 'uname -a', 'Server Information');
    
    // Test 2: Check disk space
    await executeCommand(conn, 'df -h /', 'Disk Space');
    
    // Test 3: Check memory
    await executeCommand(conn, 'free -h', 'Memory Usage');
    
    // Test 4: Check if we can create users (test permissions)
    await executeCommand(conn, 'id', 'Current User');
    
    // Test 5: Check existing users in /var/www (if any)
    await executeCommand(conn, 'ls -la /var/www/ 2>/dev/null || echo "Directory /var/www does not exist"', 'Existing Web Users');
    
    // Test 6: Check if systemd is available
    await executeCommand(conn, 'systemctl --version | head -1', 'Systemd Version');
    
    console.log('');
    console.log('🎉 All tests completed successfully!');
    console.log('✅ Your SSH connection is working properly.');
    console.log('✅ You can now create shared hosting users.');
    
  } catch (error) {
    console.error('❌ Error during testing:', error.message);
  } finally {
    conn.end();
  }
});

conn.on('error', (err) => {
  console.error('❌ SSH connection failed:', err.message);
  console.log('');
  console.log('🔧 Troubleshooting steps:');
  console.log('1. Verify the server IP address is correct');
  console.log('2. Check that SSH is running on the server (port 22)');
  console.log('3. Verify the root password is correct');
  console.log('4. Ensure the server allows root SSH login');
  console.log('5. Check if there are any firewall restrictions');
  process.exit(1);
});

conn.on('end', () => {
  console.log('🔌 SSH connection closed');
});

// Helper function to execute commands and display results
function executeCommand(connection, command, description) {
  return new Promise((resolve, reject) => {
    connection.exec(command, (err, stream) => {
      if (err) {
        reject(err);
        return;
      }

      let stdout = '';
      let stderr = '';

      stream.on('close', (code) => {
        console.log(`📋 ${description}:`);
        if (stdout.trim()) {
          console.log(stdout.trim());
        }
        if (stderr.trim()) {
          console.log(`   Error: ${stderr.trim()}`);
        }
        console.log('');
        resolve();
      });

      stream.on('data', (data) => {
        stdout += data.toString();
      });

      stream.stderr.on('data', (data) => {
        stderr += data.toString();
      });
    });
  });
}

// Connect to the server
conn.connect(sshConfig);

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n🛑 Connection test interrupted');
  conn.end();
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Connection test terminated');
  conn.end();
  process.exit(0);
});
