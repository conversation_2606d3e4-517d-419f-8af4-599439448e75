import { FastifyRequest, FastifyReply } from 'fastify';
import * as jwt from 'jsonwebtoken';
import { appConfig } from '../config';
import { ResponseHelper } from '../utils/response';
import { JwtPayload, UserRole } from '../models';
import { logger } from '../utils/logger';

// Extend FastifyRequest to include user
declare module 'fastify' {
  interface FastifyRequest {
    user?: JwtPayload;
  }
}

// Authentication middleware
export async function authMiddleware(
  request: FastifyRequest,
  reply: FastifyReply
): Promise<void> {
  try {
    const authHeader = request.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return ResponseHelper.unauthorized(reply, 'Authorization token required');
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix
    
    try {
      const decoded = jwt.verify(token, appConfig.auth.jwtSecret) as JwtPayload;
      request.user = decoded;
    } catch (jwtError) {
      logger.warn('Invalid JWT token:', jwtError);
      return ResponseHelper.unauthorized(reply, 'Invalid or expired token');
    }
  } catch (error) {
    logger.error('Auth middleware error:', error);
    return ResponseHelper.internalError(reply, 'Authentication error');
  }
}

// Admin role middleware
export async function adminMiddleware(
  request: FastifyRequest,
  reply: FastifyReply
): Promise<void> {
  if (!request.user) {
    return ResponseHelper.unauthorized(reply, 'Authentication required');
  }

  if (request.user.role !== UserRole.ADMIN && request.user.role !== UserRole.SUPER_ADMIN) {
    return ResponseHelper.forbidden(reply, 'Admin access required');
  }
}

// Super admin role middleware
export async function superAdminMiddleware(
  request: FastifyRequest,
  reply: FastifyReply
): Promise<void> {
  if (!request.user) {
    return ResponseHelper.unauthorized(reply, 'Authentication required');
  }

  if (request.user.role !== UserRole.SUPER_ADMIN) {
    return ResponseHelper.forbidden(reply, 'Super admin access required');
  }
}

// Optional auth middleware (doesn't fail if no token)
export async function optionalAuthMiddleware(
  request: FastifyRequest,
  _reply: FastifyReply
): Promise<void> {
  try {
    const authHeader = request.headers.authorization;
    
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7);
      
      try {
        const decoded = jwt.verify(token, appConfig.auth.jwtSecret) as JwtPayload;
        request.user = decoded;
      } catch (jwtError) {
        // Silently ignore invalid tokens for optional auth
        logger.debug('Optional auth - invalid token:', jwtError);
      }
    }
  } catch (error) {
    logger.error('Optional auth middleware error:', error);
    // Don't fail the request for optional auth errors
  }
}

// User ownership middleware (checks if user owns the resource)
export function createOwnershipMiddleware(userIdParam: string = 'userId') {
  return async function ownershipMiddleware(
    request: FastifyRequest,
    reply: FastifyReply
  ): Promise<void> {
    if (!request.user) {
      return ResponseHelper.unauthorized(reply, 'Authentication required');
    }

    const params = request.params as Record<string, string>;
    const resourceUserId = params[userIdParam];

    // Super admins can access any resource
    if (request.user.role === UserRole.SUPER_ADMIN) {
      return;
    }

    // Regular users can only access their own resources
    if (request.user.sub !== resourceUserId) {
      return ResponseHelper.forbidden(reply, 'Access denied to this resource');
    }
  };
}

// Rate limiting by user
export function createUserRateLimitMiddleware(maxRequests: number = 100, windowMs: number = 60000) {
  const userRequests = new Map<string, { count: number; resetTime: number }>();

  return async function userRateLimitMiddleware(
    request: FastifyRequest,
    reply: FastifyReply
  ): Promise<void> {
    if (!request.user) {
      return; // Skip rate limiting for unauthenticated requests
    }

    const userId = request.user.sub;
    const now = Date.now();
    const userLimit = userRequests.get(userId);

    if (!userLimit || now > userLimit.resetTime) {
      // Reset or initialize user limit
      userRequests.set(userId, {
        count: 1,
        resetTime: now + windowMs,
      });
      return;
    }

    if (userLimit.count >= maxRequests) {
      return ResponseHelper.error(
        reply,
        'RATE_LIMIT_EXCEEDED',
        'Too many requests. Please try again later.',
        429
      );
    }

    userLimit.count++;
  };
}
