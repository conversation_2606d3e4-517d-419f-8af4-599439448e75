{"version": 3, "file": "nginx-manager.js", "sourceRoot": "", "sources": ["../../src/services/nginx-manager.ts"], "names": [], "mappings": ";;;AAAA,4CAAyC;AACzC,sCAAsC;AACtC,6DAAuF;AACvF,qDAAiD;AAEjD,MAAa,mBAAmB;IACtB,mBAAmB,GAAG,4BAA4B,CAAC;IACnD,iBAAiB,GAAG,0BAA0B,CAAC;IAE/C,UAAU,GAAG,wBAAwB,CAAC;IAG9C,KAAK,CAAC,iBAAiB,CAAC,MAAc,EAAE,aAAqB,EAAE,MAO9D;QACC,MAAM,GAAG,GAAG,IAAI,8BAAa,EAAE,CAAC;QAEhC,IAAI,CAAC;YACH,MAAM,GAAG,CAAC,OAAO,EAAE,CAAC;YAEpB,MAAM,IAAI,GAAG,MAAM,kCAAiB,CAAC,OAAO,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC;YAClE,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;YACpC,CAAC;YAED,MAAM,WAAW,GAAG,MAAM,yCAAwB,CAAC,OAAO,CAAC;gBACzD,cAAc,EAAE,aAAa;gBAC7B,OAAO,EAAE,MAAM;aAChB,CAAC,CAAC;YACH,IAAI,CAAC,WAAW,EAAE,CAAC;gBACjB,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;YAC3C,CAAC;YAGD,MAAM,WAAW,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC;YAGxE,MAAM,cAAc,GAAG,GAAG,MAAM,CAAC,SAAS,IAAI,kBAAS,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;YACzE,MAAM,UAAU,GAAG,GAAG,IAAI,CAAC,mBAAmB,IAAI,cAAc,EAAE,CAAC;YAGnE,MAAM,GAAG,CAAC,cAAc,CAAC,SAAS,UAAU,cAAc,WAAW,OAAO,CAAC,CAAC;YAG9E,MAAM,WAAW,GAAG,GAAG,IAAI,CAAC,iBAAiB,IAAI,cAAc,EAAE,CAAC;YAClE,MAAM,GAAG,CAAC,cAAc,CAAC,UAAU,UAAU,IAAI,WAAW,EAAE,CAAC,CAAC;YAGhE,MAAM,UAAU,GAAG,MAAM,GAAG,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;YAC7D,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAClC,MAAM,IAAI,KAAK,CAAC,oCAAoC,UAAU,EAAE,CAAC,CAAC;YACpE,CAAC;YAGD,MAAM,GAAG,CAAC,cAAc,CAAC,wBAAwB,CAAC,CAAC;YAGnD,MAAM,yCAAwB,CAAC,gBAAgB,CAC7C,EAAE,cAAc,EAAE,aAAa,EAAE,EACjC;gBACE,SAAS,EAAE,GAAG,MAAM,CAAC,SAAS,IAAI,kBAAS,CAAC,MAAM,CAAC,OAAO,EAAE;gBAC5D,MAAM,EAAE,MAAM,CAAC,MAAM;gBACrB,WAAW,EAAE,MAAM,CAAC,UAAU,IAAI,KAAK;gBACvC,iBAAiB,EAAE,UAAU;aAC9B,CACF,CAAC;YAEF,eAAM,CAAC,IAAI,CAAC,4BAA4B,MAAM,CAAC,SAAS,IAAI,kBAAS,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC;QAE1F,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YACtD,MAAM,KAAK,CAAC;QACd,CAAC;gBAAS,CAAC;YACT,MAAM,GAAG,CAAC,UAAU,EAAE,CAAC;QACzB,CAAC;IACH,CAAC;IAGD,KAAK,CAAC,iBAAiB,CAAC,MAAc,EAAE,aAAqB;QAC3D,MAAM,GAAG,GAAG,IAAI,8BAAa,EAAE,CAAC;QAEhC,IAAI,CAAC;YACH,MAAM,GAAG,CAAC,OAAO,EAAE,CAAC;YAEpB,MAAM,WAAW,GAAG,MAAM,yCAAwB,CAAC,OAAO,CAAC;gBACzD,cAAc,EAAE,aAAa;gBAC7B,OAAO,EAAE,MAAM;aAChB,CAAC,CAAC;YAEH,IAAI,CAAC,WAAW,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,CAAC;gBAC3C,eAAM,CAAC,IAAI,CAAC,yCAAyC,aAAa,EAAE,CAAC,CAAC;gBACtE,OAAO;YACT,CAAC;YAED,MAAM,cAAc,GAAG,WAAW,CAAC,SAAS,CAAC;YAC7C,MAAM,UAAU,GAAG,GAAG,IAAI,CAAC,mBAAmB,IAAI,cAAc,EAAE,CAAC;YACnE,MAAM,WAAW,GAAG,GAAG,IAAI,CAAC,iBAAiB,IAAI,cAAc,EAAE,CAAC;YAGlE,MAAM,GAAG,CAAC,cAAc,CAAC,SAAS,WAAW,IAAI,UAAU,EAAE,CAAC,CAAC;YAG/D,MAAM,GAAG,CAAC,cAAc,CAAC,oCAAoC,CAAC,CAAC;YAG/D,MAAM,yCAAwB,CAAC,gBAAgB,CAC7C,EAAE,cAAc,EAAE,aAAa,EAAE,EACjC;gBACE,MAAM,EAAE;oBACN,SAAS,EAAE,CAAC;oBACZ,MAAM,EAAE,CAAC;oBACT,iBAAiB,EAAE,CAAC;iBACrB;aACF,CACF,CAAC;YAEF,eAAM,CAAC,IAAI,CAAC,4BAA4B,cAAc,EAAE,CAAC,CAAC;QAE5D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YACtD,MAAM,KAAK,CAAC;QACd,CAAC;gBAAS,CAAC;YACT,MAAM,GAAG,CAAC,UAAU,EAAE,CAAC;QACzB,CAAC;IACH,CAAC;IAGO,mBAAmB,CAAC,IAAS,EAAE,WAAgB,EAAE,MAAW;QAClE,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;YAChC,GAAG,MAAM,CAAC,SAAS,IAAI,kBAAS,CAAC,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;YACpE,GAAG,MAAM,CAAC,SAAS,IAAI,kBAAS,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;QAEpD,MAAM,YAAY,GAAG,GAAG,IAAI,CAAC,cAAc,SAAS,WAAW,CAAC,IAAI,IAAI,WAAW,CAAC,IAAI,EAAE,CAAC;QAE3F,IAAI,MAAM,CAAC,IAAI,KAAK,gBAAgB,EAAE,CAAC;YACrC,OAAO,IAAI,CAAC,wBAAwB,CAAC,UAAU,EAAE,YAAY,EAAE,MAAM,CAAC,CAAC;QACzE,CAAC;aAAM,CAAC;YACN,OAAO,IAAI,CAAC,wBAAwB,CAAC,UAAU,EAAE,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QACxE,CAAC;IACH,CAAC;IAGO,wBAAwB,CAAC,UAAkB,EAAE,YAAoB,EAAE,MAAW;QACpF,MAAM,SAAS,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC9E,MAAM,cAAc,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,2BAA2B,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAE7F,OAAO;EACT,cAAc;;;MAGV,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,uBAAuB,CAAC,CAAC,CAAC,YAAY;kBAC9C,UAAU;;WAEjB,YAAY;;;MAGjB,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gCAgDiB,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;+BACzB,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;;MAEjD,MAAM,CAAC,YAAY,IAAI,EAAE;EAC7B,CAAC;IACD,CAAC;IAGO,wBAAwB,CAAC,UAAkB,EAAE,IAAY,EAAE,MAAW;QAC5E,MAAM,SAAS,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC9E,MAAM,cAAc,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,2BAA2B,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAE7F,OAAO;EACT,cAAc;;WAEL,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;uBAChC,IAAI;;;;MAIrB,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,uBAAuB,CAAC,CAAC,CAAC,YAAY;kBAC9C,UAAU;;MAEtB,SAAS;;;;;;;;;;4BAUa,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gCA8BxC,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;+BACzB,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;;MAEjD,MAAM,CAAC,YAAY,IAAI,EAAE;EAC7B,CAAC;IACD,CAAC;IAGO,iBAAiB,CAAC,UAAkB;QAC1C,MAAM,QAAQ,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAE1C,OAAO;sBACW,IAAI,CAAC,UAAU,IAAI,QAAQ;0BACvB,IAAI,CAAC,UAAU,IAAI,QAAQ;;;;;;;;;;oEAUe,CAAC;IACnE,CAAC;IAGO,2BAA2B,CAAC,UAAkB;QACpD,OAAO;;;kBAGO,UAAU;;EAE1B,CAAC;IACD,CAAC;IAGD,KAAK,CAAC,iBAAiB,CAAC,MAAc,EAAE,aAAqB,EAAE,OAAY;QAEzE,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;QAGpD,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;IAC/D,CAAC;IAGD,KAAK,CAAC,cAAc;QAClB,MAAM,GAAG,GAAG,IAAI,8BAAa,EAAE,CAAC;QAEhC,IAAI,CAAC;YACH,MAAM,GAAG,CAAC,OAAO,EAAE,CAAC;YAGpB,MAAM,MAAM,GAAG,MAAM,GAAG,CAAC,cAAc,CAAC,2BAA2B,CAAC,CAAC;YAGrE,MAAM,UAAU,GAAG,MAAM,GAAG,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;YAG7D,MAAM,YAAY,GAAG,MAAM,GAAG,CAAC,cAAc,CAAC,SAAS,IAAI,CAAC,iBAAiB,UAAU,CAAC,CAAC;YAGzF,MAAM,cAAc,GAAG,MAAM,GAAG,CAAC,cAAc,CAAC,SAAS,IAAI,CAAC,mBAAmB,UAAU,CAAC,CAAC;YAE7F,OAAO;gBACL,MAAM,EAAE,MAAM,CAAC,IAAI,EAAE;gBACrB,WAAW,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC;gBAC3C,UAAU,EAAE,UAAU,CAAC,IAAI,EAAE;gBAC7B,YAAY,EAAE,QAAQ,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;gBAC3C,cAAc,EAAE,QAAQ,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC;gBAC/C,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACnD,MAAM,KAAK,CAAC;QACd,CAAC;gBAAS,CAAC;YACT,MAAM,GAAG,CAAC,UAAU,EAAE,CAAC;QACzB,CAAC;IACH,CAAC;IAGD,KAAK,CAAC,WAAW;QACf,MAAM,GAAG,GAAG,IAAI,8BAAa,EAAE,CAAC;QAEhC,IAAI,CAAC;YACH,MAAM,GAAG,CAAC,OAAO,EAAE,CAAC;YAGpB,MAAM,UAAU,GAAG,MAAM,GAAG,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;YAC7D,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAClC,MAAM,IAAI,KAAK,CAAC,oCAAoC,UAAU,EAAE,CAAC,CAAC;YACpE,CAAC;YAGD,MAAM,GAAG,CAAC,cAAc,CAAC,wBAAwB,CAAC,CAAC;YAEnD,eAAM,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;QAE3D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;YAC/C,MAAM,KAAK,CAAC;QACd,CAAC;gBAAS,CAAC;YACT,MAAM,GAAG,CAAC,UAAU,EAAE,CAAC;QACzB,CAAC;IACH,CAAC;CACF;AArXD,kDAqXC;AAGY,QAAA,YAAY,GAAG,IAAI,mBAAmB,EAAE,CAAC"}