import { FastifyRequest, FastifyReply } from 'fastify';
import { 
  CreateApplicationRequest, 
  UpdateApplicationRequest,
  createApplicationSchema,
  updateApplicationSchema,
  PaginationOptions
} from '../models';
import { getApplicationService } from '../services/application';
import { ResponseHelper } from '../utils/response';
import { logger } from '../utils/logger';

// Create application controller
export async function createApplicationController(
  request: FastifyRequest<{ Body: CreateApplicationRequest }>,
  reply: FastifyReply
): Promise<FastifyReply> {
  try {
    if (!request.user) {
      return ResponseHelper.unauthorized(reply, 'Authentication required');
    }

    // Validate request body
    const validationResult = createApplicationSchema.safeParse(request.body);
    
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map(err => ({
        field: err.path.join('.'),
        message: err.message,
      }));
      
      return ResponseHelper.validationError(
        reply,
        'Validation failed',
        { errors }
      );
    }

    const applicationData = validationResult.data;

    // Create application
    const application = await getApplicationService().createApplication(request.user.sub, applicationData);

    logger.info(`Application created: ${application.name} by user: ${request.user.email}`);

    return ResponseHelper.success(reply, application, 201);
  } catch (error) {
    logger.error('Create application controller error:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('already exists')) {
        return ResponseHelper.error(reply, 'CONFLICT', error.message, 409);
      }
    }

    return ResponseHelper.internalError(reply, 'Failed to create application');
  }
}

// Get applications controller
export async function getApplicationsController(
  request: FastifyRequest<{ 
    Querystring: { 
      page?: string; 
      limit?: string; 
      sort?: string; 
      order?: 'asc' | 'desc' 
    } 
  }>,
  reply: FastifyReply
): Promise<FastifyReply> {
  try {
    if (!request.user) {
      return ResponseHelper.unauthorized(reply, 'Authentication required');
    }

    // Parse pagination options
    const options: PaginationOptions = {
      page: request.query.page ? parseInt(request.query.page, 10) : 1,
      limit: request.query.limit ? parseInt(request.query.limit, 10) : 10,
      sort: request.query.sort || 'created_at',
      order: request.query.order || 'desc',
    };

    // Validate pagination parameters
    if (options.page! < 1) options.page = 1;
    if (options.limit! < 1 || options.limit! > 100) options.limit = 10;

    // Get applications
    const applications = await getApplicationService().getApplicationsByUser(request.user.sub, options);

    return ResponseHelper.success(reply, applications);
  } catch (error) {
    logger.error('Get applications controller error:', error);
    return ResponseHelper.internalError(reply, 'Failed to get applications');
  }
}

// Get application by ID controller
export async function getApplicationController(
  request: FastifyRequest<{ Params: { id: string } }>,
  reply: FastifyReply
): Promise<FastifyReply> {
  try {
    if (!request.user) {
      return ResponseHelper.unauthorized(reply, 'Authentication required');
    }

    const { id } = request.params;

    // Validate ObjectId format
    if (!id.match(/^[0-9a-fA-F]{24}$/)) {
      return ResponseHelper.validationError(reply, 'Invalid application ID format');
    }

    // Get application
    const application = await getApplicationService().getApplicationById(id, request.user.sub);

    return ResponseHelper.success(reply, application);
  } catch (error) {
    logger.error('Get application controller error:', error);
    
    if (error instanceof Error && error.message.includes('not found')) {
      return ResponseHelper.notFound(reply, 'Application not found');
    }

    return ResponseHelper.internalError(reply, 'Failed to get application');
  }
}

// Update application controller
export async function updateApplicationController(
  request: FastifyRequest<{ 
    Params: { id: string }; 
    Body: UpdateApplicationRequest 
  }>,
  reply: FastifyReply
): Promise<FastifyReply> {
  try {
    if (!request.user) {
      return ResponseHelper.unauthorized(reply, 'Authentication required');
    }

    const { id } = request.params;

    // Validate ObjectId format
    if (!id.match(/^[0-9a-fA-F]{24}$/)) {
      return ResponseHelper.validationError(reply, 'Invalid application ID format');
    }

    // Validate request body
    const validationResult = updateApplicationSchema.safeParse(request.body);
    
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map(err => ({
        field: err.path.join('.'),
        message: err.message,
      }));
      
      return ResponseHelper.validationError(
        reply,
        'Validation failed',
        { errors }
      );
    }

    const updateData = validationResult.data;

    // Update application
    const application = await getApplicationService().updateApplication(id, request.user.sub, updateData);

    logger.info(`Application updated: ${id} by user: ${request.user.email}`);

    return ResponseHelper.success(reply, application);
  } catch (error) {
    logger.error('Update application controller error:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('not found')) {
        return ResponseHelper.notFound(reply, 'Application not found');
      }
      if (error.message.includes('already exists')) {
        return ResponseHelper.error(reply, 'CONFLICT', error.message, 409);
      }
    }

    return ResponseHelper.internalError(reply, 'Failed to update application');
  }
}

// Delete application controller
export async function deleteApplicationController(
  request: FastifyRequest<{ Params: { id: string } }>,
  reply: FastifyReply
): Promise<FastifyReply> {
  try {
    if (!request.user) {
      return ResponseHelper.unauthorized(reply, 'Authentication required');
    }

    const { id } = request.params;

    // Validate ObjectId format
    if (!id.match(/^[0-9a-fA-F]{24}$/)) {
      return ResponseHelper.validationError(reply, 'Invalid application ID format');
    }

    // Delete application
    await getApplicationService().deleteApplication(id, request.user.sub);

    logger.info(`Application deleted: ${id} by user: ${request.user.email}`);

    return ResponseHelper.success(reply, { message: 'Application deleted successfully' });
  } catch (error) {
    logger.error('Delete application controller error:', error);
    
    if (error instanceof Error && error.message.includes('not found')) {
      return ResponseHelper.notFound(reply, 'Application not found');
    }

    return ResponseHelper.internalError(reply, 'Failed to delete application');
  }
}

// Get application statistics controller
export async function getApplicationStatsController(
  request: FastifyRequest,
  reply: FastifyReply
): Promise<FastifyReply> {
  try {
    if (!request.user) {
      return ResponseHelper.unauthorized(reply, 'Authentication required');
    }

    // Get application statistics
    const stats = await getApplicationService().getApplicationStats(request.user.sub);

    return ResponseHelper.success(reply, stats);
  } catch (error) {
    logger.error('Get application stats controller error:', error);
    return ResponseHelper.internalError(reply, 'Failed to get application statistics');
  }
}

// Deploy application controller
export async function deployApplicationController(
  request: FastifyRequest<{ Params: { id: string } }>,
  reply: FastifyReply
): Promise<FastifyReply> {
  try {
    if (!request.user) {
      return ResponseHelper.unauthorized(reply, 'Authentication required');
    }

    const { id } = request.params;

    // Validate ObjectId format
    if (!id.match(/^[0-9a-fA-F]{24}$/)) {
      return ResponseHelper.validationError(reply, 'Invalid application ID format');
    }

    // Start deployment process
    const application = await getApplicationService().deployApplication(
      id,
      request.user.sub
    );

    logger.info(`Application deployment started: ${id} by user: ${request.user.email}`);

    return ResponseHelper.success(reply, {
      message: 'Deployment started successfully',
      application
    });
  } catch (error) {
    logger.error('Deploy application controller error:', error);
    
    if (error instanceof Error && error.message.includes('not found')) {
      return ResponseHelper.notFound(reply, 'Application not found');
    }

    return ResponseHelper.internalError(reply, 'Failed to start deployment');
  }
}

// Stop application controller
export async function stopApplicationController(
  request: FastifyRequest<{ Params: { id: string } }>,
  reply: FastifyReply
): Promise<FastifyReply> {
  try {
    if (!request.user) {
      return ResponseHelper.unauthorized(reply, 'Authentication required');
    }

    const { id } = request.params;

    // Validate ObjectId format
    if (!id.match(/^[0-9a-fA-F]{24}$/)) {
      return ResponseHelper.validationError(reply, 'Invalid application ID format');
    }

    // TODO: Implement actual stop logic
    // For now, just update status to stopped
    const application = await getApplicationService().updateApplicationStatus(
      id,
      'stopped' as any,
      request.user.sub
    );

    logger.info(`Application stopped: ${id} by user: ${request.user.email}`);

    return ResponseHelper.success(reply, {
      message: 'Application stopped',
      application
    });
  } catch (error) {
    logger.error('Stop application controller error:', error);
    
    if (error instanceof Error && error.message.includes('not found')) {
      return ResponseHelper.notFound(reply, 'Application not found');
    }

    return ResponseHelper.internalError(reply, 'Failed to stop application');
  }
}
