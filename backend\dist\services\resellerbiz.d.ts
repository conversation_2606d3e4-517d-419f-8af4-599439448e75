export type DomainAvailabilityStatus = 'available' | 'regthroughus' | 'regthroughothers' | 'unknown';
export interface DomainAvailabilityRequest {
    domains: string[];
    tlds: string[];
}
export interface DomainAvailabilityResult {
    [domain: string]: {
        status: DomainAvailabilityStatus;
        classkey?: string;
        ispremiumname?: boolean;
        premiumcost?: number;
        eapfee?: number;
        trademark?: {
            trademark_name: string;
            trademark_number: string;
            trademark_country: string;
            trademark_date: string;
        };
    };
}
export interface DomainTransferRequest {
    domainName: string;
    authCode?: string;
    customerId: number;
    regContactId: number;
    adminContactId: number;
    techContactId: number;
    billingContactId: number;
    invoiceOption: 'NoInvoice' | 'PayInvoice' | 'KeepInvoice' | 'OnlyAdd';
    autoRenew: boolean;
    purchasePrivacy?: boolean;
    protectPrivacy?: boolean;
    nameServers?: string[];
    attributes?: {
        [key: string]: string;
    };
    purchasePremiumDns?: boolean;
}
export interface DomainTransferResult {
    description: string;
    entityid: string;
    actiontype: string;
    actiontypedesc: string;
    eaqid: string;
    actionstatus: string;
    actionstatusdesc: string;
    invoiceid?: string;
    sellingcurrencysymbol?: string;
    sellingamount?: number;
    customerid: number;
    privacydetails?: any;
}
export interface WhoisLookupRequest {
    domainName: string;
}
export interface WhoisLookupResult {
    domain: string;
    registrar: string;
    registrant: {
        name: string;
        organization?: string;
        email?: string;
        phone?: string;
        address?: string;
        city?: string;
        state?: string;
        country?: string;
        postalCode?: string;
    };
    admin?: {
        name: string;
        organization?: string;
        email?: string;
        phone?: string;
    };
    tech?: {
        name: string;
        organization?: string;
        email?: string;
        phone?: string;
    };
    nameServers: string[];
    creationDate?: string;
    expirationDate?: string;
    updatedDate?: string;
    status: string[];
}
export declare class ResellerBizService {
    private client;
    private authUserId;
    private apiKey;
    constructor();
    checkDomainAvailability(request: DomainAvailabilityRequest): Promise<DomainAvailabilityResult>;
    transferDomain(request: DomainTransferRequest): Promise<DomainTransferResult>;
    whoisLookup(request: WhoisLookupRequest): Promise<WhoisLookupResult>;
}
export declare function getResellerBizService(): ResellerBizService;
//# sourceMappingURL=resellerbiz.d.ts.map