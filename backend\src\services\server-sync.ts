import { logger } from '../utils/logger';
import { appConfig } from '../config';
import { SharedHostingUser, SharedHostingApplication } from '../models/shared-hosting';
import { SSHConnection } from './shared-hosting';

export class ServerSyncService {
  private syncInterval: NodeJS.Timeout | null = null;
  private isSyncing = false;

  constructor() {
    this.startSyncSchedule();
  }

  // Start synchronization schedule
  startSyncSchedule(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
    }

    const intervalMs = appConfig.sync.intervalMinutes * 60 * 1000;
    
    this.syncInterval = setInterval(async () => {
      if (!this.isSyncing) {
        this.isSyncing = true;
        try {
          await this.syncAllServers();
        } catch (error) {
          logger.error('Server sync failed:', error);
        } finally {
          this.isSyncing = false;
        }
      }
    }, intervalMs);

    logger.info(`Server sync started with ${appConfig.sync.intervalMinutes}min interval`);
  }

  // Stop synchronization
  stopSync(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }
    logger.info('Server sync stopped');
  }

  // Sync all servers in the pool
  async syncAllServers(): Promise<void> {
    try {
      const servers = appConfig.sharedHosting.servers;
      
      if (servers.length < 2) {
        logger.debug('Only one server configured, skipping sync');
        return;
      }

      // Get primary server (first in list)
      const primaryServer = servers[0];
      const secondaryServers = servers.slice(1);

      logger.info(`Starting sync from primary server ${primaryServer.ip_address} to ${secondaryServers.length} secondary servers`);

      // Sync to each secondary server
      for (const secondaryServer of secondaryServers) {
        try {
          await this.syncServerToServer(primaryServer, secondaryServer);
        } catch (error) {
          logger.error(`Failed to sync to server ${secondaryServer.ip_address}:`, error);
        }
      }

      logger.info('Server sync completed');
      
    } catch (error) {
      logger.error('Failed to sync servers:', error);
      throw error;
    }
  }

  // Sync from primary to secondary server
  async syncServerToServer(primaryServer: any, secondaryServer: any): Promise<void> {
    const primarySSH = new SSHConnection();
    const secondarySSH = new SSHConnection();

    try {
      // Connect to both servers
      await primarySSH.connect();
      await secondarySSH.connect({
        host: secondaryServer.ip_address,
        username: appConfig.ssh.username,
        privateKey: appConfig.ssh.privateKey,
        port: appConfig.ssh.port
      });

      // Get all active users on primary server
      const activeUsers = await SharedHostingUser.find({
        server_id: primaryServer.ip_address,
        status: 'active'
      });

      logger.info(`Syncing ${activeUsers.length} users from ${primaryServer.ip_address} to ${secondaryServer.ip_address}`);

      // Sync each user's data
      for (const user of activeUsers) {
        try {
          await this.syncUserData(primarySSH, secondarySSH, user);
        } catch (error) {
          logger.error(`Failed to sync user ${user.username}:`, error);
        }
      }

      // Sync NGINX configurations
      await this.syncNginxConfigs(primarySSH, secondarySSH);

      // Sync SSL certificates
      await this.syncSSLCertificates(primarySSH, secondarySSH);

      logger.info(`Successfully synced ${primaryServer.ip_address} to ${secondaryServer.ip_address}`);

    } catch (error) {
      logger.error(`Server sync failed between ${primaryServer.ip_address} and ${secondaryServer.ip_address}:`, error);
      throw error;
    } finally {
      await primarySSH.disconnect();
      await secondarySSH.disconnect();
    }
  }

  // Sync individual user data
  async syncUserData(primarySSH: SSHConnection, secondarySSH: SSHConnection, user: any): Promise<void> {
    try {
      const homeDir = user.home_directory;
      
      // Check if user exists on secondary server
      const userExists = await secondarySSH.exec(`id ${user.linux_username} 2>/dev/null || echo "not_found"`);
      
      if (userExists.includes('not_found')) {
        // Create user on secondary server
        await this.createUserOnSecondary(secondarySSH, user);
      }

      // Sync user files using rsync over SSH
      const rsyncCommand = `rsync -avz --delete -e "ssh -o StrictHostKeyChecking=no -i ${appConfig.ssh.privateKey}" ${homeDir}/ ${appConfig.ssh.username}@${secondarySSH.config.host}:${homeDir}/`;
      
      await primarySSH.exec(rsyncCommand);
      
      // Set correct ownership on secondary server
      await secondarySSH.exec(`chown -R ${user.linux_username}:${user.linux_username} ${homeDir}`);
      
      // Sync systemd service files
      await this.syncUserServices(primarySSH, secondarySSH, user);

      logger.debug(`Synced user data for ${user.username}`);
      
    } catch (error) {
      logger.error(`Failed to sync user data for ${user.username}:`, error);
      throw error;
    }
  }

  // Create user on secondary server
  async createUserOnSecondary(secondarySSH: SSHConnection, user: any): Promise<void> {
    try {
      // Create user with same UID/GID as primary
      const createUserCmd = `useradd -m -u ${user.uid || 1000} -s /bin/bash ${user.linux_username}`;
      await secondarySSH.exec(createUserCmd);

      // Set resource limits
      const limitsConfig = `
${user.linux_username} soft cpu ${user.resource_limits.cpu_quota}
${user.linux_username} hard cpu ${user.resource_limits.cpu_quota}
${user.linux_username} soft memlock ${user.resource_limits.memory_max * 1024}
${user.linux_username} hard memlock ${user.resource_limits.memory_max * 1024}
`;
      
      await secondarySSH.exec(`echo "${limitsConfig}" >> /etc/security/limits.conf`);

      logger.debug(`Created user ${user.username} on secondary server`);
      
    } catch (error) {
      logger.error(`Failed to create user ${user.username} on secondary server:`, error);
      throw error;
    }
  }

  // Sync user systemd services
  async syncUserServices(primarySSH: SSHConnection, secondarySSH: SSHConnection, user: any): Promise<void> {
    try {
      const serviceDir = `/etc/systemd/system`;
      const userServicePattern = `*${user.linux_username}*`;

      // Copy service files
      const rsyncServicesCmd = `rsync -avz -e "ssh -o StrictHostKeyChecking=no -i ${appConfig.ssh.privateKey}" ${serviceDir}/${userServicePattern} ${appConfig.ssh.username}@${secondarySSH.config.host}:${serviceDir}/`;
      
      await primarySSH.exec(rsyncServicesCmd);

      // Reload systemd on secondary server
      await secondarySSH.exec('systemctl daemon-reload');

      logger.debug(`Synced services for user ${user.username}`);
      
    } catch (error) {
      logger.error(`Failed to sync services for user ${user.username}:`, error);
    }
  }

  // Sync NGINX configurations
  async syncNginxConfigs(primarySSH: SSHConnection, secondarySSH: SSHConnection): Promise<void> {
    try {
      const nginxSitesDir = '/etc/nginx/sites-available';
      const nginxEnabledDir = '/etc/nginx/sites-enabled';

      // Sync sites-available
      const rsyncSitesCmd = `rsync -avz --delete -e "ssh -o StrictHostKeyChecking=no -i ${appConfig.ssh.privateKey}" ${nginxSitesDir}/ ${appConfig.ssh.username}@${secondarySSH.config.host}:${nginxSitesDir}/`;
      await primarySSH.exec(rsyncSitesCmd);

      // Sync sites-enabled
      const rsyncEnabledCmd = `rsync -avz --delete -e "ssh -o StrictHostKeyChecking=no -i ${appConfig.ssh.privateKey}" ${nginxEnabledDir}/ ${appConfig.ssh.username}@${secondarySSH.config.host}:${nginxEnabledDir}/`;
      await primarySSH.exec(rsyncEnabledCmd);

      // Test and reload NGINX on secondary server
      await secondarySSH.exec('nginx -t && systemctl reload nginx');

      logger.debug('Synced NGINX configurations');
      
    } catch (error) {
      logger.error('Failed to sync NGINX configurations:', error);
    }
  }

  // Sync SSL certificates
  async syncSSLCertificates(primarySSH: SSHConnection, secondarySSH: SSHConnection): Promise<void> {
    try {
      const sslDir = '/etc/ssl/certs/achidas';

      // Create SSL directory on secondary if it doesn't exist
      await secondarySSH.exec(`mkdir -p ${sslDir}`);

      // Sync SSL certificates
      const rsyncSSLCmd = `rsync -avz --delete -e "ssh -o StrictHostKeyChecking=no -i ${appConfig.ssh.privateKey}" ${sslDir}/ ${appConfig.ssh.username}@${secondarySSH.config.host}:${sslDir}/`;
      await primarySSH.exec(rsyncSSLCmd);

      logger.debug('Synced SSL certificates');
      
    } catch (error) {
      logger.error('Failed to sync SSL certificates:', error);
    }
  }

  // Manual sync trigger
  async triggerManualSync(): Promise<void> {
    if (this.isSyncing) {
      throw new Error('Sync already in progress');
    }

    this.isSyncing = true;
    try {
      await this.syncAllServers();
    } finally {
      this.isSyncing = false;
    }
  }

  // Get sync status
  getSyncStatus(): { isActive: boolean; lastSync: Date | null; nextSync: Date | null } {
    const nextSync = this.syncInterval ? 
      new Date(Date.now() + appConfig.sync.intervalMinutes * 60 * 1000) : null;

    return {
      isActive: this.syncInterval !== null,
      lastSync: null, // Could be tracked in database
      nextSync
    };
  }
}

// Export singleton instance
export const serverSync = new ServerSyncService();
