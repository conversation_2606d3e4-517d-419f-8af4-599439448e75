{"name": "@specy/liquid-glass-react", "version": "1.0.2", "description": "React component for @specy/liquid-glass - A Three.js powered library to make apple's liquid with glass effect", "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}}, "files": ["dist", "README.md"], "keywords": ["react", "three.js", "glass-effect", "morphing", "animation", "webgl", "ui-component", "liquid-glass", "react-component"], "author": "Specy", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/Specy/liquid-glass.git"}, "type": "module", "scripts": {"build": "tsup", "build:watch": "tsup --watch", "dev": "tsup --watch"}, "peerDependencies": {"react": ">=18.0.0", "react-dom": ">=18.0.0"}, "devDependencies": {"@specy/liquid-glass": "^1.12.1", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "react": "^18.3.1", "react-dom": "^18.3.1", "tsup": "^8.5.0", "typescript": "~5.8.3"}}