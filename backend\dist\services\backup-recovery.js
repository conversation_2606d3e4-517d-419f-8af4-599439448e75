"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.backupRecovery = exports.BackupRecoveryService = void 0;
const logger_1 = require("../utils/logger");
const config_1 = require("../config");
const shared_hosting_1 = require("../models/shared-hosting");
const shared_hosting_2 = require("./shared-hosting");
class BackupRecoveryService {
    backupJobs = new Map();
    scheduledBackups = new Map();
    constructor() {
        this.initializeScheduledBackups();
    }
    async initializeScheduledBackups() {
        try {
            this.scheduleBackup('server-full', {
                type: 'full',
                schedule: '0 2 * * *',
                retention: 7,
                compression: true,
                encryption: true,
                destination: 'vultr-object-storage'
            });
            const premiumUsers = await shared_hosting_1.SharedHostingUser.find({
                plan_type: { $in: ['premium', 'enterprise'] },
                status: 'active'
            });
            for (const user of premiumUsers) {
                this.scheduleUserBackup(user.user_id, {
                    type: 'full',
                    schedule: '0 3 * * *',
                    retention: 30,
                    compression: true,
                    encryption: true,
                    destination: 'vultr-object-storage'
                });
            }
            logger_1.logger.info('Initialized scheduled backups');
        }
        catch (error) {
            logger_1.logger.error('Failed to initialize scheduled backups:', error);
        }
    }
    scheduleBackup(jobId, config) {
        if (this.scheduledBackups.has(jobId)) {
            clearInterval(this.scheduledBackups.get(jobId));
        }
        const interval = this.parseCronToInterval(config.schedule);
        const timer = setInterval(async () => {
            try {
                if (jobId.startsWith('server-')) {
                    await this.performServerBackup(config);
                }
                else {
                    await this.performUserBackup(jobId, config);
                }
            }
            catch (error) {
                logger_1.logger.error(`Scheduled backup failed for ${jobId}:`, error);
            }
        }, interval);
        this.scheduledBackups.set(jobId, timer);
        logger_1.logger.info(`Scheduled backup job ${jobId} with interval ${interval}ms`);
    }
    scheduleUserBackup(userId, config) {
        this.scheduleBackup(`user-${userId}`, config);
    }
    async performServerBackup(config) {
        const jobId = this.generateJobId();
        const job = {
            id: jobId,
            userId: 'system',
            type: config.type,
            status: 'running',
            startTime: new Date(),
            location: ''
        };
        this.backupJobs.set(jobId, job);
        try {
            logger_1.logger.info(`Starting server backup job ${jobId}`);
            const ssh = new shared_hosting_2.SSHConnection();
            await ssh.connect();
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const backupDir = `/tmp/server-backup-${timestamp}`;
            const backupFile = `server-backup-${timestamp}.tar.gz`;
            await ssh.exec(`mkdir -p ${backupDir}`);
            await ssh.exec(`cp -r /etc/nginx ${backupDir}/`);
            await ssh.exec(`cp -r /etc/systemd/system ${backupDir}/`);
            await ssh.exec(`cp /etc/security/limits.conf ${backupDir}/`);
            const users = await shared_hosting_1.SharedHostingUser.find({ status: 'active' });
            for (const user of users) {
                const userBackupDir = `${backupDir}/users/${user.linux_username}`;
                await ssh.exec(`mkdir -p ${userBackupDir}`);
                await ssh.exec(`rsync -av --exclude='*.log' --exclude='node_modules' --exclude='.git' ${user.home_directory}/ ${userBackupDir}/`);
                await this.backupUserDatabases(ssh, user, userBackupDir);
            }
            const archivePath = `/tmp/${backupFile}`;
            if (config.compression) {
                await ssh.exec(`tar -czf ${archivePath} -C ${backupDir} .`);
            }
            else {
                await ssh.exec(`tar -cf ${archivePath} -C ${backupDir} .`);
            }
            if (config.encryption) {
                await this.encryptBackup(ssh, archivePath);
            }
            const uploadLocation = await this.uploadBackup(ssh, archivePath, config.destination);
            await ssh.exec(`rm -rf ${backupDir} ${archivePath}`);
            await ssh.disconnect();
            job.status = 'completed';
            job.endTime = new Date();
            job.location = uploadLocation;
            const sizeResult = await ssh.exec(`ls -la ${archivePath} | awk '{print $5}'`);
            job.size = parseInt(sizeResult.trim()) || 0;
            logger_1.logger.info(`Server backup completed: ${uploadLocation}`);
            return uploadLocation;
        }
        catch (error) {
            job.status = 'failed';
            job.endTime = new Date();
            job.error = error instanceof Error ? error.message : 'Unknown error';
            logger_1.logger.error(`Server backup failed for job ${jobId}:`, error);
            throw error;
        }
    }
    async performUserBackup(userId, config) {
        const jobId = this.generateJobId();
        const job = {
            id: jobId,
            userId,
            type: config.type,
            status: 'running',
            startTime: new Date(),
            location: ''
        };
        this.backupJobs.set(jobId, job);
        try {
            const user = await shared_hosting_1.SharedHostingUser.findOne({ user_id: userId });
            if (!user) {
                throw new Error('User not found');
            }
            logger_1.logger.info(`Starting user backup job ${jobId} for ${user.username}`);
            const ssh = new shared_hosting_2.SSHConnection();
            await ssh.connect();
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const backupDir = `/tmp/user-backup-${user.linux_username}-${timestamp}`;
            const backupFile = `user-backup-${user.linux_username}-${timestamp}.tar.gz`;
            await ssh.exec(`mkdir -p ${backupDir}`);
            await ssh.exec(`rsync -av --exclude='*.log' --exclude='node_modules' --exclude='.git' ${user.home_directory}/ ${backupDir}/files/`);
            await this.backupUserDatabases(ssh, user, `${backupDir}/databases`);
            await ssh.exec(`mkdir -p ${backupDir}/config`);
            await ssh.exec(`cp /etc/systemd/system/*${user.linux_username}* ${backupDir}/config/ 2>/dev/null || true`);
            const archivePath = `/tmp/${backupFile}`;
            if (config.compression) {
                await ssh.exec(`tar -czf ${archivePath} -C ${backupDir} .`);
            }
            else {
                await ssh.exec(`tar -cf ${archivePath} -C ${backupDir} .`);
            }
            if (config.encryption) {
                await this.encryptBackup(ssh, archivePath);
            }
            const uploadLocation = await this.uploadBackup(ssh, archivePath, config.destination, `users/${user.linux_username}`);
            await ssh.exec(`rm -rf ${backupDir} ${archivePath}`);
            await ssh.disconnect();
            job.status = 'completed';
            job.endTime = new Date();
            job.location = uploadLocation;
            logger_1.logger.info(`User backup completed for ${user.username}: ${uploadLocation}`);
            return uploadLocation;
        }
        catch (error) {
            job.status = 'failed';
            job.endTime = new Date();
            job.error = error instanceof Error ? error.message : 'Unknown error';
            logger_1.logger.error(`User backup failed for job ${jobId}:`, error);
            throw error;
        }
    }
    async backupUserDatabases(ssh, user, backupDir) {
        try {
            await ssh.exec(`mkdir -p ${backupDir}`);
            const mysqlDbs = await ssh.exec(`mysql -u root -p${config_1.appConfig.database.mysqlRootPassword} -e "SHOW DATABASES LIKE '${user.linux_username}_%';" --skip-column-names`);
            if (mysqlDbs.trim()) {
                const dbNames = mysqlDbs.trim().split('\n');
                for (const dbName of dbNames) {
                    await ssh.exec(`mysqldump -u root -p${config_1.appConfig.database.mysqlRootPassword} ${dbName} > ${backupDir}/${dbName}.sql`);
                }
            }
            const pgDbs = await ssh.exec(`sudo -u postgres psql -c "SELECT datname FROM pg_database WHERE datname LIKE '${user.linux_username}_%';" --tuples-only --no-align`);
            if (pgDbs.trim()) {
                const dbNames = pgDbs.trim().split('\n');
                for (const dbName of dbNames) {
                    await ssh.exec(`sudo -u postgres pg_dump ${dbName} > ${backupDir}/${dbName}.sql`);
                }
            }
        }
        catch (error) {
            logger_1.logger.error(`Failed to backup databases for user ${user.username}:`, error);
        }
    }
    async encryptBackup(ssh, filePath) {
        try {
            const encryptedPath = `${filePath}.enc`;
            await ssh.exec(`openssl enc -aes-256-cbc -salt -in ${filePath} -out ${encryptedPath} -k ${config_1.appConfig.backup.encryptionKey}`);
            await ssh.exec(`mv ${encryptedPath} ${filePath}`);
        }
        catch (error) {
            logger_1.logger.error(`Failed to encrypt backup ${filePath}:`, error);
            throw error;
        }
    }
    async uploadBackup(ssh, filePath, destination, prefix) {
        try {
            const fileName = filePath.split('/').pop();
            const remotePath = prefix ? `${prefix}/${fileName}` : fileName;
            switch (destination) {
                case 'vultr-object-storage':
                    await ssh.exec(`s3cmd put ${filePath} s3://${config_1.appConfig.backup.bucketName}/${remotePath}`);
                    return `s3://${config_1.appConfig.backup.bucketName}/${remotePath}`;
                case 's3':
                    await ssh.exec(`aws s3 cp ${filePath} s3://${config_1.appConfig.backup.bucketName}/${remotePath}`);
                    return `s3://${config_1.appConfig.backup.bucketName}/${remotePath}`;
                case 'local':
                    const localPath = `/var/backups/achidas/${remotePath}`;
                    await ssh.exec(`mkdir -p $(dirname ${localPath})`);
                    await ssh.exec(`mv ${filePath} ${localPath}`);
                    return localPath;
                default:
                    throw new Error(`Unsupported backup destination: ${destination}`);
            }
        }
        catch (error) {
            logger_1.logger.error(`Failed to upload backup to ${destination}:`, error);
            throw error;
        }
    }
    async restoreFromBackup(backupLocation, userId) {
        const ssh = new shared_hosting_2.SSHConnection();
        try {
            await ssh.connect();
            logger_1.logger.info(`Starting restore from backup: ${backupLocation}`);
            const localPath = `/tmp/restore-${Date.now()}.tar.gz`;
            await this.downloadBackup(ssh, backupLocation, localPath);
            if (backupLocation.endsWith('.enc')) {
                await this.decryptBackup(ssh, localPath);
            }
            const extractDir = `/tmp/restore-${Date.now()}`;
            await ssh.exec(`mkdir -p ${extractDir}`);
            await ssh.exec(`tar -xzf ${localPath} -C ${extractDir}`);
            if (userId) {
                await this.restoreUserData(ssh, extractDir, userId);
            }
            else {
                await this.restoreServerData(ssh, extractDir);
            }
            await ssh.exec(`rm -rf ${extractDir} ${localPath}`);
            await ssh.disconnect();
            logger_1.logger.info('Restore completed successfully');
        }
        catch (error) {
            logger_1.logger.error('Restore failed:', error);
            throw error;
        }
    }
    async downloadBackup(ssh, backupLocation, localPath) {
        if (backupLocation.startsWith('s3://')) {
            await ssh.exec(`s3cmd get ${backupLocation} ${localPath}`);
        }
        else if (backupLocation.startsWith('/')) {
            await ssh.exec(`cp ${backupLocation} ${localPath}`);
        }
        else {
            throw new Error(`Unsupported backup location: ${backupLocation}`);
        }
    }
    async decryptBackup(ssh, filePath) {
        const decryptedPath = filePath.replace('.enc', '');
        await ssh.exec(`openssl enc -aes-256-cbc -d -in ${filePath} -out ${decryptedPath} -k ${config_1.appConfig.backup.encryptionKey}`);
        await ssh.exec(`mv ${decryptedPath} ${filePath}`);
    }
    async restoreUserData(ssh, extractDir, userId) {
        const user = await shared_hosting_1.SharedHostingUser.findOne({ user_id: userId });
        if (!user) {
            throw new Error('User not found');
        }
        await ssh.exec(`rsync -av ${extractDir}/files/ ${user.home_directory}/`);
        await ssh.exec(`chown -R ${user.linux_username}:${user.linux_username} ${user.home_directory}`);
        const dbDir = `${extractDir}/databases`;
        const sqlFiles = await ssh.exec(`find ${dbDir} -name "*.sql" 2>/dev/null || echo ""`);
        if (sqlFiles.trim()) {
            const files = sqlFiles.trim().split('\n');
            for (const file of files) {
                const dbName = file.split('/').pop().replace('.sql', '');
                if (dbName.startsWith(user.linux_username)) {
                    await ssh.exec(`mysql -u root -p${config_1.appConfig.database.mysqlRootPassword} ${dbName} < ${file}`);
                }
            }
        }
    }
    async restoreServerData(ssh, extractDir) {
        await ssh.exec(`cp -r ${extractDir}/nginx/* /etc/nginx/`);
        await ssh.exec(`cp -r ${extractDir}/system/* /etc/systemd/system/`);
        await ssh.exec(`systemctl daemon-reload`);
        await ssh.exec(`nginx -t && systemctl reload nginx`);
    }
    parseCronToInterval(cronSchedule) {
        if (cronSchedule === '0 2 * * *')
            return 24 * 60 * 60 * 1000;
        if (cronSchedule === '0 3 * * *')
            return 24 * 60 * 60 * 1000;
        return 24 * 60 * 60 * 1000;
    }
    generateJobId() {
        return `backup_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    getBackupJob(jobId) {
        return this.backupJobs.get(jobId);
    }
    listBackupJobs(userId) {
        const jobs = Array.from(this.backupJobs.values());
        return userId ? jobs.filter(job => job.userId === userId) : jobs;
    }
    async cleanupOldBackups(retentionDays) {
        const ssh = new shared_hosting_2.SSHConnection();
        try {
            await ssh.connect();
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - retentionDays);
            await ssh.exec(`find /var/backups/achidas -type f -mtime +${retentionDays} -delete`);
            logger_1.logger.info(`Cleaned up backups older than ${retentionDays} days`);
        }
        catch (error) {
            logger_1.logger.error('Failed to cleanup old backups:', error);
        }
        finally {
            await ssh.disconnect();
        }
    }
}
exports.BackupRecoveryService = BackupRecoveryService;
exports.backupRecovery = new BackupRecoveryService();
//# sourceMappingURL=backup-recovery.js.map