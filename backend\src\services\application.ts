import { Collection, ObjectId } from 'mongodb';
import { databaseConnection } from '../database/connection';
import { appConfig } from '../config';
import {
  Application,
  ApplicationStatus,
  CreateApplicationRequest,
  UpdateApplicationRequest,
  toApplicationResponse,
  ApplicationResponse,
  PaginationOptions,
  PaginatedResponse
} from '../models';
import { logger } from '../utils/logger';
import { cloudflareDNS } from './cloudflare-dns';

export class ApplicationService {
  private applicationsCollection: Collection<Application>;

  constructor() {
    this.applicationsCollection = databaseConnection.getCollection<Application>('applications');
  }

  // Helper method to generate subdomain from application name
  private generateSubdomain(applicationName: string): string {
    return applicationName
      .toLowerCase()
      .replace(/[^a-z0-9-]/g, '-') // Replace non-alphanumeric with hyphens
      .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
      .replace(/^-|-$/g, ''); // Remove leading/trailing hyphens
  }

  // Helper method to generate unique subdomain with dynamic identifier
  private async generateUniqueSubdomain(applicationName: string, userId: string, excludeId?: string): Promise<string> {
    const baseSubdomain = this.generateSubdomain(applicationName);

    // First try the base subdomain
    if (await this.isSubdomainUnique(baseSubdomain, excludeId)) {
      return baseSubdomain;
    }

    // If not unique, add user identifier (first 6 chars of user ID)
    const userIdentifier = userId.substring(0, 6).toLowerCase();
    const subdomainWithUser = `${baseSubdomain}-${userIdentifier}`;

    if (await this.isSubdomainUnique(subdomainWithUser, excludeId)) {
      return subdomainWithUser;
    }

    // If still not unique, add timestamp
    const timestamp = Date.now().toString().slice(-6); // Last 6 digits of timestamp
    const subdomainWithTimestamp = `${baseSubdomain}-${userIdentifier}-${timestamp}`;

    if (await this.isSubdomainUnique(subdomainWithTimestamp, excludeId)) {
      return subdomainWithTimestamp;
    }

    // If still not unique, add random string
    const randomString = Math.random().toString(36).substring(2, 8);
    return `${baseSubdomain}-${userIdentifier}-${randomString}`;
  }

  // Helper method to check subdomain uniqueness across all applications
  private async isSubdomainUnique(subdomain: string, excludeId?: string): Promise<boolean> {
    const query: any = { subdomain };
    if (excludeId) {
      query._id = { $ne: new ObjectId(excludeId) };
    }

    const existingApp = await this.applicationsCollection.findOne(query);
    return !existingApp;
  }

  async createApplication(userId: string, applicationData: CreateApplicationRequest): Promise<ApplicationResponse> {
    try {
      // Generate unique subdomain with dynamic identifier if needed
      const subdomain = await this.generateUniqueSubdomain(applicationData.name, userId);

      // Check if application name already exists for this user (additional check)
      const existingApp = await this.applicationsCollection.findOne({
        user_id: userId,
        name: applicationData.name
      });

      if (existingApp) {
        throw new Error('Application with this name already exists');
      }

      // Create application object
      const now = new Date();
      const fullDomain = `${subdomain}.${appConfig.domain.primary}`;

      const newApplication: Application = {
        user_id: userId,
        name: applicationData.name,
        description: applicationData.description,
        type: applicationData.type,
        status: ApplicationStatus.DRAFT,
        repository: applicationData.repository,
        environment: applicationData.environment,
        deployment: applicationData.deployment,
        resources: applicationData.resources || {},
        subdomain,
        domain: fullDomain,
        created_at: now,
        updated_at: now,
        stats: {
          total_deployments: 0,
          successful_deployments: 0,
          failed_deployments: 0,
          last_deployment_status: 'none',
        },
      } as Application;

      // Insert application into database
      const result = await this.applicationsCollection.insertOne(newApplication);
      
      // Fetch the created application
      const createdApplication = await this.applicationsCollection.findOne({ _id: result.insertedId });
      
      if (!createdApplication) {
        throw new Error('Failed to create application');
      }

      logger.info(`Application created: ${applicationData.name} for user: ${userId} with subdomain: ${subdomain}.${appConfig.domain.primary}`);
      return toApplicationResponse(createdApplication);
    } catch (error) {
      logger.error('Create application error:', error);
      throw error;
    }
  }

  async getApplicationById(applicationId: string, userId?: string): Promise<ApplicationResponse> {
    try {
      const query: any = { _id: new ObjectId(applicationId) };
      
      // If userId is provided, ensure user owns the application
      if (userId) {
        query.user_id = userId;
      }

      const application = await this.applicationsCollection.findOne(query);
      
      if (!application) {
        throw new Error('Application not found');
      }

      return toApplicationResponse(application);
    } catch (error) {
      logger.error('Get application by ID error:', error);
      throw error;
    }
  }

  async getApplicationsByUser(
    userId: string, 
    options: PaginationOptions = {}
  ): Promise<PaginatedResponse<ApplicationResponse>> {
    try {
      const { page = 1, limit = 10, sort = 'created_at', order = 'desc' } = options;
      const skip = (page - 1) * limit;

      // Build sort object
      const sortObj: any = {};
      sortObj[sort] = order === 'asc' ? 1 : -1;

      // Get applications with pagination
      const applications = await this.applicationsCollection
        .find({ user_id: userId })
        .sort(sortObj)
        .skip(skip)
        .limit(limit)
        .toArray();

      // Get total count
      const total = await this.applicationsCollection.countDocuments({ user_id: userId });

      const pages = Math.ceil(total / limit);

      return {
        data: applications.map(toApplicationResponse),
        pagination: {
          page,
          limit,
          total,
          pages,
          has_next: page < pages,
          has_prev: page > 1,
        },
      };
    } catch (error) {
      logger.error('Get applications by user error:', error);
      throw error;
    }
  }

  async updateApplication(
    applicationId: string, 
    userId: string, 
    updateData: UpdateApplicationRequest
  ): Promise<ApplicationResponse> {
    try {
      // Check if application exists and user owns it
      const existingApp = await this.applicationsCollection.findOne({
        _id: new ObjectId(applicationId),
        user_id: userId
      });

      if (!existingApp) {
        throw new Error('Application not found');
      }

      // If name is being updated, check for conflicts
      if (updateData.name && updateData.name !== existingApp.name) {
        const nameConflict = await this.applicationsCollection.findOne({
          user_id: userId,
          name: updateData.name,
          _id: { $ne: new ObjectId(applicationId) }
        });

        if (nameConflict) {
          throw new Error('Application with this name already exists');
        }
      }

      // Update application
      const updateFields: any = {
        ...updateData,
        updated_at: new Date(),
      };

      const result = await this.applicationsCollection.updateOne(
        { _id: new ObjectId(applicationId), user_id: userId },
        { $set: updateFields }
      );

      if (result.matchedCount === 0) {
        throw new Error('Application not found');
      }

      // Fetch updated application
      const updatedApplication = await this.applicationsCollection.findOne({
        _id: new ObjectId(applicationId)
      });

      if (!updatedApplication) {
        throw new Error('Failed to fetch updated application');
      }

      logger.info(`Application updated: ${applicationId} for user: ${userId}`);
      return toApplicationResponse(updatedApplication);
    } catch (error) {
      logger.error('Update application error:', error);
      throw error;
    }
  }

  async deleteApplication(applicationId: string, userId: string): Promise<void> {
    try {
      const result = await this.applicationsCollection.deleteOne({
        _id: new ObjectId(applicationId),
        user_id: userId
      });

      if (result.deletedCount === 0) {
        throw new Error('Application not found');
      }

      logger.info(`Application deleted: ${applicationId} for user: ${userId}`);
    } catch (error) {
      logger.error('Delete application error:', error);
      throw error;
    }
  }

  async updateApplicationStatus(
    applicationId: string, 
    status: ApplicationStatus,
    userId?: string
  ): Promise<ApplicationResponse> {
    try {
      const query: any = { _id: new ObjectId(applicationId) };
      
      if (userId) {
        query.user_id = userId;
      }

      const result = await this.applicationsCollection.updateOne(
        query,
        { 
          $set: { 
            status,
            updated_at: new Date()
          }
        }
      );

      if (result.matchedCount === 0) {
        throw new Error('Application not found');
      }

      // Fetch updated application
      const updatedApplication = await this.applicationsCollection.findOne({
        _id: new ObjectId(applicationId)
      });

      if (!updatedApplication) {
        throw new Error('Failed to fetch updated application');
      }

      logger.info(`Application status updated: ${applicationId} to ${status}`);
      return toApplicationResponse(updatedApplication);
    } catch (error) {
      logger.error('Update application status error:', error);
      throw error;
    }
  }

  async getApplicationStats(userId: string): Promise<any> {
    try {
      const pipeline = [
        { $match: { user_id: userId } },
        {
          $group: {
            _id: '$status',
            count: { $sum: 1 }
          }
        }
      ];

      const statusCounts = await this.applicationsCollection.aggregate(pipeline).toArray();
      
      const stats = {
        total: 0,
        draft: 0,
        building: 0,
        deployed: 0,
        failed: 0,
        stopped: 0,
        suspended: 0,
      };

      statusCounts.forEach(item => {
        stats.total += item['count'];
        stats[item['_id'] as keyof typeof stats] = item['count'];
      });

      return stats;
    } catch (error) {
      logger.error('Get application stats error:', error);
      throw error;
    }
  }

  // Create DNS record for application deployment
  async createApplicationDNS(applicationId: string, serverIP: string): Promise<void> {
    try {
      const application = await this.applicationsCollection.findOne({ _id: new ObjectId(applicationId) });

      if (!application || !application.subdomain) {
        throw new Error('Application not found or missing subdomain');
      }

      // Create DNS A record pointing to the server IP
      await cloudflareDNS.createDNSRecord({
        type: 'A',
        name: application.domain!,
        content: serverIP,
        ttl: 300,
        proxied: false
      });

      logger.info(`Created DNS record for ${application.domain} -> ${serverIP}`);
    } catch (error) {
      logger.error(`Failed to create DNS record for application ${applicationId}:`, error);
      throw error;
    }
  }

  // Update DNS record for application
  async updateApplicationDNS(applicationId: string, newServerIP: string): Promise<void> {
    try {
      const application = await this.applicationsCollection.findOne({ _id: new ObjectId(applicationId) });

      if (!application || !application.domain) {
        throw new Error('Application not found or missing domain');
      }

      // Get existing DNS records for this domain
      const existingRecords = await cloudflareDNS.getDNSRecords(application.domain, 'A');

      if (existingRecords.length > 0 && existingRecords[0]?.id) {
        // Update existing record
        await cloudflareDNS.updateDNSRecord(existingRecords[0].id, {
          content: newServerIP
        });
      } else {
        // Create new record if none exists
        await this.createApplicationDNS(applicationId, newServerIP);
      }

      logger.info(`Updated DNS record for ${application.domain} -> ${newServerIP}`);
    } catch (error) {
      logger.error(`Failed to update DNS record for application ${applicationId}:`, error);
      throw error;
    }
  }

  // Delete DNS record for application
  async deleteApplicationDNS(applicationId: string): Promise<void> {
    try {
      const application = await this.applicationsCollection.findOne({ _id: new ObjectId(applicationId) });

      if (!application || !application.domain) {
        return; // Nothing to delete
      }

      // Get existing DNS records for this domain
      const existingRecords = await cloudflareDNS.getDNSRecords(application.domain, 'A');

      // Delete all A records for this domain
      for (const record of existingRecords) {
        if (record.id) {
          await cloudflareDNS.deleteDNSRecord(record.id);
        }
      }

      logger.info(`Deleted DNS records for ${application.domain}`);
    } catch (error) {
      logger.error(`Failed to delete DNS record for application ${applicationId}:`, error);
      throw error;
    }
  }

  // Deploy application with DNS record creation
  async deployApplication(applicationId: string, userId: string): Promise<Application> {
    try {
      const application = await this.applicationsCollection.findOne({
        _id: new ObjectId(applicationId),
        user_id: userId
      });

      if (!application) {
        throw new Error('Application not found');
      }

      // Update status to building
      await this.applicationsCollection.updateOne(
        { _id: new ObjectId(applicationId) },
        {
          $set: {
            status: ApplicationStatus.BUILDING,
            updated_at: new Date()
          }
        }
      );

      // Create DNS record for the application
      // For now, we'll use a default server IP - in production this would be dynamic
      const defaultServerIP = '*************'; // Default Vultr server IP

      try {
        await this.createApplicationDNS(applicationId, defaultServerIP);

        // Update status to deployed
        await this.applicationsCollection.updateOne(
          { _id: new ObjectId(applicationId) },
          {
            $set: {
              status: ApplicationStatus.DEPLOYED,
              updated_at: new Date()
            }
          }
        );

        logger.info(`Successfully deployed application ${applicationId} with DNS record`);
      } catch (dnsError) {
        // If DNS creation fails, update status to failed
        await this.applicationsCollection.updateOne(
          { _id: new ObjectId(applicationId) },
          {
            $set: {
              status: ApplicationStatus.FAILED,
              updated_at: new Date()
            }
          }
        );

        logger.error(`DNS creation failed for application ${applicationId}:`, dnsError);
        throw new Error('Deployment failed: Unable to create DNS record');
      }

      // Return updated application
      const updatedApplication = await this.applicationsCollection.findOne({ _id: new ObjectId(applicationId) });
      return updatedApplication!;
    } catch (error) {
      logger.error(`Failed to deploy application ${applicationId}:`, error);
      throw error;
    }
  }
}

// Lazy singleton instance
let applicationServiceInstance: ApplicationService | null = null;

export function getApplicationService(): ApplicationService {
  if (!applicationServiceInstance) {
    applicationServiceInstance = new ApplicationService();
  }
  return applicationServiceInstance;
}
