import { startServer } from './app';

// Start the server
startServer().catch((error) => {
  console.error('Failed to start server:', error);
  console.error('Error name:', error.name);
  console.error('Error message:', error.message);
  console.error('Error stack:', error.stack);

  // Log additional error properties
  if (error.code) console.error('Error code:', error.code);
  if (error.errno) console.error('Error errno:', error.errno);
  if (error.syscall) console.error('Error syscall:', error.syscall);
  if (error.address) console.error('Error address:', error.address);
  if (error.port) console.error('Error port:', error.port);

  console.error('Error details:', JSON.stringify(error, null, 2));
  process.exit(1);
});
