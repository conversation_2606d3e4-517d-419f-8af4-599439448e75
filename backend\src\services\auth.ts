import * as bcrypt from 'bcryptjs';
import * as jwt from 'jsonwebtoken';
import { Collection, ObjectId } from 'mongodb';
import { appConfig } from '../config';
import { databaseConnection } from '../database/connection';
import { 
  User, 
  UserStatus, 
  UserRole, 
  CreateUserRequest, 
  LoginRequest, 
  JwtPayload,
  toUserResponse,
  UserResponse 
} from '../models';
import { logger } from '../utils/logger';

export interface LoginResponse {
  token: string;
  user: UserResponse;
  expires_at: string;
}

export class AuthService {
  private usersCollection: Collection<User>;

  constructor() {
    this.usersCollection = databaseConnection.getCollection<User>('users');
  }

  async registerUser(userData: CreateUserRequest): Promise<UserResponse> {
    try {
      // Check if user already exists
      const existingUser = await this.usersCollection.findOne({
        $or: [
          { email: userData.email },
          { username: userData.username }
        ]
      });

      if (existingUser) {
        if (existingUser.email === userData.email) {
          throw new Error('User with this email already exists');
        }
        if (existingUser.username === userData.username) {
          throw new Error('User with this username already exists');
        }
      }

      // Hash password
      const saltRounds = 12;
      const passwordHash = await bcrypt.hash(userData.password, saltRounds);

      // Create user object
      const now = new Date();
      const newUser: User = {
        email: userData.email,
        username: userData.username,
        password_hash: passwordHash,
        first_name: userData.first_name,
        last_name: userData.last_name,
        company: userData.company,
        phone: userData.phone,
        role: UserRole.USER,
        status: UserStatus.ACTIVE,
        email_verified: false,
        created_at: now,
        updated_at: now,
        metadata: {
          login_attempts: 0,
        },
      } as User;

      // Insert user into database
      const result = await this.usersCollection.insertOne(newUser);
      
      // Fetch the created user
      const createdUser = await this.usersCollection.findOne({ _id: result.insertedId });
      
      if (!createdUser) {
        throw new Error('Failed to create user');
      }

      logger.info(`User registered successfully: ${userData.email}`);
      return toUserResponse(createdUser);
    } catch (error) {
      logger.error('User registration error:', error);
      throw error;
    }
  }

  async loginUser(loginData: LoginRequest): Promise<LoginResponse> {
    try {
      // Find user by email
      const user = await this.usersCollection.findOne({ email: loginData.email });
      
      if (!user) {
        throw new Error('Invalid email or password');
      }

      // Check user status
      if (user.status !== UserStatus.ACTIVE) {
        throw new Error('Account is not active');
      }

      // Verify password
      const isPasswordValid = await bcrypt.compare(loginData.password, user.password_hash);
      
      if (!isPasswordValid) {
        // Increment login attempts
        await this.usersCollection.updateOne(
          { _id: user._id },
          { 
            $inc: { 'metadata.login_attempts': 1 },
            $set: { updated_at: new Date() }
          }
        );
        
        throw new Error('Invalid email or password');
      }

      // Reset login attempts and update last login
      await this.usersCollection.updateOne(
        { _id: user._id },
        { 
          $set: { 
            last_login: new Date(),
            'metadata.login_attempts': 0,
            updated_at: new Date()
          }
        }
      );

      // Generate JWT token
      const tokenPayload: Omit<JwtPayload, 'iat' | 'exp'> = {
        sub: user._id!.toString(),
        email: user.email,
        username: user.username,
        role: user.role,
      };

      const token = jwt.sign(tokenPayload, appConfig.auth.jwtSecret, {
        expiresIn: appConfig.auth.jwtExpiresIn,
      } as jwt.SignOptions);

      // Calculate expiration date
      const expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + 7); // 7 days from now

      logger.info(`User logged in successfully: ${loginData.email}`);

      return {
        token,
        user: toUserResponse(user),
        expires_at: expiresAt.toISOString(),
      };
    } catch (error) {
      logger.error('User login error:', error);
      throw error;
    }
  }

  async getUserById(userId: string): Promise<UserResponse> {
    try {
      const user = await this.usersCollection.findOne({ _id: new ObjectId(userId) });
      
      if (!user) {
        throw new Error('User not found');
      }

      return toUserResponse(user);
    } catch (error) {
      logger.error('Get user by ID error:', error);
      throw error;
    }
  }

  async getUserByEmail(email: string): Promise<UserResponse | null> {
    try {
      const user = await this.usersCollection.findOne({ email });
      
      if (!user) {
        return null;
      }

      return toUserResponse(user);
    } catch (error) {
      logger.error('Get user by email error:', error);
      throw error;
    }
  }

  async updateUserLastLogin(userId: string, ipAddress?: string): Promise<void> {
    try {
      await this.usersCollection.updateOne(
        { _id: new ObjectId(userId) },
        { 
          $set: { 
            last_login: new Date(),
            'metadata.last_login_ip': ipAddress,
            updated_at: new Date()
          }
        }
      );
    } catch (error) {
      logger.error('Update user last login error:', error);
      throw error;
    }
  }

  verifyToken(token: string): JwtPayload {
    try {
      return jwt.verify(token, appConfig.auth.jwtSecret) as JwtPayload;
    } catch (error) {
      logger.warn('Token verification failed:', error);
      throw new Error('Invalid or expired token');
    }
  }

  async changePassword(userId: string, currentPassword: string, newPassword: string): Promise<void> {
    try {
      const user = await this.usersCollection.findOne({ _id: new ObjectId(userId) });
      
      if (!user) {
        throw new Error('User not found');
      }

      // Verify current password
      const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password_hash);
      
      if (!isCurrentPasswordValid) {
        throw new Error('Current password is incorrect');
      }

      // Hash new password
      const saltRounds = 12;
      const newPasswordHash = await bcrypt.hash(newPassword, saltRounds);

      // Update password
      await this.usersCollection.updateOne(
        { _id: user._id },
        { 
          $set: { 
            password_hash: newPasswordHash,
            updated_at: new Date()
          }
        }
      );

      logger.info(`Password changed for user: ${user.email}`);
    } catch (error) {
      logger.error('Change password error:', error);
      throw error;
    }
  }
}

// Lazy singleton instance
let authServiceInstance: AuthService | null = null;

export function getAuthService(): AuthService {
  if (!authServiceInstance) {
    authServiceInstance = new AuthService();
  }
  return authServiceInstance;
}
