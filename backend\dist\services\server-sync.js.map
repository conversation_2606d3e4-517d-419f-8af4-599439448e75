{"version": 3, "file": "server-sync.js", "sourceRoot": "", "sources": ["../../src/services/server-sync.ts"], "names": [], "mappings": ";;;AAAA,4CAAyC;AACzC,sCAAsC;AACtC,6DAA6D;AAC7D,qDAAiD;AAEjD,MAAa,iBAAiB;IACpB,YAAY,GAA0B,IAAI,CAAC;IAC3C,SAAS,GAAG,KAAK,CAAC;IAE1B;QACE,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC3B,CAAC;IAGD,iBAAiB;QACf,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACnC,CAAC;QAED,MAAM,UAAU,GAAG,kBAAS,CAAC,IAAI,CAAC,eAAe,GAAG,EAAE,GAAG,IAAI,CAAC;QAE9D,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC,KAAK,IAAI,EAAE;YACzC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;gBACpB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;gBACtB,IAAI,CAAC;oBACH,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;gBAC9B,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,eAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;gBAC7C,CAAC;wBAAS,CAAC;oBACT,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;gBACzB,CAAC;YACH,CAAC;QACH,CAAC,EAAE,UAAU,CAAC,CAAC;QAEf,eAAM,CAAC,IAAI,CAAC,4BAA4B,kBAAS,CAAC,IAAI,CAAC,eAAe,cAAc,CAAC,CAAC;IACxF,CAAC;IAGD,QAAQ;QACN,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACjC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QAC3B,CAAC;QACD,eAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;IACrC,CAAC;IAGD,KAAK,CAAC,cAAc;QAClB,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,kBAAS,CAAC,aAAa,CAAC,OAAO,CAAC;YAEhD,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACvB,eAAM,CAAC,KAAK,CAAC,2CAA2C,CAAC,CAAC;gBAC1D,OAAO;YACT,CAAC;YAGD,MAAM,aAAa,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YACjC,MAAM,gBAAgB,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAE1C,eAAM,CAAC,IAAI,CAAC,qCAAqC,aAAa,CAAC,UAAU,OAAO,gBAAgB,CAAC,MAAM,oBAAoB,CAAC,CAAC;YAG7H,KAAK,MAAM,eAAe,IAAI,gBAAgB,EAAE,CAAC;gBAC/C,IAAI,CAAC;oBACH,MAAM,IAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE,eAAe,CAAC,CAAC;gBAChE,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,eAAM,CAAC,KAAK,CAAC,4BAA4B,eAAe,CAAC,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;gBACjF,CAAC;YACH,CAAC;YAED,eAAM,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;QAEvC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;YAC/C,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAGD,KAAK,CAAC,kBAAkB,CAAC,aAAkB,EAAE,eAAoB;QAC/D,MAAM,UAAU,GAAG,IAAI,8BAAa,EAAE,CAAC;QACvC,MAAM,YAAY,GAAG,IAAI,8BAAa,EAAE,CAAC;QAEzC,IAAI,CAAC;YAEH,MAAM,UAAU,CAAC,OAAO,EAAE,CAAC;YAC3B,MAAM,YAAY,CAAC,OAAO,EAAE,CAAC;YAG7B,MAAM,WAAW,GAAG,MAAM,kCAAiB,CAAC,IAAI,CAAC;gBAC/C,SAAS,EAAE,aAAa,CAAC,UAAU;gBACnC,MAAM,EAAE,QAAQ;aACjB,CAAC,CAAC;YAEH,eAAM,CAAC,IAAI,CAAC,WAAW,WAAW,CAAC,MAAM,eAAe,aAAa,CAAC,UAAU,OAAO,eAAe,CAAC,UAAU,EAAE,CAAC,CAAC;YAGrH,KAAK,MAAM,IAAI,IAAI,WAAW,EAAE,CAAC;gBAC/B,IAAI,CAAC;oBACH,MAAM,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,YAAY,EAAE,IAAI,EAAE,eAAe,CAAC,CAAC;gBAC3E,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,eAAM,CAAC,KAAK,CAAC,uBAAuB,IAAI,CAAC,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;gBAC/D,CAAC;YACH,CAAC;YAGD,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;YAGtD,MAAM,IAAI,CAAC,mBAAmB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;YAEzD,eAAM,CAAC,IAAI,CAAC,uBAAuB,aAAa,CAAC,UAAU,OAAO,eAAe,CAAC,UAAU,EAAE,CAAC,CAAC;QAElG,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,8BAA8B,aAAa,CAAC,UAAU,QAAQ,eAAe,CAAC,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;YACjH,MAAM,KAAK,CAAC;QACd,CAAC;gBAAS,CAAC;YACT,MAAM,UAAU,CAAC,UAAU,EAAE,CAAC;YAC9B,MAAM,YAAY,CAAC,UAAU,EAAE,CAAC;QAClC,CAAC;IACH,CAAC;IAGD,KAAK,CAAC,YAAY,CAAC,UAAyB,EAAE,YAA2B,EAAE,IAAS,EAAE,eAAoB;QACxG,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC;YAGpC,MAAM,UAAU,GAAG,MAAM,YAAY,CAAC,cAAc,CAAC,MAAM,IAAI,CAAC,cAAc,kCAAkC,CAAC,CAAC;YAElH,IAAI,UAAU,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;gBAErC,MAAM,IAAI,CAAC,qBAAqB,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;YACvD,CAAC;YAGD,MAAM,YAAY,GAAG,8DAA8D,kBAAS,CAAC,GAAG,CAAC,cAAc,KAAK,OAAO,KAAK,kBAAS,CAAC,GAAG,CAAC,QAAQ,IAAI,eAAe,CAAC,UAAU,IAAI,OAAO,GAAG,CAAC;YAEnM,MAAM,UAAU,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;YAG9C,MAAM,YAAY,CAAC,cAAc,CAAC,YAAY,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,IAAI,OAAO,EAAE,CAAC,CAAC;YAGvG,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,YAAY,EAAE,IAAI,CAAC,CAAC;YAE5D,eAAM,CAAC,KAAK,CAAC,wBAAwB,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;QAExD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,gCAAgC,IAAI,CAAC,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;YACtE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAGD,KAAK,CAAC,qBAAqB,CAAC,YAA2B,EAAE,IAAS;QAChE,IAAI,CAAC;YAEH,MAAM,aAAa,GAAG,iBAAiB,IAAI,CAAC,GAAG,IAAI,IAAI,iBAAiB,IAAI,CAAC,cAAc,EAAE,CAAC;YAC9F,MAAM,YAAY,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;YAGjD,MAAM,YAAY,GAAG;EACzB,IAAI,CAAC,cAAc,aAAa,IAAI,CAAC,eAAe,CAAC,SAAS;EAC9D,IAAI,CAAC,cAAc,aAAa,IAAI,CAAC,eAAe,CAAC,SAAS;EAC9D,IAAI,CAAC,cAAc,iBAAiB,IAAI,CAAC,eAAe,CAAC,UAAU,GAAG,IAAI;EAC1E,IAAI,CAAC,cAAc,iBAAiB,IAAI,CAAC,eAAe,CAAC,UAAU,GAAG,IAAI;CAC3E,CAAC;YAEI,MAAM,YAAY,CAAC,cAAc,CAAC,SAAS,YAAY,gCAAgC,CAAC,CAAC;YAEzF,eAAM,CAAC,KAAK,CAAC,gBAAgB,IAAI,CAAC,QAAQ,sBAAsB,CAAC,CAAC;QAEpE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,yBAAyB,IAAI,CAAC,QAAQ,uBAAuB,EAAE,KAAK,CAAC,CAAC;YACnF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAGD,KAAK,CAAC,gBAAgB,CAAC,UAAyB,EAAE,YAA2B,EAAE,IAAS;QACtF,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,qBAAqB,CAAC;YACzC,MAAM,kBAAkB,GAAG,IAAI,IAAI,CAAC,cAAc,GAAG,CAAC;YAGtD,MAAM,gBAAgB,GAAG,qDAAqD,kBAAS,CAAC,GAAG,CAAC,cAAc,KAAK,UAAU,IAAI,kBAAkB,IAAI,kBAAS,CAAC,GAAG,CAAC,QAAQ,IAAI,kBAAS,CAAC,GAAG,CAAC,IAAI,IAAI,UAAU,GAAG,CAAC;YAEjN,MAAM,UAAU,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;YAGlD,MAAM,YAAY,CAAC,cAAc,CAAC,yBAAyB,CAAC,CAAC;YAE7D,eAAM,CAAC,KAAK,CAAC,4BAA4B,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;QAE5D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,oCAAoC,IAAI,CAAC,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;QAC5E,CAAC;IACH,CAAC;IAGD,KAAK,CAAC,gBAAgB,CAAC,UAAyB,EAAE,YAA2B;QAC3E,IAAI,CAAC;YACH,MAAM,aAAa,GAAG,4BAA4B,CAAC;YACnD,MAAM,eAAe,GAAG,0BAA0B,CAAC;YAGnD,MAAM,aAAa,GAAG,8DAA8D,kBAAS,CAAC,GAAG,CAAC,cAAc,KAAK,aAAa,KAAK,kBAAS,CAAC,GAAG,CAAC,QAAQ,IAAI,kBAAS,CAAC,GAAG,CAAC,IAAI,IAAI,aAAa,GAAG,CAAC;YACxM,MAAM,UAAU,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;YAG/C,MAAM,eAAe,GAAG,8DAA8D,kBAAS,CAAC,GAAG,CAAC,cAAc,KAAK,eAAe,KAAK,kBAAS,CAAC,GAAG,CAAC,QAAQ,IAAI,kBAAS,CAAC,GAAG,CAAC,IAAI,IAAI,eAAe,GAAG,CAAC;YAC9M,MAAM,UAAU,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;YAGjD,MAAM,YAAY,CAAC,cAAc,CAAC,oCAAoC,CAAC,CAAC;YAExE,eAAM,CAAC,KAAK,CAAC,6BAA6B,CAAC,CAAC;QAE9C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;QAC9D,CAAC;IACH,CAAC;IAGD,KAAK,CAAC,mBAAmB,CAAC,UAAyB,EAAE,YAA2B;QAC9E,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,wBAAwB,CAAC;YAGxC,MAAM,YAAY,CAAC,cAAc,CAAC,YAAY,MAAM,EAAE,CAAC,CAAC;YAGxD,MAAM,WAAW,GAAG,8DAA8D,kBAAS,CAAC,GAAG,CAAC,cAAc,KAAK,MAAM,KAAK,kBAAS,CAAC,GAAG,CAAC,QAAQ,IAAI,kBAAS,CAAC,GAAG,CAAC,IAAI,IAAI,MAAM,GAAG,CAAC;YACxL,MAAM,UAAU,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;YAE7C,eAAM,CAAC,KAAK,CAAC,yBAAyB,CAAC,CAAC;QAE1C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC;IAGD,KAAK,CAAC,iBAAiB;QACrB,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;QAC9B,CAAC;gBAAS,CAAC;YACT,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACzB,CAAC;IACH,CAAC;IAGD,aAAa;QACX,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC;YAClC,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,kBAAS,CAAC,IAAI,CAAC,eAAe,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAE3E,OAAO;YACL,QAAQ,EAAE,IAAI,CAAC,YAAY,KAAK,IAAI;YACpC,QAAQ,EAAE,IAAI;YACd,QAAQ;SACT,CAAC;IACJ,CAAC;CACF;AAxQD,8CAwQC;AAGY,QAAA,UAAU,GAAG,IAAI,iBAAiB,EAAE,CAAC"}