"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.hostingRoutes = hostingRoutes;
const hosting_controller_1 = require("../controllers/hosting.controller");
const auth_1 = require("../middleware/auth");
async function hostingRoutes(fastify) {
    fastify.get('/plans', hosting_controller_1.getHostingPlansController);
    fastify.get('/plans/recommend', hosting_controller_1.getRecommendedPlanController);
    fastify.get('/plans/:planName', hosting_controller_1.getHostingPlanController);
    fastify.register(async function (fastify) {
        fastify.addHook('preHandler', auth_1.authMiddleware);
        fastify.post('/shared/users', hosting_controller_1.createSharedUserController);
        fastify.post('/shared/users/:userId/applications', hosting_controller_1.createApplicationController);
    }, { prefix: '/manage' });
}
//# sourceMappingURL=hosting.routes.js.map