{"version": 3, "file": "server-management.js", "sourceRoot": "", "sources": ["../../src/services/server-management.ts"], "names": [], "mappings": ";;AAsYA,gEAKC;AA3YD,4CAAyC;AACzC,wCAA0C;AAqE1C,MAAM,uBAAuB;IACV,gBAAgB,GAAG,eAAe,CAAC;IACnC,aAAa,GAAG,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;IAC7C,cAAc,GAAG,0BAA0B,CAAC;IAG7D,KAAK,CAAC,mBAAmB;QACvB,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;YAGjD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAEnE,OAAO;gBACL,EAAE,EAAE,oBAAoB;gBACxB,UAAU,EAAE,IAAI,CAAC,gBAAgB;gBACjC,QAAQ,EAAE,kBAAkB;gBAC5B,MAAM,EAAE,KAAK;gBACb,IAAI,EAAE,YAAY;gBAClB,EAAE,EAAE,cAAc;gBAClB,MAAM,EAAE,QAAQ;gBAChB,SAAS,EAAE,CAAC;gBACZ,MAAM,EAAE,IAAI;gBACZ,OAAO,EAAE,EAAE;gBACX,YAAY,EAAE,IAAI;gBAClB,YAAY,EAAE,IAAI;gBAClB,UAAU,EAAE,sBAAsB;gBAClC,WAAW,EAAE,MAAM,IAAI,CAAC,mBAAmB,EAAE;gBAC7C,SAAS,EAAE,EAAE;gBACb,YAAY,EAAE,OAAO,CAAC,YAAY;gBAClC,kBAAkB,EAAE,OAAO,CAAC,UAAU;gBACtC,oBAAoB,EAAE,OAAO,CAAC,YAAY;gBAC1C,aAAa,EAAE;oBACb,WAAW,EAAE,OAAO,CAAC,UAAU;oBAC/B,WAAW,EAAE,OAAO,CAAC,WAAW;iBACjC;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YACzD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAGD,KAAK,CAAC,gBAAgB,CAAC,QAAgB;QACrC,IAAI,CAAC;YAEH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;YAClD,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;YACxD,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YACpD,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;YAC1D,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;YACxD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;YAC9C,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAErD,OAAO;gBACL,SAAS,EAAE,QAAQ;gBACnB,YAAY,EAAE,WAAW;gBACzB,UAAU,EAAE,SAAS;gBACrB,UAAU,EAAE,YAAY,CAAC,QAAQ;gBACjC,WAAW,EAAE,YAAY,CAAC,QAAQ;gBAClC,YAAY,EAAE,WAAW;gBACzB,MAAM,EAAE,MAAM;gBACd,YAAY,EAAE,WAAW;aAC1B,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,oCAAoC,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;YACrE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAGO,KAAK,CAAC,WAAW,CAAC,QAAgB;QACxC,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAA,gBAAS,EAAC,YAAY,QAAQ,yEAAyE,CAAC,CAAC;YAC9H,OAAO,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC;QAC/C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,IAAI,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;YAC/C,OAAO,CAAC,CAAC;QACX,CAAC;IACH,CAAC;IAGO,KAAK,CAAC,cAAc,CAAC,QAAgB;QAC3C,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAA,gBAAS,EAAC,YAAY,QAAQ,6DAA6D,CAAC,CAAC;YAClH,OAAO,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC;QAC/C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,IAAI,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YAClD,OAAO,CAAC,CAAC;QACX,CAAC;IACH,CAAC;IAGO,KAAK,CAAC,YAAY,CAAC,QAAgB;QACzC,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAA,gBAAS,EAAC,YAAY,QAAQ,mDAAmD,CAAC,CAAC;YACxG,OAAO,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC;QAC/C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,IAAI,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YAChD,OAAO,CAAC,CAAC;QACX,CAAC;IACH,CAAC;IAGO,KAAK,CAAC,eAAe,CAAC,QAAgB;QAC5C,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAA,gBAAS,EAAC,YAAY,QAAQ,0DAA0D,CAAC,CAAC;YAC/G,MAAM,CAAC,QAAQ,EAAE,QAAQ,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAW,EAAE,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC;YACjH,OAAO,EAAE,QAAQ,EAAE,QAAQ,IAAI,CAAC,EAAE,QAAQ,EAAE,QAAQ,IAAI,CAAC,EAAE,CAAC;QAC9D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,IAAI,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YACnD,OAAO,EAAE,QAAQ,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC;QACtC,CAAC;IACH,CAAC;IAGO,KAAK,CAAC,cAAc,CAAC,QAAgB;QAC3C,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAA,gBAAS,EAAC,YAAY,QAAQ,+DAA+D,CAAC,CAAC;YACpH,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAW,EAAE,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,GAAW,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;QAC3H,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,IAAI,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YAClD,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACnB,CAAC;IACH,CAAC;IAGO,KAAK,CAAC,SAAS,CAAC,QAAgB;QACtC,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAA,gBAAS,EAAC,YAAY,QAAQ,wCAAwC,CAAC,CAAC;YAC7F,OAAO,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC;QAC/C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,IAAI,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;YAC5C,OAAO,CAAC,CAAC;QACX,CAAC;IACH,CAAC;IAGO,KAAK,CAAC,mBAAmB;QAC/B,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAA,gBAAS,EAAC,YAAY,IAAI,CAAC,gBAAgB,gBAAgB,CAAC,CAAC;YAClF,OAAO,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC;QAC7C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,IAAI,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YACxD,OAAO,CAAC,CAAC;QACX,CAAC;IACH,CAAC;IAGD,KAAK,CAAC,eAAe,CAAC,OAA+B;QACnD,IAAI,CAAC;YACH,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;gBACxB,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;YAClD,CAAC;YAED,eAAM,CAAC,IAAI,CAAC,gBAAgB,OAAO,CAAC,IAAI,qBAAqB,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;YAEhF,MAAM,aAAa,GAAG,MAAM,KAAK,CAAC,GAAG,IAAI,CAAC,cAAc,YAAY,EAAE;gBACpE,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE;oBACP,eAAe,EAAE,UAAU,IAAI,CAAC,aAAa,EAAE;oBAC/C,cAAc,EAAE,kBAAkB;iBACnC;gBACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;oBACnB,MAAM,EAAE,OAAO,CAAC,MAAM;oBACtB,IAAI,EAAE,OAAO,CAAC,IAAI;oBAClB,KAAK,EAAE,GAAG;oBACV,QAAQ,EAAE,OAAO,CAAC,QAAQ;oBAC1B,KAAK,EAAE,GAAG,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,OAAO,EAAE;oBAC3C,GAAG,EAAE,WAAW,OAAO,CAAC,IAAI,EAAE;oBAC9B,SAAS,EAAE,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC;oBAC9C,WAAW,EAAE,KAAK;oBAClB,sBAAsB,EAAE,KAAK;oBAC7B,YAAY,EAAE,IAAI;iBACnB,CAAC;aACH,CAAC,CAAC;YAEH,IAAI,CAAC,aAAa,CAAC,EAAE,EAAE,CAAC;gBACtB,MAAM,KAAK,GAAG,MAAM,aAAa,CAAC,IAAI,EAAE,CAAC;gBACzC,MAAM,IAAI,KAAK,CAAC,oBAAoB,KAAK,EAAE,CAAC,CAAC;YAC/C,CAAC;YAED,MAAM,SAAS,GAAG,MAAM,aAAa,CAAC,IAAI,EAAmB,CAAC;YAE9D,MAAM,UAAU,GAAe;gBAC7B,EAAE,EAAE,SAAS,CAAC,QAAQ,CAAC,EAAE;gBACzB,UAAU,EAAE,SAAS,CAAC,QAAQ,CAAC,OAAO,IAAI,SAAS;gBACnD,QAAQ,EAAE,OAAO,CAAC,QAAQ;gBAC1B,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,IAAI,EAAE,OAAO,CAAC,IAAI;gBAClB,EAAE,EAAE,kBAAkB;gBACtB,MAAM,EAAE,YAAY;gBACpB,SAAS,EAAE,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG;gBAC9C,MAAM,EAAE,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG;gBAC3C,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI;gBAC7C,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,SAAS;gBACvD,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI;gBAClD,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACpC,WAAW,EAAE,CAAC;gBACd,SAAS,EAAE,CAAC;aACb,CAAC;YAEF,eAAM,CAAC,IAAI,CAAC,oCAAoC,UAAU,CAAC,EAAE,EAAE,CAAC,CAAC;YACjE,OAAO,UAAU,CAAC;QACpB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACnD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAGO,gBAAgB,CAAC,IAAY;QACnC,OAAO;YACC,IAAI;;;;;;;;;;;;;;;;;;;;;;CAsBf,CAAC;IACA,CAAC;IAGO,YAAY,CAAC,IAAY;QAC/B,MAAM,KAAK,GAAwB;YACjC,YAAY,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;YAC1E,YAAY,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE;YAC3E,YAAY,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE;YAC5E,aAAa,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE;SAC/E,CAAC;QAEF,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;IAC5C,CAAC;IAGD,KAAK,CAAC,cAAc;QAClB,IAAI,CAAC;YACH,MAAM,OAAO,GAAiB,EAAE,CAAC;YAGjC,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;YACtD,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAG3B,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;gBACvB,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;gBAClD,OAAO,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC,CAAC;YAChC,CAAC;YAED,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;YAC/C,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAGO,KAAK,CAAC,eAAe;QAC3B,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,IAAI,CAAC,cAAc,YAAY,EAAE;gBAC/D,OAAO,EAAE;oBACP,eAAe,EAAE,UAAU,IAAI,CAAC,aAAa,EAAE;iBAChD;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;gBACjB,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;YACnD,CAAC;YAED,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAA4B,CAAC;YAE7D,OAAO,IAAI,CAAC,SAAS;iBAClB,MAAM,CAAC,CAAC,QAAuB,EAAE,EAAE,CAAC,QAAQ,CAAC,GAAG,EAAE,UAAU,CAAC,UAAU,CAAC,CAAC;iBACzE,GAAG,CAAC,CAAC,QAAuB,EAAE,EAAE;gBAC/B,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,YAAY,CAAC,CAAC;gBACnE,OAAO;oBACL,EAAE,EAAE,QAAQ,CAAC,EAAE;oBACf,UAAU,EAAE,QAAQ,CAAC,OAAO,IAAI,SAAS;oBACzC,QAAQ,EAAE,QAAQ,CAAC,QAAQ,IAAI,SAAS;oBACxC,MAAM,EAAE,QAAQ,CAAC,MAAM,IAAI,SAAS;oBACpC,IAAI,EAAE,QAAQ,CAAC,IAAI,IAAI,SAAS;oBAChC,EAAE,EAAE,QAAQ,CAAC,EAAE,IAAI,kBAAkB;oBACrC,MAAM,EAAE,CAAC,QAAQ,CAAC,aAAa,KAAK,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAsD;oBACrH,SAAS,EAAE,SAAS,CAAC,GAAG;oBACxB,MAAM,EAAE,SAAS,CAAC,GAAG;oBACrB,OAAO,EAAE,SAAS,CAAC,IAAI;oBACvB,YAAY,EAAE,SAAS,CAAC,SAAS;oBACjC,YAAY,EAAE,SAAS,CAAC,IAAI;oBAC5B,UAAU,EAAE,QAAQ,CAAC,YAAY,IAAI,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;oBAC7D,WAAW,EAAE,CAAC;oBACd,SAAS,EAAE,CAAC;iBACb,CAAC;YACJ,CAAC,CAAC,CAAC;QACP,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YACpD,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;CACF;AAGD,IAAI,uBAAgD,CAAC;AAErD,SAAgB,0BAA0B;IACxC,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAC7B,uBAAuB,GAAG,IAAI,uBAAuB,EAAE,CAAC;IAC1D,CAAC;IACD,OAAO,uBAAuB,CAAC;AACjC,CAAC"}