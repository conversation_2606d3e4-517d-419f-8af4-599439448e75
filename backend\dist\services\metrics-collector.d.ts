export declare class MetricsCollectorService {
    private collectionInterval;
    private isCollecting;
    constructor();
    startCollection(): void;
    stopCollection(): void;
    collectServerMetrics(): Promise<void>;
    collectUserUsageMetrics(): Promise<void>;
    private getCpuUsage;
    private getMemoryUsage;
    private getDiskUsage;
    private getLoadAverage;
    private getNetworkStats;
    cleanupOldMetrics(): Promise<void>;
}
export declare const metricsCollector: MetricsCollectorService;
//# sourceMappingURL=metrics-collector.d.ts.map