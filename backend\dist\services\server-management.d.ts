export interface ServerInfo {
    id: string;
    ip_address: string;
    hostname: string;
    region: string;
    plan: string;
    os: string;
    status: 'active' | 'pending' | 'suspended' | 'installing';
    cpu_count: number;
    ram_mb: number;
    disk_gb: number;
    bandwidth_gb: number;
    monthly_cost: number;
    created_at: string;
    users_count?: number;
    max_users?: number;
    load_average?: number[];
    disk_usage_percent?: number;
    memory_usage_percent?: number;
    network_usage?: {
        incoming_mb: number;
        outgoing_mb: number;
    };
}
export interface ServerMetrics {
    cpu_usage: number;
    memory_usage: number;
    disk_usage: number;
    network_in: number;
    network_out: number;
    load_average: number[];
    uptime: number;
    active_users: number;
}
export interface ProvisionServerRequest {
    plan: string;
    region: string;
    hostname: string;
    os?: string;
    user_id: string;
    tier: 'dedicated' | 'enterprise';
}
declare class ServerManagementService {
    private readonly SHARED_SERVER_IP;
    private readonly VULTR_API_KEY;
    private readonly VULTR_API_BASE;
    getSharedServerInfo(): Promise<ServerInfo>;
    getServerMetrics(serverIp: string): Promise<ServerMetrics>;
    private getCPUUsage;
    private getMemoryUsage;
    private getDiskUsage;
    private getNetworkStats;
    private getLoadAverage;
    private getUptime;
    private getActiveUsersCount;
    provisionServer(request: ProvisionServerRequest): Promise<ServerInfo>;
    private generateUserData;
    private getPlanSpecs;
    listAllServers(): Promise<ServerInfo[]>;
    private getVultrServers;
}
export declare function getServerManagementService(): ServerManagementService;
export {};
//# sourceMappingURL=server-management.d.ts.map