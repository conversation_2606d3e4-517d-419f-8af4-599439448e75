{"name": "achi<PERSON>-backend", "version": "1.0.0", "description": "Achidas Cloud Platform Backend API", "main": "dist/server.js", "scripts": {"dev": "tsx watch src/server.ts", "build": "tsc", "start": "node dist/server.js", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "keywords": ["cloud", "platform", "vultr", "hosting", "fastify", "typescript"], "author": "Achidas Team", "license": "MIT", "dependencies": {"@fastify/cors": "^9.0.1", "@fastify/helmet": "^11.1.1", "@fastify/jwt": "^8.0.1", "@fastify/mongodb": "^8.0.0", "@fastify/rate-limit": "^9.1.0", "@fastify/swagger": "^8.14.0", "@fastify/swagger-ui": "^2.1.0", "@types/mongoose": "^5.11.96", "axios": "^1.6.8", "bcryptjs": "^2.4.3", "dotenv": "^16.4.5", "fastify": "^4.26.2", "jsonwebtoken": "^9.0.2", "mongodb": "^6.5.0", "mongoose": "^8.16.1", "pino": "^8.19.0", "pino-pretty": "^11.0.0", "ssh2": "^1.16.0", "uuid": "^9.0.1", "zod": "^3.22.4"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/jest": "^29.5.12", "@types/jsonwebtoken": "^9.0.6", "@types/node": "^20.11.30", "@types/ssh2": "^1.15.5", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^7.3.1", "@typescript-eslint/parser": "^7.3.1", "eslint": "^8.57.0", "jest": "^29.7.0", "ts-jest": "^29.1.2", "tsx": "^4.7.1", "typescript": "^5.4.3"}, "engines": {"node": ">=18.0.0"}}