{"version": 3, "sources": ["webpack://SearchAddon/webpack/universalModuleDefinition", "webpack://SearchAddon/src/common/EventEmitter.ts", "webpack://SearchAddon/src/common/Lifecycle.ts", "webpack://SearchAddon/webpack/bootstrap", "webpack://SearchAddon/src/SearchAddon.ts"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"SearchAddon\"] = factory();\n\telse\n\t\troot[\"SearchAddon\"] = factory();\n})(self, () => {\nreturn ", "/**\n * Copyright (c) 2019 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { IDisposable } from 'common/Types';\n\ninterface IListener<T, U = void> {\n  (arg1: T, arg2: U): void;\n}\n\nexport interface IEvent<T, U = void> {\n  (listener: (arg1: T, arg2: U) => any): IDisposable;\n}\n\nexport interface IEventEmitter<T, U = void> {\n  event: IEvent<T, U>;\n  fire(arg1: T, arg2: U): void;\n  dispose(): void;\n}\n\nexport class EventEmitter<T, U = void> implements IEventEmitter<T, U> {\n  private _listeners: IListener<T, U>[] = [];\n  private _event?: IEvent<T, U>;\n  private _disposed: boolean = false;\n\n  public get event(): IEvent<T, U> {\n    if (!this._event) {\n      this._event = (listener: (arg1: T, arg2: U) => any) => {\n        this._listeners.push(listener);\n        const disposable = {\n          dispose: () => {\n            if (!this._disposed) {\n              for (let i = 0; i < this._listeners.length; i++) {\n                if (this._listeners[i] === listener) {\n                  this._listeners.splice(i, 1);\n                  return;\n                }\n              }\n            }\n          }\n        };\n        return disposable;\n      };\n    }\n    return this._event;\n  }\n\n  public fire(arg1: T, arg2: U): void {\n    const queue: IListener<T, U>[] = [];\n    for (let i = 0; i < this._listeners.length; i++) {\n      queue.push(this._listeners[i]);\n    }\n    for (let i = 0; i < queue.length; i++) {\n      queue[i].call(undefined, arg1, arg2);\n    }\n  }\n\n  public dispose(): void {\n    this.clearListeners();\n    this._disposed = true;\n  }\n\n  public clearListeners(): void {\n    if (this._listeners) {\n      this._listeners.length = 0;\n    }\n  }\n}\n\nexport function forwardEvent<T>(from: IEvent<T>, to: IEventEmitter<T>): IDisposable {\n  return from(e => to.fire(e));\n}\n\nexport function runAndSubscribe<T>(event: IEvent<T>, handler: (e: T | undefined) => any): IDisposable {\n  handler(undefined);\n  return event(e => handler(e));\n}\n", "/**\n * Copyright (c) 2018 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { IDisposable } from 'common/Types';\n\n/**\n * A base class that can be extended to provide convenience methods for managing the lifecycle of an\n * object and its components.\n */\nexport abstract class Disposable implements IDisposable {\n  protected _disposables: IDisposable[] = [];\n  protected _isDisposed: boolean = false;\n\n  /**\n   * Disposes the object, triggering the `dispose` method on all registered IDisposables.\n   */\n  public dispose(): void {\n    this._isDisposed = true;\n    for (const d of this._disposables) {\n      d.dispose();\n    }\n    this._disposables.length = 0;\n  }\n\n  /**\n   * Registers a disposable object.\n   * @param d The disposable to register.\n   * @returns The disposable.\n   */\n  public register<T extends IDisposable>(d: T): T {\n    this._disposables.push(d);\n    return d;\n  }\n\n  /**\n   * Unregisters a disposable object if it has been registered, if not do\n   * nothing.\n   * @param d The disposable to unregister.\n   */\n  public unregister<T extends IDisposable>(d: T): void {\n    const index = this._disposables.indexOf(d);\n    if (index !== -1) {\n      this._disposables.splice(index, 1);\n    }\n  }\n}\n\nexport class MutableDisposable<T extends IDisposable> implements IDisposable {\n  private _value?: T;\n  private _isDisposed = false;\n\n  /**\n   * Gets the value if it exists.\n   */\n  public get value(): T | undefined {\n    return this._isDisposed ? undefined : this._value;\n  }\n\n  /**\n   * Sets the value, disposing of the old value if it exists.\n   */\n  public set value(value: T | undefined) {\n    if (this._isDisposed || value === this._value) {\n      return;\n    }\n    this._value?.dispose();\n    this._value = value;\n  }\n\n  /**\n   * Resets the stored value and disposes of the previously stored value.\n   */\n  public clear(): void {\n    this.value = undefined;\n  }\n\n  public dispose(): void {\n    this._isDisposed = true;\n    this._value?.dispose();\n    this._value = undefined;\n  }\n}\n\n/**\n * Wrap a function in a disposable.\n */\nexport function toDisposable(f: () => void): IDisposable {\n  return { dispose: f };\n}\n\n/**\n * Dispose of all disposables in an array and set its length to 0.\n */\nexport function disposeArray(disposables: IDisposable[]): void {\n  for (const d of disposables) {\n    d.dispose();\n  }\n  disposables.length = 0;\n}\n\n/**\n * Creates a disposable that will dispose of an array of disposables when disposed.\n */\nexport function getDisposeArrayDisposable(array: IDisposable[]): IDisposable {\n  return { dispose: () => disposeArray(array) };\n}\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "/**\n * Copyright (c) 2017 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport type { Terminal, IDisposable, ITerminalAddon, IDecoration } from '@xterm/xterm';\nimport type { SearchAddon as ISearchApi } from '@xterm/addon-search';\nimport { EventEmitter } from 'common/EventEmitter';\nimport { Disposable, toDisposable, disposeArray, MutableDisposable, getDisposeArrayDisposable } from 'common/Lifecycle';\n\nexport interface ISearchOptions {\n  regex?: boolean;\n  wholeWord?: boolean;\n  caseSensitive?: boolean;\n  incremental?: boolean;\n  decorations?: ISearchDecorationOptions;\n  noScroll?: boolean;\n}\n\ninterface ISearchDecorationOptions {\n  matchBackground?: string;\n  matchBorder?: string;\n  matchOverviewRuler: string;\n  activeMatchBackground?: string;\n  activeMatchBorder?: string;\n  activeMatchColorOverviewRuler: string;\n}\n\nexport interface ISearchPosition {\n  startCol: number;\n  startRow: number;\n}\n\nexport interface ISearchAddonOptions {\n  highlightLimit: number;\n}\n\nexport interface ISearchResult {\n  term: string;\n  col: number;\n  row: number;\n  size: number;\n}\n\ntype LineCacheEntry = [\n  /**\n   * The string representation of a line (as opposed to the buffer cell representation).\n   */\n  lineAsString: string,\n  /**\n   * The offsets where each line starts when the entry describes a wrapped line.\n   */\n  lineOffsets: number[]\n];\n\ninterface IHighlight extends IDisposable {\n  decoration: IDecoration;\n  match: ISearchResult;\n}\n\nconst NON_WORD_CHARACTERS = ' ~!@#$%^&*()+`-=[]{}|\\\\;:\"\\',./<>?';\nconst LINES_CACHE_TIME_TO_LIVE = 15 * 1000; // 15 secs\nconst DEFAULT_HIGHLIGHT_LIMIT = 1000;\n\nexport class SearchAddon extends Disposable implements ITerminalAddon , ISearchApi {\n  private _terminal: Terminal | undefined;\n  private _cachedSearchTerm: string | undefined;\n  private _highlightedLines: Set<number> = new Set();\n  private _highlightDecorations: IHighlight[] = [];\n  private _selectedDecoration: MutableDisposable<IHighlight> = this.register(new MutableDisposable());\n  private _highlightLimit: number;\n  private _lastSearchOptions: ISearchOptions | undefined;\n  private _highlightTimeout: number | undefined;\n  /**\n   * translateBufferLineToStringWithWrap is a fairly expensive call.\n   * We memoize the calls into an array that has a time based ttl.\n   * _linesCache is also invalidated when the terminal cursor moves.\n   */\n  private _linesCache: LineCacheEntry[] | undefined;\n  private _linesCacheTimeoutId = 0;\n  private _linesCacheDisposables = new MutableDisposable();\n\n  private readonly _onDidChangeResults = this.register(new EventEmitter<{ resultIndex: number, resultCount: number }>());\n  public readonly onDidChangeResults = this._onDidChangeResults.event;\n\n  constructor(options?: Partial<ISearchAddonOptions>) {\n    super();\n\n    this._highlightLimit = options?.highlightLimit ?? DEFAULT_HIGHLIGHT_LIMIT;\n  }\n\n  public activate(terminal: Terminal): void {\n    this._terminal = terminal;\n    this.register(this._terminal.onWriteParsed(() => this._updateMatches()));\n    this.register(this._terminal.onResize(() => this._updateMatches()));\n    this.register(toDisposable(() => this.clearDecorations()));\n  }\n\n  private _updateMatches(): void {\n    if (this._highlightTimeout) {\n      window.clearTimeout(this._highlightTimeout);\n    }\n    if (this._cachedSearchTerm && this._lastSearchOptions?.decorations) {\n      this._highlightTimeout = setTimeout(() => {\n        const term = this._cachedSearchTerm;\n        this._cachedSearchTerm = undefined;\n        this.findPrevious(term!, { ...this._lastSearchOptions, incremental: true, noScroll: true });\n      }, 200);\n    }\n  }\n\n  public clearDecorations(retainCachedSearchTerm?: boolean): void {\n    this._selectedDecoration.clear();\n    disposeArray(this._highlightDecorations);\n    this._highlightDecorations = [];\n    this._highlightedLines.clear();\n    if (!retainCachedSearchTerm) {\n      this._cachedSearchTerm = undefined;\n    }\n  }\n\n  public clearActiveDecoration(): void {\n    this._selectedDecoration.clear();\n  }\n\n  /**\n   * Find the next instance of the term, then scroll to and select it. If it\n   * doesn't exist, do nothing.\n   * @param term The search term.\n   * @param searchOptions Search options.\n   * @returns Whether a result was found.\n   */\n  public findNext(term: string, searchOptions?: ISearchOptions): boolean {\n    if (!this._terminal) {\n      throw new Error('Cannot use addon until it has been loaded');\n    }\n    const didOptionsChanged = this._lastSearchOptions ? this._didOptionsChange(this._lastSearchOptions, searchOptions) : true;\n    this._lastSearchOptions = searchOptions;\n    if (searchOptions?.decorations) {\n      if (this._cachedSearchTerm === undefined || term !== this._cachedSearchTerm || didOptionsChanged) {\n        this._highlightAllMatches(term, searchOptions);\n      }\n    }\n\n    const found = this._findNextAndSelect(term, searchOptions);\n    this._fireResults(searchOptions);\n    this._cachedSearchTerm = term;\n\n    return found;\n  }\n\n  private _highlightAllMatches(term: string, searchOptions: ISearchOptions): void {\n    if (!this._terminal) {\n      throw new Error('Cannot use addon until it has been loaded');\n    }\n    if (!term || term.length === 0) {\n      this.clearDecorations();\n      return;\n    }\n    searchOptions = searchOptions || {};\n\n    // new search, clear out the old decorations\n    this.clearDecorations(true);\n\n    const searchResultsWithHighlight: ISearchResult[] = [];\n    let prevResult: ISearchResult | undefined = undefined;\n    let result = this._find(term, 0, 0, searchOptions);\n    while (result && (prevResult?.row !== result.row || prevResult?.col !== result.col)) {\n      if (searchResultsWithHighlight.length >= this._highlightLimit) {\n        break;\n      }\n      prevResult = result;\n      searchResultsWithHighlight.push(prevResult);\n      result = this._find(\n        term,\n        prevResult.col + prevResult.term.length >= this._terminal.cols ? prevResult.row + 1 : prevResult.row,\n        prevResult.col + prevResult.term.length >= this._terminal.cols ? 0 : prevResult.col + 1,\n        searchOptions\n      );\n    }\n    for (const match of searchResultsWithHighlight) {\n      const decoration = this._createResultDecoration(match, searchOptions.decorations!);\n      if (decoration) {\n        this._highlightedLines.add(decoration.marker.line);\n        this._highlightDecorations.push({ decoration, match, dispose() { decoration.dispose(); } });\n      }\n    }\n  }\n\n  private _find(term: string, startRow: number, startCol: number, searchOptions?: ISearchOptions): ISearchResult | undefined {\n    if (!this._terminal || !term || term.length === 0) {\n      this._terminal?.clearSelection();\n      this.clearDecorations();\n      return undefined;\n    }\n    if (startCol > this._terminal.cols) {\n      throw new Error(`Invalid col: ${startCol} to search in terminal of ${this._terminal.cols} cols`);\n    }\n\n    let result: ISearchResult | undefined = undefined;\n\n    this._initLinesCache();\n\n    const searchPosition: ISearchPosition = {\n      startRow,\n      startCol\n    };\n\n    // Search startRow\n    result = this._findInLine(term, searchPosition, searchOptions);\n    // Search from startRow + 1 to end\n    if (!result) {\n\n      for (let y = startRow + 1; y < this._terminal.buffer.active.baseY + this._terminal.rows; y++) {\n        searchPosition.startRow = y;\n        searchPosition.startCol = 0;\n        // If the current line is wrapped line, increase index of column to ignore the previous scan\n        // Otherwise, reset beginning column index to zero with set new unwrapped line index\n        result = this._findInLine(term, searchPosition, searchOptions);\n        if (result) {\n          break;\n        }\n      }\n    }\n    return result;\n  }\n\n  private _findNextAndSelect(term: string, searchOptions?: ISearchOptions): boolean {\n    if (!this._terminal || !term || term.length === 0) {\n      this._terminal?.clearSelection();\n      this.clearDecorations();\n      return false;\n    }\n\n    const prevSelectedPos = this._terminal.getSelectionPosition();\n    this._terminal.clearSelection();\n\n    let startCol = 0;\n    let startRow = 0;\n    if (prevSelectedPos) {\n      if (this._cachedSearchTerm === term) {\n        startCol = prevSelectedPos.end.x;\n        startRow = prevSelectedPos.end.y;\n      } else {\n        startCol = prevSelectedPos.start.x;\n        startRow = prevSelectedPos.start.y;\n      }\n    }\n\n    this._initLinesCache();\n\n    const searchPosition: ISearchPosition = {\n      startRow,\n      startCol\n    };\n\n    // Search startRow\n    let result = this._findInLine(term, searchPosition, searchOptions);\n    // Search from startRow + 1 to end\n    if (!result) {\n\n      for (let y = startRow + 1; y < this._terminal.buffer.active.baseY + this._terminal.rows; y++) {\n        searchPosition.startRow = y;\n        searchPosition.startCol = 0;\n        // If the current line is wrapped line, increase index of column to ignore the previous scan\n        // Otherwise, reset beginning column index to zero with set new unwrapped line index\n        result = this._findInLine(term, searchPosition, searchOptions);\n        if (result) {\n          break;\n        }\n      }\n    }\n    // If we hit the bottom and didn't search from the very top wrap back up\n    if (!result && startRow !== 0) {\n      for (let y = 0; y < startRow; y++) {\n        searchPosition.startRow = y;\n        searchPosition.startCol = 0;\n        result = this._findInLine(term, searchPosition, searchOptions);\n        if (result) {\n          break;\n        }\n      }\n    }\n\n    // If there is only one result, wrap back and return selection if it exists.\n    if (!result && prevSelectedPos) {\n      searchPosition.startRow = prevSelectedPos.start.y;\n      searchPosition.startCol = 0;\n      result = this._findInLine(term, searchPosition, searchOptions);\n    }\n\n    // Set selection and scroll if a result was found\n    return this._selectResult(result, searchOptions?.decorations, searchOptions?.noScroll);\n  }\n  /**\n   * Find the previous instance of the term, then scroll to and select it. If it\n   * doesn't exist, do nothing.\n   * @param term The search term.\n   * @param searchOptions Search options.\n   * @returns Whether a result was found.\n   */\n  public findPrevious(term: string, searchOptions?: ISearchOptions): boolean {\n    if (!this._terminal) {\n      throw new Error('Cannot use addon until it has been loaded');\n    }\n    const didOptionsChanged = this._lastSearchOptions ? this._didOptionsChange(this._lastSearchOptions, searchOptions) : true;\n    this._lastSearchOptions = searchOptions;\n    if (searchOptions?.decorations) {\n      if (this._cachedSearchTerm === undefined || term !== this._cachedSearchTerm || didOptionsChanged) {\n        this._highlightAllMatches(term, searchOptions);\n      }\n    }\n\n    const found = this._findPreviousAndSelect(term, searchOptions);\n    this._fireResults(searchOptions);\n    this._cachedSearchTerm = term;\n\n    return found;\n  }\n\n  private _didOptionsChange(lastSearchOptions: ISearchOptions, searchOptions?: ISearchOptions): boolean {\n    if (!searchOptions) {\n      return false;\n    }\n    if (lastSearchOptions.caseSensitive !== searchOptions.caseSensitive) {\n      return true;\n    }\n    if (lastSearchOptions.regex !== searchOptions.regex) {\n      return true;\n    }\n    if (lastSearchOptions.wholeWord !== searchOptions.wholeWord) {\n      return true;\n    }\n    return false;\n  }\n\n  private _fireResults(searchOptions?: ISearchOptions): void {\n    if (searchOptions?.decorations) {\n      let resultIndex = -1;\n      if (this._selectedDecoration.value) {\n        const selectedMatch = this._selectedDecoration.value.match;\n        for (let i = 0; i < this._highlightDecorations.length; i++) {\n          const match = this._highlightDecorations[i].match;\n          if (match.row === selectedMatch.row && match.col === selectedMatch.col && match.size === selectedMatch.size) {\n            resultIndex = i;\n            break;\n          }\n        }\n      }\n      this._onDidChangeResults.fire({ resultIndex, resultCount: this._highlightDecorations.length });\n    }\n  }\n\n  private _findPreviousAndSelect(term: string, searchOptions?: ISearchOptions): boolean {\n    if (!this._terminal) {\n      throw new Error('Cannot use addon until it has been loaded');\n    }\n    if (!this._terminal || !term || term.length === 0) {\n      this._terminal?.clearSelection();\n      this.clearDecorations();\n      return false;\n    }\n\n    const prevSelectedPos = this._terminal.getSelectionPosition();\n    this._terminal.clearSelection();\n\n    let startRow = this._terminal.buffer.active.baseY + this._terminal.rows - 1;\n    let startCol = this._terminal.cols;\n    const isReverseSearch = true;\n\n    this._initLinesCache();\n    const searchPosition: ISearchPosition = {\n      startRow,\n      startCol\n    };\n\n    let result: ISearchResult | undefined;\n    if (prevSelectedPos) {\n      searchPosition.startRow = startRow = prevSelectedPos.start.y;\n      searchPosition.startCol = startCol = prevSelectedPos.start.x;\n      if (this._cachedSearchTerm !== term) {\n        // Try to expand selection to right first.\n        result = this._findInLine(term, searchPosition, searchOptions, false);\n        if (!result) {\n          // If selection was not able to be expanded to the right, then try reverse search\n          searchPosition.startRow = startRow = prevSelectedPos.end.y;\n          searchPosition.startCol = startCol = prevSelectedPos.end.x;\n        }\n      }\n    }\n\n    if (!result) {\n      result = this._findInLine(term, searchPosition, searchOptions, isReverseSearch);\n    }\n\n    // Search from startRow - 1 to top\n    if (!result) {\n      searchPosition.startCol = Math.max(searchPosition.startCol, this._terminal.cols);\n      for (let y = startRow - 1; y >= 0; y--) {\n        searchPosition.startRow = y;\n        result = this._findInLine(term, searchPosition, searchOptions, isReverseSearch);\n        if (result) {\n          break;\n        }\n      }\n    }\n    // If we hit the top and didn't search from the very bottom wrap back down\n    if (!result && startRow !== (this._terminal.buffer.active.baseY + this._terminal.rows - 1)) {\n      for (let y = (this._terminal.buffer.active.baseY + this._terminal.rows - 1); y >= startRow; y--) {\n        searchPosition.startRow = y;\n        result = this._findInLine(term, searchPosition, searchOptions, isReverseSearch);\n        if (result) {\n          break;\n        }\n      }\n    }\n\n    // Set selection and scroll if a result was found\n    return this._selectResult(result, searchOptions?.decorations, searchOptions?.noScroll);\n  }\n\n  /**\n   * Sets up a line cache with a ttl\n   */\n  private _initLinesCache(): void {\n    const terminal = this._terminal!;\n    if (!this._linesCache) {\n      this._linesCache = new Array(terminal.buffer.active.length);\n      this._linesCacheDisposables.value = getDisposeArrayDisposable([\n        terminal.onLineFeed(() => this._destroyLinesCache()),\n        terminal.onCursorMove(() => this._destroyLinesCache()),\n        terminal.onResize(() => this._destroyLinesCache())\n      ]);\n    }\n\n    window.clearTimeout(this._linesCacheTimeoutId);\n    this._linesCacheTimeoutId = window.setTimeout(() => this._destroyLinesCache(), LINES_CACHE_TIME_TO_LIVE);\n  }\n\n  private _destroyLinesCache(): void {\n    this._linesCache = undefined;\n    this._linesCacheDisposables.clear();\n    if (this._linesCacheTimeoutId) {\n      window.clearTimeout(this._linesCacheTimeoutId);\n      this._linesCacheTimeoutId = 0;\n    }\n  }\n\n  /**\n   * A found substring is a whole word if it doesn't have an alphanumeric character directly\n   * adjacent to it.\n   * @param searchIndex starting indext of the potential whole word substring\n   * @param line entire string in which the potential whole word was found\n   * @param term the substring that starts at searchIndex\n   */\n  private _isWholeWord(searchIndex: number, line: string, term: string): boolean {\n    return ((searchIndex === 0) || (NON_WORD_CHARACTERS.includes(line[searchIndex - 1]))) &&\n      (((searchIndex + term.length) === line.length) || (NON_WORD_CHARACTERS.includes(line[searchIndex + term.length])));\n  }\n\n  /**\n   * Searches a line for a search term. Takes the provided terminal line and searches the text line,\n   * which may contain subsequent terminal lines if the text is wrapped. If the provided line number\n   * is part of a wrapped text line that started on an earlier line then it is skipped since it will\n   * be properly searched when the terminal line that the text starts on is searched.\n   * @param term The search term.\n   * @param searchPosition The position to start the search.\n   * @param searchOptions Search options.\n   * @param isReverseSearch Whether the search should start from the right side of the terminal and\n   * search to the left.\n   * @returns The search result if it was found.\n   */\n  protected _findInLine(term: string, searchPosition: ISearchPosition, searchOptions: ISearchOptions = {}, isReverseSearch: boolean = false): ISearchResult | undefined {\n    const terminal = this._terminal!;\n    const row = searchPosition.startRow;\n    const col = searchPosition.startCol;\n\n    // Ignore wrapped lines, only consider on unwrapped line (first row of command string).\n    const firstLine = terminal.buffer.active.getLine(row);\n    if (firstLine?.isWrapped) {\n      if (isReverseSearch) {\n        searchPosition.startCol += terminal.cols;\n        return;\n      }\n\n      // This will iterate until we find the line start.\n      // When we find it, we will search using the calculated start column.\n      searchPosition.startRow--;\n      searchPosition.startCol += terminal.cols;\n      return this._findInLine(term, searchPosition, searchOptions);\n    }\n    let cache = this._linesCache?.[row];\n    if (!cache) {\n      cache = this._translateBufferLineToStringWithWrap(row, true);\n      if (this._linesCache) {\n        this._linesCache[row] = cache;\n      }\n    }\n    const [stringLine, offsets] = cache;\n\n    const offset = this._bufferColsToStringOffset(row, col);\n    const searchTerm = searchOptions.caseSensitive ? term : term.toLowerCase();\n    const searchStringLine = searchOptions.caseSensitive ? stringLine : stringLine.toLowerCase();\n\n    let resultIndex = -1;\n    if (searchOptions.regex) {\n      const searchRegex = RegExp(searchTerm, 'g');\n      let foundTerm: RegExpExecArray | null;\n      if (isReverseSearch) {\n        // This loop will get the resultIndex of the _last_ regex match in the range 0..offset\n        while (foundTerm = searchRegex.exec(searchStringLine.slice(0, offset))) {\n          resultIndex = searchRegex.lastIndex - foundTerm[0].length;\n          term = foundTerm[0];\n          searchRegex.lastIndex -= (term.length - 1);\n        }\n      } else {\n        foundTerm = searchRegex.exec(searchStringLine.slice(offset));\n        if (foundTerm && foundTerm[0].length > 0) {\n          resultIndex = offset + (searchRegex.lastIndex - foundTerm[0].length);\n          term = foundTerm[0];\n        }\n      }\n    } else {\n      if (isReverseSearch) {\n        if (offset - searchTerm.length >= 0) {\n          resultIndex = searchStringLine.lastIndexOf(searchTerm, offset - searchTerm.length);\n        }\n      } else {\n        resultIndex = searchStringLine.indexOf(searchTerm, offset);\n      }\n    }\n\n    if (resultIndex >= 0) {\n      if (searchOptions.wholeWord && !this._isWholeWord(resultIndex, searchStringLine, term)) {\n        return;\n      }\n\n      // Adjust the row number and search index if needed since a \"line\" of text can span multiple\n      // rows\n      let startRowOffset = 0;\n      while (startRowOffset < offsets.length - 1 && resultIndex >= offsets[startRowOffset + 1]) {\n        startRowOffset++;\n      }\n      let endRowOffset = startRowOffset;\n      while (endRowOffset < offsets.length - 1 && resultIndex + term.length >= offsets[endRowOffset + 1]) {\n        endRowOffset++;\n      }\n      const startColOffset = resultIndex - offsets[startRowOffset];\n      const endColOffset = resultIndex + term.length - offsets[endRowOffset];\n      const startColIndex = this._stringLengthToBufferSize(row + startRowOffset, startColOffset);\n      const endColIndex = this._stringLengthToBufferSize(row + endRowOffset, endColOffset);\n      const size = endColIndex - startColIndex + terminal.cols * (endRowOffset - startRowOffset);\n\n      return {\n        term,\n        col: startColIndex,\n        row: row + startRowOffset,\n        size\n      };\n    }\n  }\n\n  private _stringLengthToBufferSize(row: number, offset: number): number {\n    const line = this._terminal!.buffer.active.getLine(row);\n    if (!line) {\n      return 0;\n    }\n    for (let i = 0; i < offset; i++) {\n      const cell = line.getCell(i);\n      if (!cell) {\n        break;\n      }\n      // Adjust the searchIndex to normalize emoji into single chars\n      const char = cell.getChars();\n      if (char.length > 1) {\n        offset -= char.length - 1;\n      }\n      // Adjust the searchIndex for empty characters following wide unicode\n      // chars (eg. CJK)\n      const nextCell = line.getCell(i + 1);\n      if (nextCell && nextCell.getWidth() === 0) {\n        offset++;\n      }\n    }\n    return offset;\n  }\n\n  private _bufferColsToStringOffset(startRow: number, cols: number): number {\n    const terminal = this._terminal!;\n    let lineIndex = startRow;\n    let offset = 0;\n    let line = terminal.buffer.active.getLine(lineIndex);\n    while (cols > 0 && line) {\n      for (let i = 0; i < cols && i < terminal.cols; i++) {\n        const cell = line.getCell(i);\n        if (!cell) {\n          break;\n        }\n        if (cell.getWidth()) {\n          // Treat null characters as whitespace to align with the translateToString API\n          offset += cell.getCode() === 0 ? 1 : cell.getChars().length;\n        }\n      }\n      lineIndex++;\n      line = terminal.buffer.active.getLine(lineIndex);\n      if (line && !line.isWrapped) {\n        break;\n      }\n      cols -= terminal.cols;\n    }\n    return offset;\n  }\n\n  /**\n   * Translates a buffer line to a string, including subsequent lines if they are wraps.\n   * Wide characters will count as two columns in the resulting string. This\n   * function is useful for getting the actual text underneath the raw selection\n   * position.\n   * @param lineIndex The index of the line being translated.\n   * @param trimRight Whether to trim whitespace to the right.\n   */\n  private _translateBufferLineToStringWithWrap(lineIndex: number, trimRight: boolean): LineCacheEntry {\n    const terminal = this._terminal!;\n    const strings = [];\n    const lineOffsets = [0];\n    let line = terminal.buffer.active.getLine(lineIndex);\n    while (line) {\n      const nextLine = terminal.buffer.active.getLine(lineIndex + 1);\n      const lineWrapsToNext = nextLine ? nextLine.isWrapped : false;\n      let string = line.translateToString(!lineWrapsToNext && trimRight);\n      if (lineWrapsToNext && nextLine) {\n        const lastCell = line.getCell(line.length - 1);\n        const lastCellIsNull = lastCell && lastCell.getCode() === 0 && lastCell.getWidth() === 1;\n        // a wide character wrapped to the next line\n        if (lastCellIsNull && nextLine.getCell(0)?.getWidth() === 2) {\n          string = string.slice(0, -1);\n        }\n      }\n      strings.push(string);\n      if (lineWrapsToNext) {\n        lineOffsets.push(lineOffsets[lineOffsets.length - 1] + string.length);\n      } else {\n        break;\n      }\n      lineIndex++;\n      line = nextLine;\n    }\n    return [strings.join(''), lineOffsets];\n  }\n\n  /**\n   * Selects and scrolls to a result.\n   * @param result The result to select.\n   * @returns Whether a result was selected.\n   */\n  private _selectResult(result: ISearchResult | undefined, options?: ISearchDecorationOptions, noScroll?: boolean): boolean {\n    const terminal = this._terminal!;\n    this._selectedDecoration.clear();\n    if (!result) {\n      terminal.clearSelection();\n      return false;\n    }\n    terminal.select(result.col, result.row, result.size);\n    if (options) {\n      const marker = terminal.registerMarker(-terminal.buffer.active.baseY - terminal.buffer.active.cursorY + result.row);\n      if (marker) {\n        const decoration = terminal.registerDecoration({\n          marker,\n          x: result.col,\n          width: result.size,\n          backgroundColor: options.activeMatchBackground,\n          layer: 'top',\n          overviewRulerOptions: {\n            color: options.activeMatchColorOverviewRuler\n          }\n        });\n        if (decoration) {\n          const disposables: IDisposable[] = [];\n          disposables.push(marker);\n          disposables.push(decoration.onRender((e) => this._applyStyles(e, options.activeMatchBorder, true)));\n          disposables.push(decoration.onDispose(() => disposeArray(disposables)));\n          this._selectedDecoration.value = { decoration, match: result, dispose() { decoration.dispose(); } };\n        }\n      }\n    }\n\n    if (!noScroll) {\n      // If it is not in the viewport then we scroll else it just gets selected\n      if (result.row >= (terminal.buffer.active.viewportY + terminal.rows) || result.row < terminal.buffer.active.viewportY) {\n        let scroll = result.row - terminal.buffer.active.viewportY;\n        scroll -= Math.floor(terminal.rows / 2);\n        terminal.scrollLines(scroll);\n      }\n    }\n    return true;\n  }\n\n  /**\n   * Applies styles to the decoration when it is rendered.\n   * @param element The decoration's element.\n   * @param borderColor The border color to apply.\n   * @param isActiveResult Whether the element is part of the active search result.\n   * @returns\n   */\n  private _applyStyles(element: HTMLElement, borderColor: string | undefined, isActiveResult: boolean): void {\n    if (!element.classList.contains('xterm-find-result-decoration')) {\n      element.classList.add('xterm-find-result-decoration');\n      if (borderColor) {\n        element.style.outline = `1px solid ${borderColor}`;\n      }\n    }\n    if (isActiveResult) {\n      element.classList.add('xterm-find-active-result-decoration');\n    }\n  }\n\n  /**\n   * Creates a decoration for the result and applies styles\n   * @param result the search result for which to create the decoration\n   * @param options the options for the decoration\n   * @returns the {@link IDecoration} or undefined if the marker has already been disposed of\n   */\n  private _createResultDecoration(result: ISearchResult, options: ISearchDecorationOptions): IDecoration | undefined {\n    const terminal = this._terminal!;\n    const marker = terminal.registerMarker(-terminal.buffer.active.baseY - terminal.buffer.active.cursorY + result.row);\n    if (!marker) {\n      return undefined;\n    }\n    const findResultDecoration = terminal.registerDecoration({\n      marker,\n      x: result.col,\n      width: result.size,\n      backgroundColor: options.matchBackground,\n      overviewRulerOptions: this._highlightedLines.has(marker.line) ? undefined : {\n        color: options.matchOverviewRuler,\n        position: 'center'\n      }\n    });\n    if (findResultDecoration) {\n      const disposables: IDisposable[] = [];\n      disposables.push(marker);\n      disposables.push(findResultDecoration.onRender((e) => this._applyStyles(e, options.matchBorder, false)));\n      disposables.push(findResultDecoration.onDispose(() => disposeArray(disposables)));\n    }\n    return findResultDecoration;\n  }\n}\n"], "mappings": ";;;;;;;KAAA,SAA2CA,GAAMC,GAAAA;AAC1B,kBAAA,OAAZC,WAA0C,YAAA,OAAXC,SACxCA,OAAOD,UAAUD,EAAAA,IACQ,cAAA,OAAXG,UAAyBA,OAAOC,MAC9CD,OAAO,CAAA,GAAIH,CAAAA,IACe,YAAA,OAAZC,UACdA,QAAqB,cAAID,EAAAA,IAEzBD,EAAkB,cAAIC,EAAAA;IACvB,EAAEK,MAAM,OAAA,MAAA;AAAA;AAAA,UAAA,IAAA,EAAA,KAAA,CAAAC,IAAAC,OAAA;AAAA,eAAA,eAAAA,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,kBAAAA,GAAA,eAAAA,GAAA,eAAA,QCYTA,GAAA,eAAA,MAAA;UAAA,cAAA;AACU,iBAAAC,aAAgC,CAAA,GAEhC,KAAAC,YAAAA;UA4CV;UA1CE,IAAA,QAAWC;AAmBT,mBAlBKC,KAAKC,WACRD,KAAKC,SAAUC,CAAAA,QACbF,KAAKH,WAAWM,KAAKD,EAAAA,GACF,EACjBE,SAAS,MAAA;AACP,kBAAA,CAAKJ,KAAKF;AACR,yBAASO,KAAI,GAAGA,KAAIL,KAAKH,WAAWS,QAAQD,KAC1C,KAAIL,KAAKH,WAAWQ,EAAAA,MAAOH,GAEzB,QAAA,KADAF,KAAKH,WAAWU,OAAOF,IAAG,CAAA;;YAAA,EAAA,KAUjCL,KAAKC;UACd;UAEO,KAAKO,IAASC,IAAAA;AACnB,kBAAMC,KAA2B,CAAA;AACjC,qBAASL,KAAI,GAAGA,KAAIL,KAAKH,WAAWS,QAAQD,KAC1CK,CAAAA,GAAMP,KAAKH,KAAKH,WAAWQ,EAAAA,CAAAA;AAE7B,qBAASA,KAAI,GAAGA,KAAIK,GAAMJ,QAAQD,KAChCK,CAAAA,GAAML,EAAAA,EAAGM,KAAAA,QAAgBH,IAAMC,EAAAA;UAEnC;UAEO,UAAAL;AACLJ,iBAAKY,eAAAA,GACLZ,KAAKF,YAAAA;UACP;UAEO,iBAAAc;AACDZ,iBAAKH,eACPG,KAAKH,WAAWS,SAAS;UAE7B;QAAA,GAGFV,GAAA,eAAA,SAAgCiB,IAAiBC,IAAAA;AAC/C,iBAAOD,GAAKlB,CAAAA,OAAKmB,GAAGC,KAAKpB,EAAAA,CAAAA;QAC3B,GAEAC,GAAA,kBAAA,SAAmCG,IAAkBiB,IAAAA;AAEnD,iBADAA,GAAAA,MAAQC,GACDlB,GAAMJ,CAAAA,OAAKqB,GAAQrB,EAAAA,CAAAA;QAC5B;MAAA,GAAA,KAAA,CAAAA,IAAAC,OAAA;ACkBA,iBAAgBsB,GAAaC,IAAAA;AAC3B,qBAAWC,MAAKD,GACdC,CAAAA,GAAEhB,QAAAA;AAEJe,UAAAA,GAAYb,SAAS;QACvB;AAAA,eAAA,eAAAV,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,4BAAAA,GAAA,eAAAA,GAAA,eAAAA,GAAA,oBAAAA,GAAA,aAAA,QAzFAA,GAAA,aAAA,MAAA;UAAA,cAAA;AACY,iBAAAyB,eAA8B,CAAA,GAC9B,KAAAC,cAAAA;UAkCZ;UA7BS,UAAAlB;AACLJ,iBAAKsB,cAAAA;AACL,uBAAWF,MAAKpB,KAAKqB,aACnBD,CAAAA,GAAEhB,QAAAA;AAEJJ,iBAAKqB,aAAaf,SAAS;UAC7B;UAOO,SAAgCc,IAAAA;AAErC,mBADApB,KAAKqB,aAAalB,KAAKiB,EAAAA,GAChBA;UACT;UAOO,WAAkCA,IAAAA;AACvC,kBAAMG,KAAQvB,KAAKqB,aAAaG,QAAQJ,EAAAA;AAAAA,mBACpCG,MACFvB,KAAKqB,aAAad,OAAOgB,IAAO,CAAA;UAEpC;QAAA,GAGF3B,GAAA,oBAAA,MAAA;UAAA,cAAA;AAEU,iBAAA0B,cAAAA;UAgCV;UA3BE,IAAA,QAAWG;AACT,mBAAOzB,KAAKsB,cAAAA,SAA0BtB,KAAK0B;UAC7C;UAKA,IAAA,MAAiBD,IAAAA;;AACXzB,iBAAKsB,eAAeG,OAAUzB,KAAK0B,YAGvC1B,UAAK0B,WAAL1B,mBAAaI,WACbJ,KAAK0B,SAASD;UAChB;UAKO,QAAAE;AACL3B,iBAAKyB,QAAAA;UACP;UAEO,UAAArB;;AACLJ,iBAAKsB,cAAAA,OACLtB,UAAK0B,WAAL1B,mBAAaI,WACbJ,KAAK0B,SAAAA;UACP;QAAA,GAMF9B,GAAA,eAAA,SAA6BgC,IAAAA;AAC3B,iBAAO,EAAExB,SAASwB,GAAAA;QACpB,GAKAhC,GAAA,eAAAS,IAUAT,GAAA,4BAAA,SAA0CiC,IAAAA;AACxC,iBAAO,EAAEzB,SAAS,MAAMc,GAAaW,EAAAA,EAAAA;QACvC;MAAA,EAAA,GC1GIC,IAA2B,CAAC;AAGhC,eAASC,EAAoBC,IAAAA;AAE5B,YAAIC,IAAeH,EAAyBE,EAAAA;AAC5C,YAAA,WAAIC,EACH,QAAOA,EAAa3C;AAGrB,YAAIC,IAASuC,EAAyBE,EAAAA,IAAY,EAGjD1C,SAAS,CAAC,EAAA;AAOX,eAHA4C,EAAoBF,EAAAA,EAAUzC,GAAQA,EAAOD,SAASyC,CAAAA,GAG/CxC,EAAOD;MACf;AAAA,UAAA,IAAA,CAAA;AAAA,cAAA,MAAA;AAAA,YAAAK,KAAA;AAAA,eAAA,eAAAA,IAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAAA,GAAA,cAAA;ACfA,cAAAC,KAAA,EAAA,GAAA,GACA,IAAA,EAAA,GAAA,GAoDMuC,IAAsB;QAI5B,MAAaC,UAAoB,EAAAC,WAAAA;UAqB/B,YAAYC,IAAAA;AACVC,kBAAAA,GAnBM,KAAAC,oBAAiC,oBAAIC,OACrC,KAAAC,wBAAsC,CAAA,GACtC,KAAAC,sBAAqD3C,KAAK4C,SAAS,IAAI,EAAAC,mBAAAA,GAUvE,KAAAC,uBAAuB,GACvB,KAAAC,yBAAyB,IAAI,EAAAF,qBAEpB,KAAAG,sBAAsBhD,KAAK4C,SAAS,IAAIhD,GAAAqD,cAAAA,GACzC,KAAAC,qBAAqBlD,KAAKgD,oBAAoBjD,OAK5DC,KAAKmD,mBAAkBb,MAAAA,gBAAAA,GAASc,mBA1BJ;UA2B9B;UAEO,SAASC,IAAAA;AACdrD,iBAAKsD,YAAYD,IACjBrD,KAAK4C,SAAS5C,KAAKsD,UAAUC,cAAc,MAAMvD,KAAKwD,eAAAA,CAAAA,CAAAA,GACtDxD,KAAK4C,SAAS5C,KAAKsD,UAAUG,SAAS,MAAMzD,KAAKwD,eAAAA,CAAAA,CAAAA,GACjDxD,KAAK4C,UAAS,GAAA,EAAAc,cAAa,MAAM1D,KAAK2D,iBAAAA,CAAAA,CAAAA;UACxC;UAEQ,iBAAAH;;AACFxD,iBAAK4D,qBACPC,OAAOC,aAAa9D,KAAK4D,iBAAAA,GAEvB5D,KAAK+D,uBAAqB/D,UAAKgE,uBAALhE,mBAAyBiE,iBACrDjE,KAAK4D,oBAAoBM,WAAW,MAAA;AAClC,oBAAMC,KAAOnE,KAAK+D;AAClB/D,mBAAK+D,oBAAAA,QACL/D,KAAKoE,aAAaD,IAAO,EAAA,GAAKnE,KAAKgE,oBAAoBK,aAAAA,MAAmBC,UAAAA,KAAU,CAAA;YAAO,GAC1F,GAAA;UAEP;UAEO,iBAAiBC,IAAAA;AACtBvE,iBAAK2C,oBAAoBhB,MAAAA,IACzB,GAAA,EAAAT,cAAalB,KAAK0C,qBAAAA,GAClB1C,KAAK0C,wBAAwB,CAAA,GAC7B1C,KAAKwC,kBAAkBb,MAAAA,GAClB4C,OACHvE,KAAK+D,oBAAAA;UAET;UAEO,wBAAAS;AACLxE,iBAAK2C,oBAAoBhB,MAAAA;UAC3B;UASO,SAASwC,IAAcM,IAAAA;AAC5B,gBAAA,CAAKzE,KAAKsD,UACR,OAAM,IAAIoB,MAAM,2CAAA;AAElB,kBAAMC,KAAAA,CAAoB3E,KAAKgE,sBAAqBhE,KAAK4E,kBAAkB5E,KAAKgE,oBAAoBS,EAAAA;AACpGzE,iBAAKgE,qBAAqBS,KACtBA,MAAAA,gBAAAA,GAAeR,iBAAAA,WACbjE,KAAK+D,qBAAmCI,OAASnE,KAAK+D,qBAAqBY,OAC7E3E,KAAK6E,qBAAqBV,IAAMM,EAAAA;AAIpC,kBAAMK,KAAQ9E,KAAK+E,mBAAmBZ,IAAMM,EAAAA;AAI5C,mBAHAzE,KAAKgF,aAAaP,EAAAA,GAClBzE,KAAK+D,oBAAoBI,IAElBW;UACT;UAEQ,qBAAqBX,IAAcM,IAAAA;AACzC,gBAAA,CAAKzE,KAAKsD,UACR,OAAM,IAAIoB,MAAM,2CAAA;AAElB,gBAAA,CAAKP,MAAwB,MAAhBA,GAAK7D,OAEhB,QAAA,KADAN,KAAK2D,iBAAAA;AAGPc,YAAAA,KAAgBA,MAAiB,CAAC,GAGlCzE,KAAK2D,iBAAAA,IAAiB;AAEtB,kBAAMsB,KAA8C,CAAA;AACpD,gBAAIC,IACAC,KAASnF,KAAKoF,MAAMjB,IAAM,GAAG,GAAGM,EAAAA;AACpC,mBAAOU,QAAWD,MAAAA,gBAAAA,GAAYG,SAAQF,GAAOE,QAAOH,MAAAA,gBAAAA,GAAYI,SAAQH,GAAOG,QAAAA,EACzEL,GAA2B3E,UAAUN,KAAKmD,mBAG9C+B,CAAAA,KAAaC,IACbF,GAA2B9E,KAAK+E,EAAAA,GAChCC,KAASnF,KAAKoF,MACZjB,IACAe,GAAWI,MAAMJ,GAAWf,KAAK7D,UAAUN,KAAKsD,UAAUiC,OAAOL,GAAWG,MAAM,IAAIH,GAAWG,KACjGH,GAAWI,MAAMJ,GAAWf,KAAK7D,UAAUN,KAAKsD,UAAUiC,OAAO,IAAIL,GAAWI,MAAM,GACtFb,EAAAA;AAGJ,uBAAWe,MAASP,IAA4B;AAC9C,oBAAMQ,KAAazF,KAAK0F,wBAAwBF,IAAOf,GAAcR,WAAAA;AACjEwB,cAAAA,OACFzF,KAAKwC,kBAAkBmD,IAAIF,GAAWG,OAAOC,IAAAA,GAC7C7F,KAAK0C,sBAAsBvC,KAAK,EAAEsF,YAAAA,IAAYD,OAAAA,IAAO,UAAApF;AAAYqF,gBAAAA,GAAWrF,QAAAA;cAAW,EAAA,CAAA;YAAA;UAG7F;UAEQ,MAAM+D,IAAc2B,IAAkBC,IAAkBtB,IAAAA;;AAC9D,gBAAA,CAAKzE,KAAKsD,aAAAA,CAAca,MAAwB,MAAhBA,GAAK7D,OAGnC,SAFAN,UAAKsD,cAALtD,mBAAgBgG,kBAAAA,KAChBhG,KAAK2D,iBAAAA;AAGP,gBAAIoC,KAAW/F,KAAKsD,UAAUiC,KAC5B,OAAM,IAAIb,MAAM,gBAAgBqB,EAAAA,6BAAqC/F,KAAKsD,UAAUiC,IAAAA,OAAAA;AAGtF,gBAAIJ;AAEJnF,iBAAKiG,gBAAAA;AAEL,kBAAMC,KAAkC,EACtCJ,UAAAA,IACAC,UAAAA,GAAAA;AAMF,gBAFAZ,KAASnF,KAAKmG,YAAYhC,IAAM+B,IAAgBzB,EAAAA,GAAAA,CAE3CU,GAEH,UAASiB,KAAIN,KAAW,GAAGM,KAAIpG,KAAKsD,UAAU+C,OAAOC,OAAOC,QAAQvG,KAAKsD,UAAUkD,SACjFN,GAAeJ,WAAWM,IAC1BF,GAAeH,WAAW,GAG1BZ,KAASnF,KAAKmG,YAAYhC,IAAM+B,IAAgBzB,EAAAA,GAAAA,CAC5CU,KANmFiB,KAAAA;AAW3F,mBAAOjB;UACT;UAEQ,mBAAmBhB,IAAcM,IAAAA;;AACvC,gBAAA,CAAKzE,KAAKsD,aAAAA,CAAca,MAAwB,MAAhBA,GAAK7D,OAGnC,SAFAN,UAAKsD,cAALtD,mBAAgBgG,kBAChBhG,KAAK2D,iBAAAA,GAAAA;AAIP,kBAAM8C,KAAkBzG,KAAKsD,UAAUoD,qBAAAA;AACvC1G,iBAAKsD,UAAU0C,eAAAA;AAEf,gBAAID,KAAW,GACXD,KAAW;AACXW,YAAAA,OACEzG,KAAK+D,sBAAsBI,MAC7B4B,KAAWU,GAAgBE,IAAIC,GAC/Bd,KAAWW,GAAgBE,IAAIP,MAE/BL,KAAWU,GAAgBI,MAAMD,GACjCd,KAAWW,GAAgBI,MAAMT,KAIrCpG,KAAKiG,gBAAAA;AAEL,kBAAMC,KAAkC,EACtCJ,UAAAA,IACAC,UAAAA,GAAAA;AAIF,gBAAIZ,KAASnF,KAAKmG,YAAYhC,IAAM+B,IAAgBzB,EAAAA;AAEpD,gBAAA,CAAKU,GAEH,UAASiB,KAAIN,KAAW,GAAGM,KAAIpG,KAAKsD,UAAU+C,OAAOC,OAAOC,QAAQvG,KAAKsD,UAAUkD,SACjFN,GAAeJ,WAAWM,IAC1BF,GAAeH,WAAW,GAG1BZ,KAASnF,KAAKmG,YAAYhC,IAAM+B,IAAgBzB,EAAAA,GAAAA,CAC5CU,KANmFiB,KAAAA;AAY3F,gBAAA,CAAKjB,MAAuB,MAAbW,GACb,UAASM,KAAI,GAAGA,KAAIN,OAClBI,GAAeJ,WAAWM,IAC1BF,GAAeH,WAAW,GAC1BZ,KAASnF,KAAKmG,YAAYhC,IAAM+B,IAAgBzB,EAAAA,GAAAA,CAC5CU,KAJwBiB,KAAAA;AAkBhC,mBAAA,CAPKjB,MAAUsB,OACbP,GAAeJ,WAAWW,GAAgBI,MAAMT,GAChDF,GAAeH,WAAW,GAC1BZ,KAASnF,KAAKmG,YAAYhC,IAAM+B,IAAgBzB,EAAAA,IAI3CzE,KAAK8G,cAAc3B,IAAQV,MAAAA,gBAAAA,GAAeR,aAAaQ,MAAAA,gBAAAA,GAAeH,QAAAA;UAC/E;UAQO,aAAaH,IAAcM,IAAAA;AAChC,gBAAA,CAAKzE,KAAKsD,UACR,OAAM,IAAIoB,MAAM,2CAAA;AAElB,kBAAMC,KAAAA,CAAoB3E,KAAKgE,sBAAqBhE,KAAK4E,kBAAkB5E,KAAKgE,oBAAoBS,EAAAA;AACpGzE,iBAAKgE,qBAAqBS,KACtBA,MAAAA,gBAAAA,GAAeR,iBAAAA,WACbjE,KAAK+D,qBAAmCI,OAASnE,KAAK+D,qBAAqBY,OAC7E3E,KAAK6E,qBAAqBV,IAAMM,EAAAA;AAIpC,kBAAMK,KAAQ9E,KAAK+G,uBAAuB5C,IAAMM,EAAAA;AAIhD,mBAHAzE,KAAKgF,aAAaP,EAAAA,GAClBzE,KAAK+D,oBAAoBI,IAElBW;UACT;UAEQ,kBAAkBkC,IAAmCvC,IAAAA;AAC3D,mBAAA,CAAA,CAAKA,OAGDuC,GAAkBC,kBAAkBxC,GAAcwC,iBAGlDD,GAAkBE,UAAUzC,GAAcyC,SAG1CF,GAAkBG,cAAc1C,GAAc0C;UAIpD;UAEQ,aAAa1C,IAAAA;AACnB,gBAAIA,MAAAA,gBAAAA,GAAeR,aAAa;AAC9B,kBAAImD,KAAAA;AACJ,kBAAIpH,KAAK2C,oBAAoBlB,OAAO;AAClC,sBAAM4F,KAAgBrH,KAAK2C,oBAAoBlB,MAAM+D;AACrD,yBAASnF,KAAI,GAAGA,KAAIL,KAAK0C,sBAAsBpC,QAAQD,MAAK;AAC1D,wBAAMmF,KAAQxF,KAAK0C,sBAAsBrC,EAAAA,EAAGmF;AAC5C,sBAAIA,GAAMH,QAAQgC,GAAchC,OAAOG,GAAMF,QAAQ+B,GAAc/B,OAAOE,GAAM8B,SAASD,GAAcC,MAAM;AAC3GF,oBAAAA,KAAc/G;AACd;kBAAA;gBAAA;cAAA;AAINL,mBAAKgD,oBAAoBjC,KAAK,EAAEqG,aAAAA,IAAaG,aAAavH,KAAK0C,sBAAsBpC,OAAAA,CAAAA;YAAAA;UAEzF;UAEQ,uBAAuB6D,IAAcM,IAAAA;;AAC3C,gBAAA,CAAKzE,KAAKsD,UACR,OAAM,IAAIoB,MAAM,2CAAA;AAElB,gBAAA,CAAK1E,KAAKsD,aAAAA,CAAca,MAAwB,MAAhBA,GAAK7D,OAGnC,SAFAN,UAAKsD,cAALtD,mBAAgBgG,kBAChBhG,KAAK2D,iBAAAA,GAAAA;AAIP,kBAAM8C,KAAkBzG,KAAKsD,UAAUoD,qBAAAA;AACvC1G,iBAAKsD,UAAU0C,eAAAA;AAEf,gBAAIF,KAAW9F,KAAKsD,UAAU+C,OAAOC,OAAOC,QAAQvG,KAAKsD,UAAUkD,OAAO,GACtET,KAAW/F,KAAKsD,UAAUiC;AAC9B,kBAAMiC,KAAAA;AAENxH,iBAAKiG,gBAAAA;AACL,kBAAMC,KAAkC,EACtCJ,UAAAA,IACAC,UAAAA,GAAAA;AAGF,gBAAIZ;AAoBJ,gBAnBIsB,OACFP,GAAeJ,WAAWA,KAAWW,GAAgBI,MAAMT,GAC3DF,GAAeH,WAAWA,KAAWU,GAAgBI,MAAMD,GACvD5G,KAAK+D,sBAAsBI,OAE7BgB,IAASnF,KAAKmG,YAAYhC,IAAM+B,IAAgBzB,IAAAA,KAAe,GAC1DU,MAEHe,GAAeJ,WAAWA,KAAWW,GAAgBE,IAAIP,GACzDF,GAAeH,WAAWA,KAAWU,GAAgBE,IAAIC,MAK1DzB,MACHA,IAASnF,KAAKmG,YAAYhC,IAAM+B,IAAgBzB,IAAe+C,EAAAA,IAAAA,CAI5DrC,GAAQ;AACXe,cAAAA,GAAeH,WAAW0B,KAAKC,IAAIxB,GAAeH,UAAU/F,KAAKsD,UAAUiC,IAAAA;AAC3E,uBAASa,KAAIN,KAAW,GAAGM,MAAK,MAC9BF,GAAeJ,WAAWM,IAC1BjB,IAASnF,KAAKmG,YAAYhC,IAAM+B,IAAgBzB,IAAe+C,EAAAA,GAAAA,CAC3DrC,IAH6BiB,KAAAA;YAAAA;AASrC,gBAAA,CAAKjB,KAAUW,OAAc9F,KAAKsD,UAAU+C,OAAOC,OAAOC,QAAQvG,KAAKsD,UAAUkD,OAAO,EACtF,UAASJ,KAAKpG,KAAKsD,UAAU+C,OAAOC,OAAOC,QAAQvG,KAAKsD,UAAUkD,OAAO,GAAIJ,MAAKN,OAChFI,GAAeJ,WAAWM,IAC1BjB,IAASnF,KAAKmG,YAAYhC,IAAM+B,IAAgBzB,IAAe+C,EAAAA,GAAAA,CAC3DrC,IAHsFiB,KAAAA;AAU9F,mBAAOpG,KAAK8G,cAAc3B,GAAQV,MAAAA,gBAAAA,GAAeR,aAAaQ,MAAAA,gBAAAA,GAAeH,QAAAA;UAC/E;UAKQ,kBAAA2B;AACN,kBAAM5C,KAAWrD,KAAKsD;AACjBtD,iBAAK2H,gBACR3H,KAAK2H,cAAc,IAAIC,MAAMvE,GAASgD,OAAOC,OAAOhG,MAAAA,GACpDN,KAAK+C,uBAAuBtB,SAAQ,GAAA,EAAAoG,2BAA0B,CAC5DxE,GAASyE,WAAW,MAAM9H,KAAK+H,mBAAAA,CAAAA,GAC/B1E,GAAS2E,aAAa,MAAMhI,KAAK+H,mBAAAA,CAAAA,GACjC1E,GAASI,SAAS,MAAMzD,KAAK+H,mBAAAA,CAAAA,CAAAA,CAAAA,IAIjClE,OAAOC,aAAa9D,KAAK8C,oBAAAA,GACzB9C,KAAK8C,uBAAuBe,OAAOK,WAAW,MAAMlE,KAAK+H,mBAAAA,GAvX5B,IAAA;UAwX/B;UAEQ,qBAAAA;AACN/H,iBAAK2H,cAAAA,QACL3H,KAAK+C,uBAAuBpB,MAAAA,GACxB3B,KAAK8C,yBACPe,OAAOC,aAAa9D,KAAK8C,oBAAAA,GACzB9C,KAAK8C,uBAAuB;UAEhC;UASQ,aAAamF,IAAqBpC,IAAc1B,IAAAA;AACtD,oBAAyB,MAAhB8D,MAAuB9F,EAAoB+F,SAASrC,GAAKoC,KAAc,CAAA,CAAA,OAC3EA,KAAc9D,GAAK7D,WAAYuF,GAAKvF,UAAY6B,EAAoB+F,SAASrC,GAAKoC,KAAc9D,GAAK7D,MAAAA,CAAAA;UAC5G;UAcU,YAAY6D,IAAc+B,IAAiCzB,KAAgC,CAAC,GAAG+C,KAAAA,OAA2B;;AAClI,kBAAMnE,KAAWrD,KAAKsD,WAChB+B,KAAMa,GAAeJ,UACrBR,KAAMY,GAAeH,UAGrBoC,IAAY9E,GAASgD,OAAOC,OAAO8B,QAAQ/C,EAAAA;AACjD,gBAAI8C,uBAAWE,UACb,QAAIb,KAAAA,MACFtB,GAAeH,YAAY1C,GAASkC,SAMtCW,GAAeJ,YACfI,GAAeH,YAAY1C,GAASkC,MAC7BvF,KAAKmG,YAAYhC,IAAM+B,IAAgBzB,EAAAA;AAEhD,gBAAI6D,KAAQtI,UAAK2H,gBAAL3H,mBAAmBqF;AAC1BiD,kBACHA,IAAQtI,KAAKuI,qCAAqClD,IAAAA,IAAK,GACnDrF,KAAK2H,gBACP3H,KAAK2H,YAAYtC,EAAAA,IAAOiD;AAG5B,kBAAA,CAAOE,GAAYC,CAAAA,IAAWH,GAExBI,IAAS1I,KAAK2I,0BAA0BtD,IAAKC,EAAAA,GAC7CsD,IAAanE,GAAcwC,gBAAgB9C,KAAOA,GAAK0E,YAAAA,GACvDC,IAAmBrE,GAAcwC,gBAAgBuB,IAAaA,EAAWK,YAAAA;AAE/E,gBAAIzB,IAAAA;AACJ,gBAAI3C,GAAcyC,OAAO;AACvB,oBAAM6B,KAAcC,OAAOJ,GAAY,GAAA;AACvC,kBAAIK;AACJ,kBAAIzB,GAEF,QAAOyB,KAAYF,GAAYG,KAAKJ,EAAiBK,MAAM,GAAGT,CAAAA,CAAAA,IAC5DtB,KAAc2B,GAAYK,YAAYH,GAAU,CAAA,EAAG3I,QACnD6D,KAAO8E,GAAU,CAAA,GACjBF,GAAYK,aAAcjF,GAAK7D,SAAS;kBAG1C2I,CAAAA,KAAYF,GAAYG,KAAKJ,EAAiBK,MAAMT,CAAAA,CAAAA,GAChDO,MAAaA,GAAU,CAAA,EAAG3I,SAAS,MACrC8G,IAAcsB,KAAUK,GAAYK,YAAYH,GAAU,CAAA,EAAG3I,SAC7D6D,KAAO8E,GAAU,CAAA;YAAA,MAIjBzB,CAAAA,KACEkB,IAASE,EAAWtI,UAAU,MAChC8G,IAAc0B,EAAiBO,YAAYT,GAAYF,IAASE,EAAWtI,MAAAA,KAG7E8G,IAAc0B,EAAiBtH,QAAQoH,GAAYF,CAAAA;AAIvD,gBAAItB,KAAe,GAAG;AACpB,kBAAI3C,GAAc0C,aAAAA,CAAcnH,KAAKsJ,aAAalC,GAAa0B,GAAkB3E,EAAAA,EAC/E;AAKF,kBAAIoF,KAAiB;AACrB,qBAAOA,KAAiBd,EAAQnI,SAAS,KAAK8G,KAAeqB,EAAQc,KAAiB,CAAA,IACpFA,CAAAA;AAEF,kBAAIC,KAAeD;AACnB,qBAAOC,KAAef,EAAQnI,SAAS,KAAK8G,IAAcjD,GAAK7D,UAAUmI,EAAQe,KAAe,CAAA,IAC9FA,CAAAA;AAEF,oBAAMC,KAAiBrC,IAAcqB,EAAQc,EAAAA,GACvCG,KAAetC,IAAcjD,GAAK7D,SAASmI,EAAQe,EAAAA,GACnDG,KAAgB3J,KAAK4J,0BAA0BvE,KAAMkE,IAAgBE,EAAAA;AAI3E,qBAAO,EACLtF,MAAAA,IACAmB,KAAKqE,IACLtE,KAAKA,KAAMkE,IACXjC,MAPkBtH,KAAK4J,0BAA0BvE,KAAMmE,IAAcE,EAAAA,IAC5CC,KAAgBtG,GAASkC,QAAQiE,KAAeD,IAAAA;YAAAA;UAS/E;UAEQ,0BAA0BlE,IAAaqD,IAAAA;AAC7C,kBAAM7C,KAAO7F,KAAKsD,UAAW+C,OAAOC,OAAO8B,QAAQ/C,EAAAA;AACnD,gBAAA,CAAKQ,GACH,QAAO;AAET,qBAASxF,KAAI,GAAGA,KAAIqI,IAAQrI,MAAK;AAC/B,oBAAMwJ,KAAOhE,GAAKiE,QAAQzJ,EAAAA;AAC1B,kBAAA,CAAKwJ,GACH;AAGF,oBAAME,KAAOF,GAAKG,SAAAA;AACdD,cAAAA,GAAKzJ,SAAS,MAChBoI,MAAUqB,GAAKzJ,SAAS;AAI1B,oBAAM2J,KAAWpE,GAAKiE,QAAQzJ,KAAI,CAAA;AAC9B4J,cAAAA,MAAoC,MAAxBA,GAASC,SAAAA,KACvBxB;YAAAA;AAGJ,mBAAOA;UACT;UAEQ,0BAA0B5C,IAAkBP,IAAAA;AAClD,kBAAMlC,KAAWrD,KAAKsD;AACtB,gBAAI6G,KAAYrE,IACZ4C,KAAS,GACT7C,KAAOxC,GAASgD,OAAOC,OAAO8B,QAAQ+B,EAAAA;AAC1C,mBAAO5E,KAAO,KAAKM,MAAM;AACvB,uBAASxF,KAAI,GAAGA,KAAIkF,MAAQlF,KAAIgD,GAASkC,MAAMlF,MAAK;AAClD,sBAAMwJ,KAAOhE,GAAKiE,QAAQzJ,EAAAA;AAC1B,oBAAA,CAAKwJ,GACH;AAEEA,gBAAAA,GAAKK,SAAAA,MAEPxB,MAA6B,MAAnBmB,GAAKO,QAAAA,IAAkB,IAAIP,GAAKG,SAAAA,EAAW1J;cAAAA;AAKzD,kBAFA6J,MACAtE,KAAOxC,GAASgD,OAAOC,OAAO8B,QAAQ+B,EAAAA,GAClCtE,MAAAA,CAASA,GAAKwC,UAChB;AAEF9C,cAAAA,MAAQlC,GAASkC;YAAAA;AAEnB,mBAAOmD;UACT;UAUQ,qCAAqCyB,IAAmBE,IAAAA;;AAC9D,kBAAMhH,KAAWrD,KAAKsD,WAChBgH,KAAU,CAAA,GACVC,KAAc,CAAC,CAAA;AACrB,gBAAI1E,KAAOxC,GAASgD,OAAOC,OAAO8B,QAAQ+B,EAAAA;AAC1C,mBAAOtE,MAAM;AACX,oBAAM2E,KAAWnH,GAASgD,OAAOC,OAAO8B,QAAQ+B,KAAY,CAAA,GACtDM,IAAAA,CAAAA,CAAkBD,MAAWA,GAASnC;AAC5C,kBAAIqC,IAAS7E,GAAK8E,kBAAAA,CAAmBF,KAAmBJ,EAAAA;AACxD,kBAAII,KAAmBD,IAAU;AAC/B,sBAAMI,KAAW/E,GAAKiE,QAAQjE,GAAKvF,SAAS,CAAA;AACrBsK,gBAAAA,MAAmC,MAAvBA,GAASR,QAAAA,KAA2C,MAAxBQ,GAASV,SAAAA,KAEd,QAApCM,KAAAA,GAASV,QAAQ,CAAA,MAAjBU,mBAAqBN,gBACzCQ,IAASA,EAAOvB,MAAM,GAAA,EAAI;cAAA;AAI9B,kBADAmB,GAAQnK,KAAKuK,CAAAA,GAAAA,CACTD,EAGF;AAFAF,cAAAA,GAAYpK,KAAKoK,GAAYA,GAAYjK,SAAS,CAAA,IAAKoK,EAAOpK,MAAAA,GAIhE6J,MACAtE,KAAO2E;YAAAA;AAET,mBAAO,CAACF,GAAQO,KAAK,EAAA,GAAKN,EAAAA;UAC5B;UAOQ,cAAcpF,IAAmC7C,IAAoCgC,IAAAA;AAC3F,kBAAMjB,KAAWrD,KAAKsD;AAEtB,gBADAtD,KAAK2C,oBAAoBhB,MAAAA,GAAAA,CACpBwD,GAEH,QADA9B,GAAS2C,eAAAA,GAAAA;AAIX,gBADA3C,GAASyH,OAAO3F,GAAOG,KAAKH,GAAOE,KAAKF,GAAOmC,IAAAA,GAC3ChF,IAAS;AACX,oBAAMsD,KAASvC,GAAS0H,eAAAA,CAAgB1H,GAASgD,OAAOC,OAAOC,QAAQlD,GAASgD,OAAOC,OAAO0E,UAAU7F,GAAOE,GAAAA;AAC/G,kBAAIO,IAAQ;AACV,sBAAMH,KAAapC,GAAS4H,mBAAmB,EAC7CrF,QAAAA,IACAgB,GAAGzB,GAAOG,KACV4F,OAAO/F,GAAOmC,MACd6D,iBAAiB7I,GAAQ8I,uBACzBC,OAAO,OACPC,sBAAsB,EACpBC,OAAOjJ,GAAQkJ,8BAAAA,EAAAA,CAAAA;AAGnB,oBAAI/F,IAAY;AACd,wBAAMtE,KAA6B,CAAA;AACnCA,kBAAAA,GAAYhB,KAAKyF,EAAAA,GACjBzE,GAAYhB,KAAKsF,GAAWgG,SAAU9L,CAAAA,OAAMK,KAAK0L,aAAa/L,IAAG2C,GAAQqJ,mBAAAA,IAAmB,CAAA,CAAA,GAC5FxK,GAAYhB,KAAKsF,GAAWmG,UAAU,OAAM,GAAA,EAAA1K,cAAaC,EAAAA,CAAAA,CAAAA,GACzDnB,KAAK2C,oBAAoBlB,QAAQ,EAAEgE,YAAAA,IAAYD,OAAOL,IAAQ,UAAA/E;AAAYqF,oBAAAA,GAAWrF,QAAAA;kBAAW,EAAA;gBAAA;cAAA;YAAA;AAKtG,gBAAA,CAAKkE,OAECa,GAAOE,OAAQhC,GAASgD,OAAOC,OAAOuF,YAAYxI,GAASmD,QAASrB,GAAOE,MAAMhC,GAASgD,OAAOC,OAAOuF,YAAW;AACrH,kBAAIC,KAAS3G,GAAOE,MAAMhC,GAASgD,OAAOC,OAAOuF;AACjDC,cAAAA,MAAUrE,KAAKsE,MAAM1I,GAASmD,OAAO,CAAA,GACrCnD,GAAS2I,YAAYF,EAAAA;YAAAA;AAGzB,mBAAA;UACF;UASQ,aAAaG,IAAsBC,IAAiCC,IAAAA;AACrEF,YAAAA,GAAQG,UAAUC,SAAS,8BAAA,MAC9BJ,GAAQG,UAAUzG,IAAI,8BAAA,GAClBuG,OACFD,GAAQK,MAAMC,UAAU,aAAaL,EAAAA,MAGrCC,MACFF,GAAQG,UAAUzG,IAAI,qCAAA;UAE1B;UAQQ,wBAAwBR,IAAuB7C,IAAAA;AACrD,kBAAMe,KAAWrD,KAAKsD,WAChBsC,KAASvC,GAAS0H,eAAAA,CAAgB1H,GAASgD,OAAOC,OAAOC,QAAQlD,GAASgD,OAAOC,OAAO0E,UAAU7F,GAAOE,GAAAA;AAC/G,gBAAA,CAAKO,GACH;AAEF,kBAAM4G,KAAuBnJ,GAAS4H,mBAAmB,EACvDrF,QAAAA,IACAgB,GAAGzB,GAAOG,KACV4F,OAAO/F,GAAOmC,MACd6D,iBAAiB7I,GAAQmK,iBACzBnB,sBAAsBtL,KAAKwC,kBAAkBkK,IAAI9G,GAAOC,IAAAA,IAAAA,SAAoB,EAC1E0F,OAAOjJ,GAAQqK,oBACfC,UAAU,SAAA,EAAA,CAAA;AAGd,gBAAIJ,IAAsB;AACxB,oBAAMrL,KAA6B,CAAA;AACnCA,cAAAA,GAAYhB,KAAKyF,EAAAA,GACjBzE,GAAYhB,KAAKqM,GAAqBf,SAAU9L,CAAAA,OAAMK,KAAK0L,aAAa/L,IAAG2C,GAAQuK,aAAAA,KAAa,CAAA,CAAA,GAChG1L,GAAYhB,KAAKqM,GAAqBZ,UAAU,OAAM,GAAA,EAAA1K,cAAaC,EAAAA,CAAAA,CAAAA;YAAAA;AAErE,mBAAOqL;UACT;QAAA;AAzqBF,QAAA7M,GAAA,cAAA;MAAA,GAAA,GAAA;IAAA,GAAA,CAAA;;;", "names": ["root", "factory", "exports", "module", "define", "amd", "self", "e", "t", "_listeners", "_disposed", "event", "this", "_event", "listener", "push", "dispose", "i", "length", "splice", "arg1", "arg2", "queue", "call", "clearListeners", "from", "to", "fire", "handler", "undefined", "dispose<PERSON><PERSON><PERSON>", "disposables", "d", "_disposables", "_isDisposed", "index", "indexOf", "value", "_value", "clear", "f", "array", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "__webpack_modules__", "NON_WORD_CHARACTERS", "SearchAddon", "Disposable", "options", "super", "_highlightedLines", "Set", "_highlightDecorations", "_selectedDecoration", "register", "MutableDisposable", "_linesCacheTimeoutId", "_linesCacheDisposables", "_onDidChangeResults", "EventEmitter", "onDidChangeResults", "_highlightLimit", "highlightLimit", "terminal", "_terminal", "onWriteParsed", "_updateMatches", "onResize", "toDisposable", "clearDecorations", "_highlightTimeout", "window", "clearTimeout", "_cachedSearchTerm", "_lastSearchOptions", "decorations", "setTimeout", "term", "find<PERSON>revious", "incremental", "noScroll", "retainCachedSearchTerm", "clearActiveDecoration", "searchOptions", "Error", "didOptionsChanged", "_didOptionsChange", "_highlightAllMatches", "found", "_findNextAndSelect", "_fireResults", "searchResultsWithHighlight", "prevResult", "result", "_find", "row", "col", "cols", "match", "decoration", "_createResultDecoration", "add", "marker", "line", "startRow", "startCol", "clearSelection", "_initLinesCache", "searchPosition", "_findInLine", "y", "buffer", "active", "baseY", "rows", "prevSelectedPos", "getSelectionPosition", "end", "x", "start", "_selectResult", "_findPreviousAndSelect", "lastSearchOptions", "caseSensitive", "regex", "wholeWord", "resultIndex", "selectedM<PERSON>", "size", "resultCount", "isReverseSearch", "Math", "max", "_linesCache", "Array", "getDisposeArrayDisposable", "onLineFeed", "_destroyLinesCache", "onCursorMove", "searchIndex", "includes", "firstLine", "getLine", "isWrapped", "cache", "_translateBufferLineToStringWithWrap", "stringLine", "offsets", "offset", "_bufferColsToStringOffset", "searchTerm", "toLowerCase", "searchStringLine", "searchRegex", "RegExp", "foundTerm", "exec", "slice", "lastIndex", "lastIndexOf", "_isWholeWord", "startRowOffset", "endRowOffset", "startColOffset", "endColOffset", "startColIndex", "_stringLengthToBufferSize", "cell", "getCell", "char", "getChars", "nextCell", "getWidth", "lineIndex", "getCode", "trimRight", "strings", "lineOffsets", "nextLine", "lineWrapsToNext", "string", "translateToString", "lastCell", "join", "select", "registerMarker", "cursorY", "registerDecoration", "width", "backgroundColor", "activeMatchBackground", "layer", "overviewRulerOptions", "color", "activeMatchColorOverviewRuler", "onRender", "_applyStyles", "activeMatchBorder", "onDispose", "viewportY", "scroll", "floor", "scrollLines", "element", "borderColor", "isActiveResult", "classList", "contains", "style", "outline", "findResultDecoration", "matchBackground", "has", "matchOverviewRuler", "position", "matchBorder"]}