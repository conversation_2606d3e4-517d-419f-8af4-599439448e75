import { exec } from 'child_process';
import { promisify } from 'util';

export const execAsync = promisify(exec);

export interface ExecResult {
  stdout: string;
  stderr: string;
}

export async function execCommand(command: string): Promise<ExecResult> {
  try {
    const result = await execAsync(command);
    return {
      stdout: result.stdout,
      stderr: result.stderr
    };
  } catch (error: any) {
    throw new Error(`Command failed: ${command}\nError: ${error.message}`);
  }
}

export async function execCommandSafe(command: string): Promise<ExecResult | null> {
  try {
    return await execCommand(command);
  } catch (error) {
    return null;
  }
}
