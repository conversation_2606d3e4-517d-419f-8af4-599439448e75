"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.appConfig = void 0;
const dotenv_1 = require("dotenv");
const zod_1 = require("zod");
(0, dotenv_1.config)();
const envSchema = zod_1.z.object({
    NODE_ENV: zod_1.z.enum(['development', 'staging', 'production']).default('development'),
    PORT: zod_1.z.string().transform(Number).default('3000'),
    HOST: zod_1.z.string().default('0.0.0.0'),
    DATABASE_URL: zod_1.z.string().min(1, 'DATABASE_URL is required'),
    JWT_SECRET: zod_1.z.string().min(32, 'JWT_SECRET must be at least 32 characters'),
    JWT_EXPIRES_IN: zod_1.z.string().default('7d'),
    VULTR_API_KEY: zod_1.z.string().min(1, 'VULTR_API_KEY is required'),
    VULTR_API_BASE_URL: zod_1.z.string().url().default('https://api.vultr.com/v2'),
    RESELLERBIZ_AUTH_USERID: zod_1.z.string().min(1, 'RESELLERBIZ_AUTH_USERID is required'),
    RESELLERBIZ_API_KEY: zod_1.z.string().min(1, 'RESELLERBIZ_API_KEY is required'),
    RESELLERBIZ_API_BASE_URL: zod_1.z.string().url().default('https://httpapi.com/api'),
    RESELLERBIZ_TEST_MODE: zod_1.z.string().transform(val => val === 'true').default('false'),
    LOG_LEVEL: zod_1.z.enum(['fatal', 'error', 'warn', 'info', 'debug', 'trace']).default('info'),
    LOG_PRETTY: zod_1.z.string().transform(val => val === 'true').default('false'),
    RATE_LIMIT_MAX: zod_1.z.string().transform(Number).default('100'),
    RATE_LIMIT_WINDOW: zod_1.z.string().transform(Number).default('60000'),
    CORS_ORIGIN: zod_1.z.string().default('*'),
    CORS_CREDENTIALS: zod_1.z.string().transform(val => val === 'true').default('true'),
    JAEGER_ENDPOINT: zod_1.z.string().url().optional(),
    METRICS_PORT: zod_1.z.string().transform(Number).default('9090'),
    CIRCUIT_BREAKER_FAILURE_THRESHOLD: zod_1.z.string().transform(Number).default('5'),
    CIRCUIT_BREAKER_TIMEOUT_SECONDS: zod_1.z.string().transform(Number).default('60'),
    SHARED_HOSTING_SERVERS: zod_1.z.string().optional(),
    SHARED_HOSTING_MAX_USERS_PER_SERVER: zod_1.z.string().transform(Number).default('50'),
    SHARED_HOSTING_BASE_PORT: zod_1.z.string().transform(Number).default('8000'),
    SHARED_HOSTING_SSH_BASE_PORT: zod_1.z.string().transform(Number).default('2200'),
    SHARED_HOSTING_FTP_BASE_PORT: zod_1.z.string().transform(Number).default('2100'),
    SSH_HOST: zod_1.z.string().optional(),
    SSH_PORT: zod_1.z.string().transform(Number).default('22'),
    SSH_USERNAME: zod_1.z.string().default('root'),
    SSH_PASSWORD: zod_1.z.string().optional(),
    SSH_PRIVATE_KEY_PATH: zod_1.z.string().optional(),
    SSH_TIMEOUT: zod_1.z.string().transform(Number).default('30000'),
    GRACE_PERIOD_DAYS_DEFAULT: zod_1.z.string().transform(Number).default('30'),
    GRACE_PERIOD_DAYS_PREMIUM: zod_1.z.string().transform(Number).default('60'),
    DELETION_DELAY_DAYS: zod_1.z.string().transform(Number).default('7'),
    PAYMENT_CHECK_INTERVAL_HOURS: zod_1.z.string().transform(Number).default('24'),
    SYNC_INTERVAL_MINUTES: zod_1.z.string().transform(Number).default('5'),
    SYNC_ENABLED: zod_1.z.string().transform(val => val === 'true').default('true'),
    MONITORING_ENABLED: zod_1.z.string().transform(val => val === 'true').default('true'),
    MONITORING_INTERVAL_SECONDS: zod_1.z.string().transform(Number).default('3600'),
    METRICS_RETENTION_DAYS: zod_1.z.string().transform(Number).default('30'),
    SECURITY_MONITORING_ENABLED: zod_1.z.string().transform(val => val === 'true').default('true'),
    MAX_LOGIN_ATTEMPTS: zod_1.z.string().transform(Number).default('5'),
    LOCKOUT_DURATION_MINUTES: zod_1.z.string().transform(Number).default('15'),
    ABUSE_CHECK_INTERVAL_MINUTES: zod_1.z.string().transform(Number).default('5'),
    PRIMARY_DOMAIN: zod_1.z.string().default('poolot.com'),
    CLOUDFLARE_API_TOKEN: zod_1.z.string().optional(),
    CLOUDFLARE_ZONE_ID: zod_1.z.string().optional(),
    CLOUDFLARE_DNS_ENABLED: zod_1.z.string().transform(val => val === 'true').default('false'),
    BACKUP_ENABLED: zod_1.z.string().transform(val => val === 'true').default('true'),
    BACKUP_INTERVAL_HOURS: zod_1.z.string().transform(Number).default('24'),
    BACKUP_RETENTION_DAYS: zod_1.z.string().transform(Number).default('30'),
    BACKUP_STORAGE_PATH: zod_1.z.string().default('/var/backups/achidas'),
    DEPLOYMENT_TIMEOUT_MINUTES: zod_1.z.string().transform(Number).default('30'),
    BUILD_TIMEOUT_MINUTES: zod_1.z.string().transform(Number).default('15'),
    MAX_CONCURRENT_DEPLOYMENTS: zod_1.z.string().transform(Number).default('5'),
});
const env = envSchema.parse(process.env);
exports.appConfig = {
    server: {
        port: env.PORT,
        host: env.HOST,
        environment: env.NODE_ENV,
        env: env.NODE_ENV,
    },
    database: {
        url: env.DATABASE_URL,
    },
    auth: {
        jwtSecret: env.JWT_SECRET,
        jwtExpiresIn: env.JWT_EXPIRES_IN,
    },
    vultr: {
        apiKey: env.VULTR_API_KEY,
        baseUrl: env.VULTR_API_BASE_URL,
    },
    resellerBiz: {
        authUserId: env.RESELLERBIZ_AUTH_USERID,
        apiKey: env.RESELLERBIZ_API_KEY,
        baseUrl: env.RESELLERBIZ_TEST_MODE ? 'https://test.httpapi.com/api' : env.RESELLERBIZ_API_BASE_URL,
        testMode: env.RESELLERBIZ_TEST_MODE,
    },
    logging: {
        level: env.LOG_LEVEL,
        pretty: env.LOG_PRETTY,
    },
    rateLimit: {
        max: env.RATE_LIMIT_MAX,
        window: env.RATE_LIMIT_WINDOW,
        timeWindow: env.RATE_LIMIT_WINDOW,
    },
    cors: {
        origin: env.CORS_ORIGIN === '*' ? true : env.CORS_ORIGIN.split(',').map(origin => origin.trim()),
        credentials: env.CORS_CREDENTIALS,
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
        allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'X-Request-ID'],
    },
    monitoring: {
        jaegerEndpoint: env.JAEGER_ENDPOINT,
        metricsPort: env.METRICS_PORT,
        enabled: env.MONITORING_ENABLED,
        intervalSeconds: env.MONITORING_INTERVAL_SECONDS,
        retentionDays: env.METRICS_RETENTION_DAYS,
    },
    circuitBreaker: {
        failureThreshold: env.CIRCUIT_BREAKER_FAILURE_THRESHOLD,
        timeoutSeconds: env.CIRCUIT_BREAKER_TIMEOUT_SECONDS,
    },
    sharedHosting: {
        servers: env.SHARED_HOSTING_SERVERS ? JSON.parse(env.SHARED_HOSTING_SERVERS) : [],
        maxUsersPerServer: env.SHARED_HOSTING_MAX_USERS_PER_SERVER,
        basePort: env.SHARED_HOSTING_BASE_PORT,
        sshBasePort: env.SHARED_HOSTING_SSH_BASE_PORT,
        ftpBasePort: env.SHARED_HOSTING_FTP_BASE_PORT,
    },
    ssh: {
        host: env.SSH_HOST,
        port: env.SSH_PORT,
        username: env.SSH_USERNAME,
        password: env.SSH_PASSWORD,
        privateKeyPath: env.SSH_PRIVATE_KEY_PATH,
        timeout: env.SSH_TIMEOUT,
    },
    payment: {
        gracePeriodDaysDefault: env.GRACE_PERIOD_DAYS_DEFAULT,
        gracePeriodDaysPremium: env.GRACE_PERIOD_DAYS_PREMIUM,
        deletionDelayDays: env.DELETION_DELAY_DAYS,
        checkIntervalHours: env.PAYMENT_CHECK_INTERVAL_HOURS,
    },
    sync: {
        intervalMinutes: env.SYNC_INTERVAL_MINUTES,
        enabled: env.SYNC_ENABLED,
    },
    security: {
        monitoringEnabled: env.SECURITY_MONITORING_ENABLED,
        maxLoginAttempts: env.MAX_LOGIN_ATTEMPTS,
        lockoutDurationMinutes: env.LOCKOUT_DURATION_MINUTES,
        abuseCheckIntervalMinutes: env.ABUSE_CHECK_INTERVAL_MINUTES,
    },
    domain: {
        primary: env.PRIMARY_DOMAIN,
    },
    cloudflare: {
        apiToken: env.CLOUDFLARE_API_TOKEN,
        zoneId: env.CLOUDFLARE_ZONE_ID,
        dnsEnabled: env.CLOUDFLARE_DNS_ENABLED,
    },
    backup: {
        enabled: env.BACKUP_ENABLED,
        intervalHours: env.BACKUP_INTERVAL_HOURS,
        retentionDays: env.BACKUP_RETENTION_DAYS,
        storagePath: env.BACKUP_STORAGE_PATH,
    },
    deployment: {
        timeoutMinutes: env.DEPLOYMENT_TIMEOUT_MINUTES,
        buildTimeoutMinutes: env.BUILD_TIMEOUT_MINUTES,
        maxConcurrentDeployments: env.MAX_CONCURRENT_DEPLOYMENTS,
    },
};
//# sourceMappingURL=index.js.map