"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getHostingPlansController = getHostingPlansController;
exports.getHostingPlanController = getHostingPlanController;
exports.getRecommendedPlanController = getRecommendedPlanController;
exports.createSharedUserController = createSharedUserController;
exports.getSharedUsersController = getSharedUsersController;
exports.getSharedUserController = getSharedUserController;
exports.updateSharedUserController = updateSharedUserController;
exports.deleteSharedUserController = deleteSharedUserController;
exports.getSharedHostingAnalyticsController = getSharedHostingAnalyticsController;
exports.getUserResourceUsageController = getUserResourceUsageController;
exports.getServerCapacityController = getServerCapacityController;
exports.getUserApplicationsController = getUserApplicationsController;
exports.createApplicationController = createApplicationController;
exports.updateUserApplicationController = updateUserApplicationController;
exports.deleteUserApplicationController = deleteUserApplicationController;
exports.suspendUserController = suspendUserController;
exports.reactivateUserController = reactivateUserController;
exports.resetUserPasswordController = resetUserPasswordController;
exports.getServerPoolStatusController = getServerPoolStatusController;
exports.addServerToPoolController = addServerToPoolController;
exports.removeServerFromPoolController = removeServerFromPoolController;
exports.synchronizeServersController = synchronizeServersController;
exports.getUserPaymentStatusController = getUserPaymentStatusController;
exports.updateUserPaymentStatusController = updateUserPaymentStatusController;
exports.testSSHConnectionController = testSSHConnectionController;
const response_1 = require("../utils/response");
const logger_1 = require("../utils/logger");
const shared_hosting_1 = require("../services/shared-hosting");
const HOSTING_PLANS = [
    {
        tier: 'Shared',
        plan_name: 'shared',
        description: 'Shared hosting with full server resources - pay-as-you-go',
        features: [
            'Full server resources access',
            'Dynamic resource allocation',
            'Unlimited applications',
            'Custom subdomains (*.poolot.com)',
            'SSL Certificate',
            'Database Access (MySQL, PostgreSQL, MongoDB)',
            '24/7 Support',
            'Daily Backups',
            'File Manager',
            'SSH Access',
            'Git Integration'
        ],
        max_concurrent_users: 50,
        price_per_hour: 0.01,
        price_per_month: 7.20,
        recommended_for: ['Personal projects', 'Small businesses', 'Startups', 'Learning', 'Prototyping'],
        african_pricing: {
            nigeria_naira: 11664,
            south_africa_rand: 133,
            kenya_shilling: 936,
            ghana_cedi: 86
        }
    },
    {
        tier: 'Dedicated',
        plan_name: 'dedicated-starter',
        description: 'Entry-level dedicated server for small projects',
        features: [
            'Full 1 vCPU',
            '1GB RAM',
            '25GB SSD Storage',
            '1TB Bandwidth',
            'Root Access',
            'Custom subdomains (*.poolot.com)',
            'SSL Certificate',
            '24/7 Support',
            'Daily Backups',
            'Custom OS',
            'Full server control'
        ],
        cpu_allocation: 100,
        memory_allocation: 1024,
        storage_allocation: 25,
        bandwidth_limit: 1024,
        max_concurrent_users: 1,
        price_per_hour: 0.007,
        price_per_month: 5.00,
        vultr_plan: 'vc2-1c-1gb',
        recommended_for: ['Small businesses', 'Development', 'Testing', 'Personal projects'],
        african_pricing: {
            nigeria_naira: 8100,
            south_africa_rand: 92,
            kenya_shilling: 650,
            ghana_cedi: 60
        }
    },
    {
        tier: 'Dedicated',
        plan_name: 'dedicated-standard',
        description: 'Standard dedicated server for growing businesses',
        features: [
            'Full 2 vCPU',
            '4GB RAM',
            '80GB SSD Storage',
            '3TB Bandwidth',
            'Root Access',
            'Custom subdomains (*.poolot.com)',
            'SSL Certificate',
            '24/7 Support',
            'Daily Backups',
            'Custom OS',
            'Load Balancer',
            'Database Services'
        ],
        cpu_allocation: 200,
        memory_allocation: 4096,
        storage_allocation: 80,
        bandwidth_limit: 3072,
        max_concurrent_users: 1,
        price_per_hour: 0.024,
        price_per_month: 17.28,
        vultr_plan: 'vc2-2c-4gb',
        recommended_for: ['Medium businesses', 'E-commerce', 'Web applications'],
        african_pricing: {
            nigeria_naira: 27993,
            south_africa_rand: 319,
            kenya_shilling: 2246,
            ghana_cedi: 207
        }
    },
    {
        tier: 'Dedicated',
        plan_name: 'dedicated-performance',
        description: 'High-performance dedicated server for demanding applications',
        features: [
            'Full 4 vCPU',
            '8GB RAM',
            '160GB SSD Storage',
            '4TB Bandwidth',
            'Root Access',
            'Custom subdomains (*.poolot.com)',
            'SSL Certificate',
            'Priority Support',
            'Daily Backups',
            'Custom OS',
            'Load Balancer',
            'Database Services',
            'CDN Integration'
        ],
        cpu_allocation: 400,
        memory_allocation: 8192,
        storage_allocation: 160,
        bandwidth_limit: 4096,
        max_concurrent_users: 1,
        price_per_hour: 0.048,
        price_per_month: 34.56,
        vultr_plan: 'vc2-4c-8gb',
        recommended_for: ['High-traffic websites', 'SaaS applications', 'API services'],
        african_pricing: {
            nigeria_naira: 55987,
            south_africa_rand: 639,
            kenya_shilling: 4493,
            ghana_cedi: 415
        }
    },
    {
        tier: 'Enterprise',
        plan_name: 'enterprise-standard',
        description: 'Enterprise-grade dedicated server for growing companies',
        features: [
            'Full 4 vCPU',
            '8GB RAM',
            '160GB SSD Storage',
            '4TB Bandwidth',
            'Root Access',
            'Custom subdomains (*.poolot.com)',
            'Premium SSL',
            '24/7 Priority Support',
            'Hourly Backups',
            'Custom OS',
            'Load Balancer',
            'DDoS Protection',
            'Monitoring Dashboard',
            'Database Services'
        ],
        cpu_allocation: 400,
        memory_allocation: 8192,
        storage_allocation: 160,
        bandwidth_limit: 4096,
        max_concurrent_users: 1,
        price_per_hour: 0.048,
        price_per_month: 34.56,
        vultr_plan: 'vc2-4c-8gb',
        recommended_for: ['Large businesses', 'High-traffic applications', 'SaaS platforms'],
        african_pricing: {
            nigeria_naira: 55987,
            south_africa_rand: 639,
            kenya_shilling: 4493,
            ghana_cedi: 415
        }
    },
    {
        tier: 'Enterprise',
        plan_name: 'enterprise-premium',
        description: 'Premium enterprise server for mission-critical applications',
        features: [
            'Full 8 vCPU',
            '16GB RAM',
            '320GB SSD Storage',
            '6TB Bandwidth',
            'Root Access',
            'Custom subdomains (*.poolot.com)',
            'Premium SSL',
            '24/7 Dedicated Support',
            'Real-time Backups',
            'Custom OS',
            'Advanced Load Balancer',
            'DDoS Protection',
            'Advanced Monitoring',
            'Database Services',
            'CDN Integration',
            'Auto-scaling'
        ],
        cpu_allocation: 800,
        memory_allocation: 16384,
        storage_allocation: 320,
        bandwidth_limit: 6144,
        max_concurrent_users: 1,
        price_per_hour: 0.096,
        price_per_month: 69.12,
        vultr_plan: 'vc2-8c-16gb',
        recommended_for: ['Enterprise applications', 'Mission-critical systems', 'Large SaaS platforms'],
        african_pricing: {
            nigeria_naira: 111974,
            south_africa_rand: 1278,
            kenya_shilling: 8986,
            ghana_cedi: 829
        }
    },
    {
        tier: 'Enterprise',
        plan_name: 'enterprise-premium',
        description: 'Premium enterprise dedicated server',
        features: [
            'Full 8 vCPU',
            '16GB RAM',
            '320GB SSD Storage',
            '5TB Bandwidth',
            'Root Access',
            'Premium SSL',
            '24/7 Priority Support',
            'Hourly Backups',
            'Custom OS',
            'Load Balancer',
            'DDoS Protection',
            'Monitoring Dashboard',
            'CDN Integration',
            'Auto-scaling'
        ],
        cpu_allocation: 800,
        memory_allocation: 16384,
        storage_allocation: 320,
        bandwidth_limit: 5120,
        max_concurrent_users: 1,
        price_per_hour: 0.095,
        price_per_month: 68.40,
        vultr_plan: 'vc2-8c-16gb',
        recommended_for: ['Enterprise applications', 'High-performance computing', 'Large SaaS'],
        african_pricing: {
            nigeria_naira: 110808,
            south_africa_rand: 1265,
            kenya_shilling: 8892,
            ghana_cedi: 821
        }
    }
];
async function getHostingPlansController(request, reply) {
    try {
        const { tier } = request.query;
        let plans = HOSTING_PLANS;
        if (tier) {
            const tierFilter = tier.charAt(0).toUpperCase() + tier.slice(1).toLowerCase();
            plans = HOSTING_PLANS.filter(plan => plan.tier === tierFilter);
        }
        logger_1.logger.info(`Retrieved ${plans.length} hosting plans${tier ? ` for tier: ${tier}` : ''}`);
        return response_1.ResponseHelper.success(reply, plans);
    }
    catch (error) {
        logger_1.logger.error('Get hosting plans controller error:', error);
        return response_1.ResponseHelper.internalError(reply, 'Failed to fetch hosting plans');
    }
}
async function getHostingPlanController(request, reply) {
    try {
        const { planName } = request.params;
        const plan = HOSTING_PLANS.find(p => p.plan_name === planName);
        if (!plan) {
            return response_1.ResponseHelper.notFound(reply, `Hosting plan '${planName}' not found`);
        }
        logger_1.logger.info(`Retrieved hosting plan: ${planName}`);
        return response_1.ResponseHelper.success(reply, plan);
    }
    catch (error) {
        logger_1.logger.error('Get hosting plan controller error:', error);
        return response_1.ResponseHelper.internalError(reply, 'Failed to fetch hosting plan');
    }
}
async function getRecommendedPlanController(request, reply) {
    try {
        const { service_type, expected_traffic, budget, tier } = request.query;
        let recommendedPlan;
        if (tier === 'shared' || budget === 'low' || service_type === 'personal') {
            recommendedPlan = HOSTING_PLANS.find(p => p.tier === 'Shared') ?? HOSTING_PLANS[0];
        }
        else if (tier === 'dedicated') {
            if (budget === 'medium' || expected_traffic === 'medium') {
                recommendedPlan = HOSTING_PLANS.find(p => p.plan_name === 'dedicated-standard') ?? HOSTING_PLANS[1];
            }
            else if (expected_traffic === 'high') {
                recommendedPlan = HOSTING_PLANS.find(p => p.plan_name === 'dedicated-performance') ?? HOSTING_PLANS[2];
            }
            else {
                recommendedPlan = HOSTING_PLANS.find(p => p.plan_name === 'dedicated-starter') ?? HOSTING_PLANS[1];
            }
        }
        else if (tier === 'enterprise') {
            if (expected_traffic === 'very-high' || budget === 'high') {
                recommendedPlan = HOSTING_PLANS.find(p => p.plan_name === 'enterprise-premium') ?? HOSTING_PLANS[4];
            }
            else {
                recommendedPlan = HOSTING_PLANS.find(p => p.plan_name === 'enterprise-standard') ?? HOSTING_PLANS[3];
            }
        }
        else if (expected_traffic === 'high' || service_type === 'enterprise') {
            recommendedPlan = HOSTING_PLANS.find(p => p.plan_name === 'enterprise-standard') ?? HOSTING_PLANS[3];
        }
        else if (expected_traffic === 'medium' || service_type === 'business') {
            recommendedPlan = HOSTING_PLANS.find(p => p.plan_name === 'dedicated-standard') ?? HOSTING_PLANS[1];
        }
        else {
            recommendedPlan = HOSTING_PLANS.find(p => p.tier === 'Shared') ?? HOSTING_PLANS[0];
        }
        logger_1.logger.info(`Recommended plan: ${recommendedPlan.plan_name} for service_type: ${service_type}, traffic: ${expected_traffic}`);
        return response_1.ResponseHelper.success(reply, recommendedPlan);
    }
    catch (error) {
        logger_1.logger.error('Get recommended plan controller error:', error);
        return response_1.ResponseHelper.internalError(reply, 'Failed to get recommended plan');
    }
}
async function createSharedUserController(request, reply) {
    try {
        if (!request.user) {
            return response_1.ResponseHelper.unauthorized(reply, 'Authentication required');
        }
        const userData = request.body;
        if (!userData.user_id || !userData.username) {
            return response_1.ResponseHelper.validationError(reply, 'Missing required fields: user_id, username');
        }
        userData.plan = 'shared';
        const sharedUser = await (0, shared_hosting_1.getSharedHostingService)().createUser(userData);
        logger_1.logger.info(`Created shared hosting user: ${userData.username} on server: ${sharedUser.server_id}`);
        return response_1.ResponseHelper.success(reply, sharedUser, 201);
    }
    catch (error) {
        logger_1.logger.error('❌ Create shared user controller error:', {
            error: error instanceof Error ? error.message : String(error),
            stack: error instanceof Error ? error.stack : undefined,
            userData: request.body
        });
        if (error instanceof Error) {
            if (error.message.includes('already exists')) {
                return response_1.ResponseHelper.validationError(reply, error.message);
            }
            if (error.message.includes('server capacity')) {
                return response_1.ResponseHelper.validationError(reply, 'No available server capacity for new users');
            }
        }
        return response_1.ResponseHelper.internalError(reply, 'Failed to create shared hosting user');
    }
}
async function getSharedUsersController(request, reply) {
    try {
        if (!request.user) {
            return response_1.ResponseHelper.unauthorized(reply, 'Authentication required');
        }
        if (request.user.role !== 'admin' && request.user.role !== 'super_admin') {
            return response_1.ResponseHelper.forbidden(reply, 'Admin access required');
        }
        const { page = '1', limit = '20', status, plan, server_id, search } = request.query;
        const filters = {
            page: parseInt(page),
            limit: parseInt(limit)
        };
        if (status)
            filters.status = status;
        if (plan)
            filters.plan = plan;
        if (server_id)
            filters.server_id = server_id;
        if (search)
            filters.search = search;
        const result = await (0, shared_hosting_1.getSharedHostingService)().getUsers(filters);
        logger_1.logger.info(`Retrieved ${result.users.length} shared hosting users (page ${page})`);
        return response_1.ResponseHelper.success(reply, result);
    }
    catch (error) {
        logger_1.logger.error('Get shared users controller error:', error);
        return response_1.ResponseHelper.internalError(reply, 'Failed to fetch shared hosting users');
    }
}
async function getSharedUserController(request, reply) {
    try {
        if (!request.user) {
            return response_1.ResponseHelper.unauthorized(reply, 'Authentication required');
        }
        const { userId } = request.params;
        if (request.user.sub !== userId && request.user.role !== 'admin' && request.user.role !== 'super_admin') {
            return response_1.ResponseHelper.forbidden(reply, 'Access denied');
        }
        const user = await (0, shared_hosting_1.getSharedHostingService)().getUser(userId);
        if (!user) {
            return response_1.ResponseHelper.notFound(reply, 'Shared hosting user not found');
        }
        logger_1.logger.info(`Retrieved shared hosting user: ${userId}`);
        return response_1.ResponseHelper.success(reply, user);
    }
    catch (error) {
        logger_1.logger.error('Get shared user controller error:', error);
        return response_1.ResponseHelper.internalError(reply, 'Failed to fetch shared hosting user');
    }
}
async function updateSharedUserController(request, reply) {
    try {
        if (!request.user) {
            return response_1.ResponseHelper.unauthorized(reply, 'Authentication required');
        }
        const { userId } = request.params;
        const updateData = request.body;
        if (request.user.role !== 'admin' && request.user.role !== 'super_admin') {
            return response_1.ResponseHelper.forbidden(reply, 'Admin access required');
        }
        const updatedUser = await (0, shared_hosting_1.getSharedHostingService)().updateUser(userId, updateData);
        logger_1.logger.info(`Updated shared hosting user: ${userId}`);
        return response_1.ResponseHelper.success(reply, updatedUser);
    }
    catch (error) {
        logger_1.logger.error('Update shared user controller error:', error);
        if (error instanceof Error && error.message.includes('not found')) {
            return response_1.ResponseHelper.notFound(reply, 'Shared hosting user not found');
        }
        return response_1.ResponseHelper.internalError(reply, 'Failed to update shared hosting user');
    }
}
async function deleteSharedUserController(request, reply) {
    try {
        if (!request.user) {
            return response_1.ResponseHelper.unauthorized(reply, 'Authentication required');
        }
        const { userId } = request.params;
        if (request.user.role !== 'super_admin') {
            return response_1.ResponseHelper.forbidden(reply, 'Super admin access required');
        }
        await (0, shared_hosting_1.getSharedHostingService)().deleteUser(userId);
        logger_1.logger.info(`Deleted shared hosting user: ${userId}`);
        return response_1.ResponseHelper.success(reply, { message: 'User deleted successfully' });
    }
    catch (error) {
        logger_1.logger.error('Delete shared user controller error:', error);
        if (error instanceof Error && error.message.includes('not found')) {
            return response_1.ResponseHelper.notFound(reply, 'Shared hosting user not found');
        }
        return response_1.ResponseHelper.internalError(reply, 'Failed to delete shared hosting user');
    }
}
async function getSharedHostingAnalyticsController(request, reply) {
    try {
        if (!request.user) {
            return response_1.ResponseHelper.unauthorized(reply, 'Authentication required');
        }
        if (request.user.role !== 'admin' && request.user.role !== 'super_admin') {
            return response_1.ResponseHelper.forbidden(reply, 'Admin access required');
        }
        const { period = 'month', server_id } = request.query;
        const analytics = await (0, shared_hosting_1.getSharedHostingService)().getAnalytics(period, server_id);
        logger_1.logger.info(`Retrieved shared hosting analytics for period: ${period}`);
        return response_1.ResponseHelper.success(reply, analytics);
    }
    catch (error) {
        logger_1.logger.error('Get shared hosting analytics controller error:', error);
        return response_1.ResponseHelper.internalError(reply, 'Failed to fetch analytics');
    }
}
async function getUserResourceUsageController(request, reply) {
    try {
        if (!request.user) {
            return response_1.ResponseHelper.unauthorized(reply, 'Authentication required');
        }
        const { userId } = request.params;
        const { period = 'week' } = request.query;
        if (request.user.sub !== userId && request.user.role !== 'admin' && request.user.role !== 'super_admin') {
            return response_1.ResponseHelper.forbidden(reply, 'Access denied');
        }
        const usage = await (0, shared_hosting_1.getSharedHostingService)().getUserResourceUsage(userId, period);
        logger_1.logger.info(`Retrieved resource usage for user: ${userId}, period: ${period}`);
        return response_1.ResponseHelper.success(reply, usage);
    }
    catch (error) {
        logger_1.logger.error('Get user resource usage controller error:', error);
        return response_1.ResponseHelper.internalError(reply, 'Failed to fetch resource usage');
    }
}
async function getServerCapacityController(request, reply) {
    try {
        if (!request.user) {
            return response_1.ResponseHelper.unauthorized(reply, 'Authentication required');
        }
        if (request.user.role !== 'admin' && request.user.role !== 'super_admin') {
            return response_1.ResponseHelper.forbidden(reply, 'Admin access required');
        }
        const { serverId } = request.params;
        const capacity = await (0, shared_hosting_1.getSharedHostingService)().getServerCapacity(serverId);
        logger_1.logger.info(`Retrieved server capacity${serverId ? ` for server: ${serverId}` : ' for all servers'}`);
        return response_1.ResponseHelper.success(reply, capacity);
    }
    catch (error) {
        logger_1.logger.error('Get server capacity controller error:', error);
        return response_1.ResponseHelper.internalError(reply, 'Failed to fetch server capacity');
    }
}
async function getUserApplicationsController(request, reply) {
    try {
        if (!request.user) {
            return response_1.ResponseHelper.unauthorized(reply, 'Authentication required');
        }
        const { userId } = request.params;
        const { page = '1', limit = '10', status, type } = request.query;
        if (request.user.sub !== userId && request.user.role !== 'admin' && request.user.role !== 'super_admin') {
            return response_1.ResponseHelper.forbidden(reply, 'Access denied');
        }
        const filters = {
            page: parseInt(page),
            limit: parseInt(limit)
        };
        if (status)
            filters.status = status;
        if (type)
            filters.type = type;
        const applications = await (0, shared_hosting_1.getSharedHostingService)().getUserApplications(userId, filters);
        logger_1.logger.info(`Retrieved applications for user: ${userId}`);
        return response_1.ResponseHelper.success(reply, applications);
    }
    catch (error) {
        logger_1.logger.error('Get user applications controller error:', error);
        return response_1.ResponseHelper.internalError(reply, 'Failed to fetch user applications');
    }
}
async function createApplicationController(request, reply) {
    try {
        if (!request.user) {
            return response_1.ResponseHelper.unauthorized(reply, 'Authentication required');
        }
        const { userId } = request.params;
        const appData = request.body;
        if (request.user.sub !== userId && request.user.role !== 'admin' && request.user.role !== 'super_admin') {
            return response_1.ResponseHelper.forbidden(reply, 'Access denied');
        }
        if (!appData.name || !appData.type) {
            return response_1.ResponseHelper.validationError(reply, 'Missing required fields: name, type');
        }
        const application = await (0, shared_hosting_1.getSharedHostingService)().createApplication(userId, appData);
        logger_1.logger.info(`Created application: ${appData.name} for user: ${userId}`);
        return response_1.ResponseHelper.success(reply, application, 201);
    }
    catch (error) {
        logger_1.logger.error('Create application controller error:', error);
        if (error instanceof Error) {
            if (error.message.includes('not found')) {
                return response_1.ResponseHelper.notFound(reply, 'Shared hosting user not found');
            }
            if (error.message.includes('already exists')) {
                return response_1.ResponseHelper.validationError(reply, error.message);
            }
        }
        return response_1.ResponseHelper.internalError(reply, 'Failed to create application');
    }
}
async function updateUserApplicationController(request, reply) {
    try {
        if (!request.user) {
            return response_1.ResponseHelper.unauthorized(reply, 'Authentication required');
        }
        const { userId, appId } = request.params;
        const updateData = request.body;
        if (request.user.sub !== userId && request.user.role !== 'admin' && request.user.role !== 'super_admin') {
            return response_1.ResponseHelper.forbidden(reply, 'Access denied');
        }
        const application = await (0, shared_hosting_1.getSharedHostingService)().updateApplication(userId, appId, updateData);
        logger_1.logger.info(`Updated application: ${appId} for user: ${userId}`);
        return response_1.ResponseHelper.success(reply, application);
    }
    catch (error) {
        logger_1.logger.error('Update user application controller error:', error);
        if (error instanceof Error && error.message.includes('not found')) {
            return response_1.ResponseHelper.notFound(reply, 'Application not found');
        }
        return response_1.ResponseHelper.internalError(reply, 'Failed to update application');
    }
}
async function deleteUserApplicationController(request, reply) {
    try {
        if (!request.user) {
            return response_1.ResponseHelper.unauthorized(reply, 'Authentication required');
        }
        const { userId, appId } = request.params;
        if (request.user.sub !== userId && request.user.role !== 'admin' && request.user.role !== 'super_admin') {
            return response_1.ResponseHelper.forbidden(reply, 'Access denied');
        }
        await (0, shared_hosting_1.getSharedHostingService)().deleteApplication(userId, appId);
        logger_1.logger.info(`Deleted application: ${appId} for user: ${userId}`);
        return response_1.ResponseHelper.success(reply, { message: 'Application deleted successfully' });
    }
    catch (error) {
        logger_1.logger.error('Delete user application controller error:', error);
        if (error instanceof Error && error.message.includes('not found')) {
            return response_1.ResponseHelper.notFound(reply, 'Application not found');
        }
        return response_1.ResponseHelper.internalError(reply, 'Failed to delete application');
    }
}
async function suspendUserController(request, reply) {
    try {
        if (!request.user) {
            return response_1.ResponseHelper.unauthorized(reply, 'Authentication required');
        }
        if (request.user.role !== 'admin' && request.user.role !== 'super_admin') {
            return response_1.ResponseHelper.forbidden(reply, 'Admin access required');
        }
        const { userId } = request.params;
        const { reason } = request.body;
        await (0, shared_hosting_1.getSharedHostingService)().suspendUser(userId, reason);
        logger_1.logger.info(`Suspended user: ${userId}, reason: ${reason || 'No reason provided'}`);
        return response_1.ResponseHelper.success(reply, { message: 'User suspended successfully' });
    }
    catch (error) {
        logger_1.logger.error('Suspend user controller error:', error);
        if (error instanceof Error && error.message.includes('not found')) {
            return response_1.ResponseHelper.notFound(reply, 'User not found');
        }
        return response_1.ResponseHelper.internalError(reply, 'Failed to suspend user');
    }
}
async function reactivateUserController(request, reply) {
    try {
        if (!request.user) {
            return response_1.ResponseHelper.unauthorized(reply, 'Authentication required');
        }
        if (request.user.role !== 'admin' && request.user.role !== 'super_admin') {
            return response_1.ResponseHelper.forbidden(reply, 'Admin access required');
        }
        const { userId } = request.params;
        await (0, shared_hosting_1.getSharedHostingService)().reactivateUser(userId);
        logger_1.logger.info(`Reactivated user: ${userId}`);
        return response_1.ResponseHelper.success(reply, { message: 'User reactivated successfully' });
    }
    catch (error) {
        logger_1.logger.error('Reactivate user controller error:', error);
        if (error instanceof Error && error.message.includes('not found')) {
            return response_1.ResponseHelper.notFound(reply, 'User not found');
        }
        return response_1.ResponseHelper.internalError(reply, 'Failed to reactivate user');
    }
}
async function resetUserPasswordController(request, reply) {
    try {
        if (!request.user) {
            return response_1.ResponseHelper.unauthorized(reply, 'Authentication required');
        }
        if (request.user.role !== 'admin' && request.user.role !== 'super_admin') {
            return response_1.ResponseHelper.forbidden(reply, 'Admin access required');
        }
        const { userId } = request.params;
        const { new_password } = request.body;
        const result = await (0, shared_hosting_1.getSharedHostingService)().resetUserPassword(userId, new_password);
        logger_1.logger.info(`Reset password for user: ${userId}`);
        return response_1.ResponseHelper.success(reply, result);
    }
    catch (error) {
        logger_1.logger.error('Reset user password controller error:', error);
        if (error instanceof Error && error.message.includes('not found')) {
            return response_1.ResponseHelper.notFound(reply, 'User not found');
        }
        return response_1.ResponseHelper.internalError(reply, 'Failed to reset user password');
    }
}
async function getServerPoolStatusController(request, reply) {
    try {
        if (!request.user) {
            return response_1.ResponseHelper.unauthorized(reply, 'Authentication required');
        }
        if (request.user.role !== 'admin' && request.user.role !== 'super_admin') {
            return response_1.ResponseHelper.forbidden(reply, 'Admin access required');
        }
        const serverPool = await (0, shared_hosting_1.getSharedHostingService)().getServerPoolStatus();
        return response_1.ResponseHelper.success(reply, serverPool);
    }
    catch (error) {
        logger_1.logger.error('Get server pool status error:', error);
        return response_1.ResponseHelper.internalError(reply, 'Failed to retrieve server pool status');
    }
}
async function addServerToPoolController(request, reply) {
    try {
        if (!request.user) {
            return response_1.ResponseHelper.unauthorized(reply, 'Authentication required');
        }
        if (request.user.role !== 'super_admin') {
            return response_1.ResponseHelper.forbidden(reply, 'Super admin access required');
        }
        const { id, ip_address, hostname, region, max_users, status = 'active' } = request.body;
        await (0, shared_hosting_1.getSharedHostingService)().addServerToPool({
            id,
            ip_address,
            hostname,
            region,
            max_users,
            status
        });
        return response_1.ResponseHelper.success(reply, { message: `Server ${id} added to pool successfully` });
    }
    catch (error) {
        logger_1.logger.error('Add server to pool error:', error);
        return response_1.ResponseHelper.internalError(reply, 'Failed to add server to pool');
    }
}
async function removeServerFromPoolController(request, reply) {
    try {
        if (!request.user) {
            return response_1.ResponseHelper.unauthorized(reply, 'Authentication required');
        }
        if (request.user.role !== 'super_admin') {
            return response_1.ResponseHelper.forbidden(reply, 'Super admin access required');
        }
        const { serverId } = request.params;
        await (0, shared_hosting_1.getSharedHostingService)().removeServerFromPool(serverId);
        return response_1.ResponseHelper.success(reply, { message: `Server ${serverId} removed from pool successfully` });
    }
    catch (error) {
        logger_1.logger.error('Remove server from pool error:', error);
        return response_1.ResponseHelper.internalError(reply, 'Failed to remove server from pool');
    }
}
async function synchronizeServersController(request, reply) {
    try {
        if (!request.user) {
            return response_1.ResponseHelper.unauthorized(reply, 'Authentication required');
        }
        if (request.user.role !== 'admin' && request.user.role !== 'super_admin') {
            return response_1.ResponseHelper.forbidden(reply, 'Admin access required');
        }
        await (0, shared_hosting_1.getSharedHostingService)().synchronizeServers();
        return response_1.ResponseHelper.success(reply, { message: 'Server synchronization completed successfully' });
    }
    catch (error) {
        logger_1.logger.error('Server synchronization error:', error);
        return response_1.ResponseHelper.internalError(reply, 'Failed to synchronize servers');
    }
}
async function getUserPaymentStatusController(request, reply) {
    try {
        if (!request.user) {
            return response_1.ResponseHelper.unauthorized(reply, 'Authentication required');
        }
        const { userId } = request.params;
        if (request.user.sub !== userId && request.user.role !== 'admin' && request.user.role !== 'super_admin') {
            return response_1.ResponseHelper.forbidden(reply, 'Access denied');
        }
        const paymentStatus = (0, shared_hosting_1.getSharedHostingService)().getUserPaymentStatus(userId);
        if (!paymentStatus) {
            return response_1.ResponseHelper.notFound(reply, 'Payment status not found');
        }
        return response_1.ResponseHelper.success(reply, paymentStatus);
    }
    catch (error) {
        logger_1.logger.error('Get user payment status error:', error);
        return response_1.ResponseHelper.internalError(reply, 'Failed to retrieve payment status');
    }
}
async function updateUserPaymentStatusController(request, reply) {
    try {
        if (!request.user) {
            return response_1.ResponseHelper.unauthorized(reply, 'Authentication required');
        }
        if (request.user.role !== 'admin' && request.user.role !== 'super_admin') {
            return response_1.ResponseHelper.forbidden(reply, 'Admin access required');
        }
        const { userId } = request.params;
        const { status, grace_period_days } = request.body;
        await (0, shared_hosting_1.getSharedHostingService)().updateUserPaymentStatus(userId, status, grace_period_days);
        return response_1.ResponseHelper.success(reply, { message: `Payment status updated to ${status} for user ${userId}` });
    }
    catch (error) {
        logger_1.logger.error('Update user payment status error:', error);
        return response_1.ResponseHelper.internalError(reply, 'Failed to update payment status');
    }
}
async function testSSHConnectionController(request, reply) {
    try {
        if (!request.user) {
            return response_1.ResponseHelper.unauthorized(reply, 'Authentication required');
        }
        if (request.user.role !== 'admin' && request.user.role !== 'super_admin') {
            return response_1.ResponseHelper.forbidden(reply, 'Admin access required');
        }
        logger_1.logger.info('Testing SSH connection...');
        const result = await (0, shared_hosting_1.getSharedHostingService)().testSSHConnection();
        logger_1.logger.info('SSH connection test completed');
        return response_1.ResponseHelper.success(reply, result);
    }
    catch (error) {
        logger_1.logger.error('SSH connection test controller error:', error);
        return response_1.ResponseHelper.internalError(reply, 'SSH connection test failed');
    }
}
//# sourceMappingURL=hosting.controller.js.map