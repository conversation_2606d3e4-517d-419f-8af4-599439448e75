"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SSHConnection = void 0;
exports.getSharedHostingService = getSharedHostingService;
const logger_1 = require("../utils/logger");
const child_process_1 = require("child_process");
const util_1 = require("util");
const fs = __importStar(require("fs/promises"));
const path = __importStar(require("path"));
const ssh2_1 = require("ssh2");
const config_1 = require("../config");
const mongoose_1 = __importDefault(require("mongoose"));
const shared_hosting_1 = require("../models/shared-hosting");
const application_1 = require("./application");
const cloudflare_dns_1 = require("./cloudflare-dns");
const execAsync = (0, util_1.promisify)(child_process_1.exec);
class SSHConnection {
    client;
    config;
    constructor() {
        this.client = new ssh2_1.Client();
        const sharedServerIP = process.env['SHARED_SERVER_IP'] || '*************';
        const sshPort = parseInt(process.env['SHARED_SERVER_SSH_PORT'] || '22');
        const sshUser = process.env['SHARED_SERVER_SSH_USER'] || 'root';
        const sshPassword = process.env['SHARED_SERVER_SSH_PASSWORD'];
        const sshKeyPath = process.env['SHARED_SERVER_SSH_KEY_PATH'];
        this.config = {
            host: sharedServerIP,
            port: sshPort,
            username: sshUser,
            ...(sshPassword && { password: sshPassword }),
            ...(sshKeyPath && {
                privateKey: require('fs').readFileSync(sshKeyPath)
            })
        };
        logger_1.logger.info(`SSH Configuration initialized:`, {
            host: this.config.host,
            port: this.config.port,
            username: this.config.username,
            hasPassword: !!this.config.password,
            hasPrivateKey: !!this.config.privateKey
        });
    }
    async connect() {
        return new Promise((resolve, reject) => {
            logger_1.logger.info(`🔌 Attempting SSH connection to ${this.config.host}:${this.config.port} as ${this.config.username}`);
            logger_1.logger.info(`🔧 SSH Config: host=${this.config.host}, port=${this.config.port}, username=${this.config.username}, hasPassword=${!!this.config.password}`);
            const timeout = setTimeout(() => {
                logger_1.logger.error(`❌ SSH connection timeout after 30 seconds to ${this.config.host}`);
                this.client.destroy();
                reject(new Error(`SSH connection timeout to ${this.config.host}`));
            }, 30000);
            this.client.on('ready', () => {
                clearTimeout(timeout);
                logger_1.logger.info(`✅ SSH connection established successfully to ${this.config.host}:${this.config.port}`);
                resolve();
            });
            this.client.on('error', (err) => {
                clearTimeout(timeout);
                logger_1.logger.error(`❌ SSH connection failed to ${this.config.host}:${this.config.port}:`, {
                    error: err.message,
                    code: err.code,
                    level: err.level,
                    description: err.description
                });
                reject(err);
            });
            this.client.on('close', () => {
                logger_1.logger.info(`🔌 SSH connection closed to ${this.config.host}`);
            });
            this.client.on('end', () => {
                logger_1.logger.info(`🔌 SSH connection ended to ${this.config.host}`);
            });
            try {
                logger_1.logger.info(`🚀 Initiating SSH connection to ${this.config.host}...`);
                this.client.connect(this.config);
            }
            catch (error) {
                clearTimeout(timeout);
                logger_1.logger.error(`❌ Failed to initiate SSH connection to ${this.config.host}:`, error);
                reject(error);
            }
        });
    }
    async executeCommand(command) {
        return new Promise((resolve, reject) => {
            this.client.exec(command, (err, stream) => {
                if (err) {
                    reject(err);
                    return;
                }
                let stdout = '';
                let stderr = '';
                stream.on('close', (code) => {
                    if (code !== 0 && stderr.trim()) {
                        logger_1.logger.warn(`Command exited with code ${code}: ${command}`, { stderr: stderr.trim() });
                    }
                    resolve(stdout);
                });
                stream.on('data', (data) => {
                    stdout += data.toString();
                });
                stream.stderr.on('data', (data) => {
                    stderr += data.toString();
                });
            });
        });
    }
    async executeCommandDetailed(command) {
        return new Promise((resolve, reject) => {
            this.client.exec(command, (err, stream) => {
                if (err) {
                    reject(err);
                    return;
                }
                let stdout = '';
                let stderr = '';
                stream.on('close', (code) => {
                    if (code !== 0) {
                        logger_1.logger.warn(`Command exited with code ${code}: ${command}`);
                    }
                    resolve({ stdout, stderr });
                });
                stream.on('data', (data) => {
                    stdout += data.toString();
                });
                stream.stderr.on('data', (data) => {
                    stderr += data.toString();
                });
            });
        });
    }
    async disconnect() {
        this.client.end();
        logger_1.logger.info('SSH connection closed');
    }
}
exports.SSHConnection = SSHConnection;
class SharedHostingService {
    BASE_PORT = config_1.appConfig.sharedHosting.basePort;
    SSH_BASE_PORT = config_1.appConfig.sharedHosting.sshBasePort;
    FTP_BASE_PORT = config_1.appConfig.sharedHosting.ftpBasePort;
    MAX_USERS_PER_SERVER = config_1.appConfig.sharedHosting.maxUsersPerServer;
    serverPool = [];
    paymentStatuses = new Map();
    constructor() {
        this.initializeServerPool();
        this.initializePaymentTracking();
        this.initializeProductionServices();
    }
    initializeProductionServices() {
        this.startAnalyticsCollection();
        this.initializeServerMetrics();
        logger_1.logger.info('All production services initialized');
    }
    async initializeServerMetrics() {
        try {
            const metricsInterval = setInterval(async () => {
                try {
                    await this.updateServerMetrics();
                }
                catch (error) {
                    logger_1.logger.error('Server metrics update error:', error);
                }
            }, 2 * 60 * 1000);
            this.metricsInterval = metricsInterval;
            logger_1.logger.info('Server metrics collection initialized');
        }
        catch (error) {
            logger_1.logger.error('Failed to initialize server metrics:', error);
        }
    }
    async initializeServerPool() {
        try {
            const serverConfig = process.env['SHARED_HOSTING_SERVERS'];
            if (serverConfig) {
                const servers = JSON.parse(serverConfig);
                this.serverPool = servers.map((server) => ({
                    ...server,
                    last_sync: new Date(),
                    created_at: new Date()
                }));
            }
            else {
                const defaultServerIp = process.env['SHARED_SERVER_IP'] || '*************';
                this.serverPool = [{
                        id: 'default-shared-01',
                        ip_address: defaultServerIp,
                        hostname: 'achidas-shared-1',
                        region: 'default',
                        status: 'active',
                        max_users: this.MAX_USERS_PER_SERVER,
                        current_users: 0,
                        cpu_usage: 0,
                        memory_usage: 0,
                        disk_usage: 0,
                        load_average: 0,
                        last_sync: new Date(),
                        created_at: new Date()
                    }];
            }
            logger_1.logger.info(`Initialized server pool with ${this.serverPool.length} servers`);
        }
        catch (error) {
            logger_1.logger.error('Failed to initialize server pool:', error);
            const defaultServerIp = process.env['SHARED_SERVER_IP'] || '*************';
            this.serverPool = [{
                    id: 'default-shared-01',
                    ip_address: defaultServerIp,
                    hostname: 'achidas-shared-1',
                    region: 'default',
                    status: 'active',
                    max_users: this.MAX_USERS_PER_SERVER,
                    current_users: 0,
                    cpu_usage: 0,
                    memory_usage: 0,
                    disk_usage: 0,
                    load_average: 0,
                    last_sync: new Date(),
                    created_at: new Date()
                }];
        }
    }
    async initializePaymentTracking() {
        try {
            const checkInterval = config_1.appConfig.payment.checkIntervalHours * 60 * 60 * 1000;
            setInterval(async () => {
                try {
                    await this.processPaymentBasedLifecycle();
                }
                catch (error) {
                    logger_1.logger.error('Payment lifecycle processing failed:', error);
                }
            }, checkInterval);
            logger_1.logger.info(`Payment tracking system initialized with ${config_1.appConfig.payment.checkIntervalHours}h check interval`);
        }
        catch (error) {
            logger_1.logger.error('Failed to initialize payment tracking:', error);
        }
    }
    async getOptimalServer() {
        logger_1.logger.info(`🎯 Getting optimal server from pool of ${this.serverPool.length} servers`);
        try {
            logger_1.logger.info(`📊 Updating server metrics before selection...`);
            await this.updateServerMetrics();
            logger_1.logger.info(`✅ Server metrics updated successfully`);
            logger_1.logger.info(`🔍 Filtering active servers with capacity...`);
            const availableServers = this.serverPool.filter(server => server.status === 'active' &&
                server.current_users < server.max_users);
            logger_1.logger.info(`📋 Found ${availableServers.length} available servers:`, {
                total_servers: this.serverPool.length,
                available_servers: availableServers.length,
                server_statuses: this.serverPool.map(s => ({ id: s.id, status: s.status, users: `${s.current_users}/${s.max_users}` }))
            });
            if (availableServers.length === 0) {
                logger_1.logger.error(`❌ No available servers with capacity found`);
                throw new Error('No available servers with capacity');
            }
            if (availableServers.length === 1) {
                logger_1.logger.info(`🎯 Single server mode - using server: ${availableServers[0].id}`);
                return availableServers[0];
            }
            logger_1.logger.info(`⚖️ Load balancing between ${availableServers.length} servers...`);
            const sortedServers = availableServers.sort((a, b) => {
                const utilizationA = (a.current_users / a.max_users) + (a.cpu_usage / 100) + (a.memory_usage / 100);
                const utilizationB = (b.current_users / b.max_users) + (b.cpu_usage / 100) + (b.memory_usage / 100);
                return utilizationA - utilizationB;
            });
            logger_1.logger.info(`🎯 Selected optimal server: ${sortedServers[0].id}`, {
                server_id: sortedServers[0].id,
                utilization: {
                    users: `${sortedServers[0].current_users}/${sortedServers[0].max_users}`,
                    cpu: `${sortedServers[0].cpu_usage}%`,
                    memory: `${sortedServers[0].memory_usage}%`
                }
            });
            return sortedServers[0];
        }
        catch (error) {
            logger_1.logger.error(`❌ Failed to get optimal server:`, {
                error: error instanceof Error ? error.message : String(error),
                stack: error instanceof Error ? error.stack : undefined,
                server_pool_size: this.serverPool.length
            });
            throw error;
        }
    }
    async updateServerMetrics() {
        logger_1.logger.info(`🔄 Updating metrics for ${this.serverPool.length} servers...`);
        const updatePromises = this.serverPool.map(async (server) => {
            try {
                logger_1.logger.info(`📊 Updating metrics for server ${server.id} (${server.ip_address})`);
                const ssh = new SSHConnection();
                ssh.config.host = server.ip_address;
                logger_1.logger.info(`🔌 Connecting to server ${server.id} at ${server.ip_address}...`);
                await ssh.connect();
                logger_1.logger.info(`✅ Connected to server ${server.id} successfully`);
                logger_1.logger.info(`👥 Getting user count for server ${server.id}...`);
                const userCountResult = await ssh.executeCommand("ls /home | grep '^user_' | wc -l");
                server.current_users = parseInt(userCountResult.trim()) || 0;
                logger_1.logger.info(`👥 Server ${server.id} has ${server.current_users} users`);
                const cpuResult = await ssh.executeCommand("top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1");
                server.cpu_usage = parseFloat(cpuResult.trim()) || 0;
                const memResult = await ssh.executeCommand("free | grep Mem | awk '{printf \"%.1f\", $3/$2 * 100.0}'");
                server.memory_usage = parseFloat(memResult.trim()) || 0;
                const diskResult = await ssh.executeCommand("df / | tail -1 | awk '{print $5}' | cut -d'%' -f1");
                server.disk_usage = parseFloat(diskResult.trim()) || 0;
                const loadResult = await ssh.executeCommand("uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | cut -d',' -f1");
                server.load_average = parseFloat(loadResult.trim()) || 0;
                server.last_sync = new Date();
                await ssh.disconnect();
                logger_1.logger.info(`✅ Metrics updated for server ${server.id}:`, {
                    users: server.current_users,
                    cpu: server.cpu_usage,
                    memory: server.memory_usage,
                    disk: server.disk_usage,
                    load: server.load_average
                });
            }
            catch (error) {
                logger_1.logger.error(`❌ Failed to update metrics for server ${server.id} (${server.ip_address}):`, {
                    error: error instanceof Error ? error.message : String(error),
                    stack: error instanceof Error ? error.stack : undefined
                });
                server.status = 'offline';
            }
        });
        await Promise.allSettled(updatePromises);
        logger_1.logger.info(`🔄 Server metrics update completed`);
    }
    async updateUserPaymentStatus(userId, status, gracePeriodDays) {
        const paymentStatus = this.paymentStatuses.get(userId);
        if (!paymentStatus) {
            throw new Error(`Payment status not found for user: ${userId}`);
        }
        const now = new Date();
        switch (status) {
            case 'active':
                paymentStatus.status = 'active';
                paymentStatus.last_payment_date = now;
                delete paymentStatus.grace_period_start;
                delete paymentStatus.grace_period_end;
                delete paymentStatus.deletion_scheduled;
                break;
            case 'grace_period':
                paymentStatus.status = 'grace_period';
                paymentStatus.grace_period_start = now;
                paymentStatus.grace_period_days = gracePeriodDays || 30;
                const graceEnd = new Date(now);
                graceEnd.setDate(graceEnd.getDate() + paymentStatus.grace_period_days);
                paymentStatus.grace_period_end = graceEnd;
                const deletionDate = new Date(graceEnd);
                deletionDate.setDate(deletionDate.getDate() + 7);
                paymentStatus.deletion_scheduled = deletionDate;
                break;
            case 'suspended':
                paymentStatus.status = 'suspended';
                break;
            case 'deleted':
                paymentStatus.status = 'deleted';
                break;
        }
        this.paymentStatuses.set(userId, paymentStatus);
        logger_1.logger.info(`Updated payment status for user ${userId} to: ${status}`);
    }
    async processPaymentBasedLifecycle() {
        const now = new Date();
        for (const [userId, paymentStatus] of Array.from(this.paymentStatuses.entries())) {
            try {
                switch (paymentStatus.status) {
                    case 'active':
                        const daysSinceLastPayment = Math.floor((now.getTime() - (paymentStatus.last_payment_date?.getTime() || 0)) / (1000 * 60 * 60 * 24));
                        if (daysSinceLastPayment > 35) {
                            await this.updateUserPaymentStatus(userId, 'grace_period');
                            await this.suspendUser(userId, 'Payment overdue - entering grace period');
                        }
                        break;
                    case 'grace_period':
                        if (paymentStatus.grace_period_end && now > paymentStatus.grace_period_end) {
                            await this.updateUserPaymentStatus(userId, 'suspended');
                            logger_1.logger.warn(`User ${userId} grace period expired, scheduling for deletion`);
                        }
                        break;
                    case 'suspended':
                        if (paymentStatus.deletion_scheduled && now > paymentStatus.deletion_scheduled) {
                            await this.deleteUser(userId);
                            await this.updateUserPaymentStatus(userId, 'deleted');
                            logger_1.logger.info(`User ${userId} deleted due to non-payment`);
                        }
                        break;
                }
            }
            catch (error) {
                logger_1.logger.error(`Failed to process payment lifecycle for user ${userId}:`, error);
            }
        }
    }
    getUserPaymentStatus(userId) {
        return this.paymentStatuses.get(userId);
    }
    getServerPoolStatus() {
        return [...this.serverPool];
    }
    async addServerToPool(serverConfig) {
        const newServer = {
            ...serverConfig,
            current_users: 0,
            cpu_usage: 0,
            memory_usage: 0,
            disk_usage: 0,
            load_average: 0,
            last_sync: new Date(),
            created_at: new Date()
        };
        this.serverPool.push(newServer);
        logger_1.logger.info(`Added new server to pool: ${newServer.id}`);
    }
    async removeServerFromPool(serverId) {
        const serverIndex = this.serverPool.findIndex(server => server.id === serverId);
        if (serverIndex === -1) {
            throw new Error(`Server not found: ${serverId}`);
        }
        const server = this.serverPool[serverIndex];
        if (server.current_users > 0) {
            throw new Error(`Cannot remove server ${serverId} - still has ${server.current_users} users`);
        }
        this.serverPool.splice(serverIndex, 1);
        logger_1.logger.info(`Removed server from pool: ${serverId}`);
    }
    async synchronizeServers() {
        if (this.serverPool.length <= 1) {
            logger_1.logger.info('Single server mode - no synchronization needed');
            return;
        }
        const activeServers = this.serverPool.filter(server => server.status === 'active');
        if (activeServers.length <= 1) {
            logger_1.logger.info('Only one active server - no synchronization needed');
            return;
        }
        const primaryServer = activeServers[0];
        const secondaryServers = activeServers.slice(1);
        logger_1.logger.info(`Starting synchronization from primary server: ${primaryServer.id}`);
        const syncPromises = secondaryServers.map(async (targetServer) => {
            try {
                await this.syncServerToServer(primaryServer, targetServer);
                logger_1.logger.info(`Synchronized ${primaryServer.id} -> ${targetServer.id}`);
            }
            catch (error) {
                logger_1.logger.error(`Failed to sync ${primaryServer.id} -> ${targetServer.id}:`, error);
            }
        });
        await Promise.allSettled(syncPromises);
        logger_1.logger.info('Server synchronization completed');
    }
    async syncServerToServer(sourceServer, targetServer) {
        const ssh = new SSHConnection();
        try {
            ssh.config.host = targetServer.ip_address;
            await ssh.connect();
            const rsyncCommand = `rsync -avz --delete -e "ssh -o StrictHostKeyChecking=no" root@${sourceServer.ip_address}:/var/www/ /var/www/`;
            await ssh.executeCommand(rsyncCommand);
            const userSyncCommands = [
                `rsync -avz -e "ssh -o StrictHostKeyChecking=no" root@${sourceServer.ip_address}:/etc/passwd /etc/passwd.new`,
                `rsync -avz -e "ssh -o StrictHostKeyChecking=no" root@${sourceServer.ip_address}:/etc/shadow /etc/shadow.new`,
                `rsync -avz -e "ssh -o StrictHostKeyChecking=no" root@${sourceServer.ip_address}:/etc/group /etc/group.new`,
                `cp /etc/passwd /etc/passwd.backup`,
                `cp /etc/shadow /etc/shadow.backup`,
                `cp /etc/group /etc/group.backup`,
                `mv /etc/passwd.new /etc/passwd`,
                `mv /etc/shadow.new /etc/shadow`,
                `mv /etc/group.new /etc/group`
            ];
            for (const command of userSyncCommands) {
                await ssh.executeCommand(command);
            }
            targetServer.last_sync = new Date();
        }
        catch (error) {
            logger_1.logger.error(`Rsync failed between ${sourceServer.id} and ${targetServer.id}:`, error);
            throw error;
        }
        finally {
            await ssh.disconnect();
        }
    }
    startAutoSync() {
        if (this.syncInterval) {
            clearInterval(this.syncInterval);
        }
        this.syncInterval = setInterval(async () => {
            try {
                await this.synchronizeServers();
            }
            catch (error) {
                logger_1.logger.error('Auto-sync failed:', error);
            }
        }, 5 * 60 * 1000);
        logger_1.logger.info('Auto-sync started (5-minute intervals)');
    }
    stopAutoSync() {
        if (this.syncInterval) {
            clearInterval(this.syncInterval);
            this.syncInterval = null;
            logger_1.logger.info('Auto-sync stopped');
        }
    }
    startPaymentLifecycleMonitoring() {
        if (this.paymentInterval) {
            clearInterval(this.paymentInterval);
        }
        this.paymentInterval = setInterval(async () => {
            try {
                await this.processPaymentBasedLifecycle();
            }
            catch (error) {
                logger_1.logger.error('Payment lifecycle processing failed:', error);
            }
        }, 24 * 60 * 60 * 1000);
        logger_1.logger.info('Payment lifecycle monitoring started (daily checks)');
    }
    stopPaymentLifecycleMonitoring() {
        if (this.paymentInterval) {
            clearInterval(this.paymentInterval);
            this.paymentInterval = null;
            logger_1.logger.info('Payment lifecycle monitoring stopped');
        }
    }
    async createUser(userData) {
        const startTime = Date.now();
        logger_1.logger.info(`Starting user creation process for: ${userData.username}`, {
            user_id: userData.user_id,
            username: userData.username,
            plan: userData.plan
        });
        try {
            logger_1.logger.info(`🔍 Checking for existing user: ${userData.username}`, {
                user_id: userData.user_id,
                username: userData.username
            });
            logger_1.logger.info(`🔍 Proceeding with user creation for: ${userData.username}`);
            logger_1.logger.info(`Getting optimal server for user: ${userData.username}`);
            const selectedServer = await this.getOptimalServer();
            logger_1.logger.info(`Selected server for user ${userData.username}:`, {
                server_id: selectedServer.id,
                server_ip: selectedServer.ip_address,
                current_users: selectedServer.current_users,
                max_users: selectedServer.max_users
            });
            const linuxUsername = `user_${userData.username.toLowerCase().replace(/[^a-z0-9]/g, '')}`;
            const homeDirectory = `/var/www/${linuxUsername}`;
            const port = await this.getNextAvailablePort();
            const sshPort = await this.getNextAvailableSSHPort();
            const ftpPort = await this.getNextAvailableFTPPort();
            logger_1.logger.info(`Generated user configuration for ${userData.username}:`, {
                linux_username: linuxUsername,
                home_directory: homeDirectory,
                port,
                ssh_port: sshPort,
                ftp_port: ftpPort
            });
            logger_1.logger.info(`Creating database record for user: ${userData.username}`);
            const newUser = new shared_hosting_1.SharedHostingUser({
                user_id: userData.user_id,
                username: userData.username,
                linux_username: linuxUsername,
                email: `${userData.username}@poolot.com`,
                home_directory: homeDirectory,
                plan: userData.plan,
                status: 'pending',
                server_id: selectedServer.id,
                server_ip: selectedServer.ip_address,
                port,
                ssh_port: sshPort,
                ftp_port: ftpPort,
                resource_limits: this.getPlanLimits(userData.plan),
                usage: {
                    cpu_usage: 0,
                    memory_usage: 0,
                    bandwidth_used: 0,
                    storage_used: 0
                },
                applications: []
            });
            logger_1.logger.info(`🔍 Checking for existing users with same identifiers...`);
            try {
                const existingUserByUserId = await shared_hosting_1.SharedHostingUser.findOne({ user_id: userData.user_id });
                const existingUserByUsername = await shared_hosting_1.SharedHostingUser.findOne({ username: userData.username });
                const existingUserByLinuxUsername = await shared_hosting_1.SharedHostingUser.findOne({ linux_username: linuxUsername });
                if (existingUserByUserId) {
                    logger_1.logger.error(`❌ User with user_id ${userData.user_id} already exists`);
                    throw new Error(`User with user_id ${userData.user_id} already exists`);
                }
                if (existingUserByUsername) {
                    logger_1.logger.error(`❌ User with username ${userData.username} already exists`);
                    throw new Error(`User with username ${userData.username} already exists`);
                }
                if (existingUserByLinuxUsername) {
                    logger_1.logger.error(`❌ User with linux_username ${linuxUsername} already exists`);
                    throw new Error(`User with linux_username ${linuxUsername} already exists`);
                }
                logger_1.logger.info(`✅ No existing users found with same identifiers`);
            }
            catch (checkError) {
                logger_1.logger.error(`❌ Error checking for existing users:`, {
                    error: checkError instanceof Error ? checkError.message : String(checkError),
                    stack: checkError instanceof Error ? checkError.stack : undefined
                });
                throw checkError;
            }
            logger_1.logger.info(`Saving user to database: ${userData.username}`);
            let savedUser;
            try {
                const savePromise = newUser.save();
                const timeoutPromise = new Promise((_, reject) => {
                    setTimeout(() => reject(new Error('Database save timeout after 15 seconds')), 15000);
                });
                logger_1.logger.info(`🔄 Starting database save operation for: ${userData.username}`);
                savedUser = await Promise.race([savePromise, timeoutPromise]);
                logger_1.logger.info(`✅ User saved to database successfully: ${userData.username}`, {
                    database_id: savedUser._id,
                    user_id: savedUser.user_id,
                    status: savedUser.status
                });
            }
            catch (dbError) {
                logger_1.logger.error(`❌ Database save failed for user: ${userData.username}`, {
                    error: dbError instanceof Error ? dbError.message : String(dbError),
                    stack: dbError instanceof Error ? dbError.stack : undefined,
                    userData: userData,
                    mongooseConnectionState: mongoose_1.default.connection.readyState,
                    mongooseConnectionName: mongoose_1.default.connection.name,
                    mongooseConnectionHost: mongoose_1.default.connection.host,
                    mongooseConnectionPort: mongoose_1.default.connection.port
                });
                throw new Error(`Database save failed: ${dbError instanceof Error ? dbError.message : 'Unknown error'}`);
            }
            try {
                logger_1.logger.info(`Starting SSH operations for user: ${userData.username}`);
                const sshStartTime = Date.now();
                logger_1.logger.info(`Creating Linux user: ${linuxUsername}`);
                await this.createLinuxUser(linuxUsername, homeDirectory, selectedServer);
                logger_1.logger.info(`Linux user created successfully: ${linuxUsername}`);
                logger_1.logger.info(`Setting up resource limits for: ${linuxUsername}`);
                await this.setupResourceLimits(linuxUsername, userData.plan);
                logger_1.logger.info(`Resource limits configured for: ${linuxUsername}`);
                logger_1.logger.info(`Creating directory structure for: ${linuxUsername}`);
                await this.createUserDirectoryStructure(homeDirectory, linuxUsername, selectedServer);
                logger_1.logger.info(`Directory structure created for: ${linuxUsername}`);
                logger_1.logger.info(`Setting up bandwidth throttling for: ${linuxUsername}`);
                await this.setupBandwidthThrottling(linuxUsername, port);
                logger_1.logger.info(`Bandwidth throttling configured for: ${linuxUsername}`);
                const sshDuration = Date.now() - sshStartTime;
                logger_1.logger.info(`All SSH operations completed for: ${userData.username}`, {
                    duration_ms: sshDuration,
                    linux_username: linuxUsername
                });
                logger_1.logger.info(`Updating user status to active: ${userData.username}`);
                savedUser.status = 'active';
                await savedUser.save();
                logger_1.logger.info(`Updating server metrics for: ${selectedServer.id}`);
                await this.updateServerUserCount(selectedServer.id, 1);
                logger_1.logger.info(`Creating payment status for: ${userData.username}`);
                await this.createPaymentStatus(userData.user_id, userData.plan);
                const totalDuration = Date.now() - startTime;
                logger_1.logger.info(`User creation completed successfully: ${userData.username}`, {
                    total_duration_ms: totalDuration,
                    ssh_duration_ms: sshDuration,
                    linux_username: linuxUsername,
                    home_directory: homeDirectory,
                    server_id: selectedServer.id,
                    final_status: 'active'
                });
                return this.formatUserResponse(savedUser);
            }
            catch (sshError) {
                savedUser.status = 'deleted';
                await savedUser.save();
                logger_1.logger.error(`SSH setup failed for user ${linuxUsername}, marked as deleted:`, sshError);
                throw new Error(`Failed to setup user environment: ${sshError instanceof Error ? sshError.message : 'Unknown error'}`);
            }
        }
        catch (error) {
            logger_1.logger.error('Failed to create shared hosting user:', error);
            throw error;
        }
    }
    formatUserResponse(user) {
        return {
            id: user._id.toString(),
            user_id: user.user_id,
            username: user.username,
            linux_username: user.linux_username,
            home_directory: user.home_directory,
            plan: user.plan,
            status: this.mapStatus(user.status),
            server_id: user.server_id,
            server_ip: user.server_ip,
            port: user.port,
            ssh_port: user.ssh_port,
            ftp_port: user.ftp_port,
            resource_limits: user.resource_limits,
            usage: user.usage,
            created_at: user.created_at.toISOString(),
            applications: (user.applications || []).map((app) => ({
                id: app._id?.toString() || app.id,
                name: app.name,
                type: app.type,
                framework: app.framework,
                domain: app.domain,
                subdomain: app.subdomain,
                directory: app.directory,
                status: app.status,
                port: app.port,
                ssl_enabled: app.ssl_enabled,
                created_at: app.created_at?.toISOString(),
                last_deployed: app.last_deployed?.toISOString()
            }))
        };
    }
    mapStatus(dbStatus) {
        switch (dbStatus) {
            case 'active': return 'active';
            case 'suspended': return 'suspended';
            case 'deleted':
            case 'pending':
            default: return 'disabled';
        }
    }
    async updateServerUserCount(serverId, increment) {
        try {
            await shared_hosting_1.ServerMetrics.findOneAndUpdate({ server_id: serverId }, {
                $inc: { current_users: increment },
                $set: { last_updated: new Date() }
            }, { upsert: true });
        }
        catch (error) {
            logger_1.logger.error(`Failed to update server user count for ${serverId}:`, error);
        }
    }
    async createPaymentStatus(userId, plan) {
        try {
            const paymentStatus = new shared_hosting_1.PaymentStatus({
                user_id: userId,
                plan: plan,
                status: 'active',
                last_payment: new Date(),
                next_payment: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
                grace_period_end: null,
                deletion_scheduled: null
            });
            await paymentStatus.save();
            logger_1.logger.info(`Created payment status for user: ${userId}`);
        }
        catch (error) {
            logger_1.logger.error(`Failed to create payment status for user ${userId}:`, error);
        }
    }
    getStartDateForPeriod(period) {
        const now = new Date();
        switch (period) {
            case '24h':
                return new Date(now.getTime() - 24 * 60 * 60 * 1000);
            case '7d':
                return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
            case '30d':
                return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
            case '90d':
                return new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
            default:
                return new Date(now.getTime() - 24 * 60 * 60 * 1000);
        }
    }
    async collectUsageAnalytics() {
        try {
            logger_1.logger.info('Starting usage analytics collection...');
            const activeUsers = await shared_hosting_1.SharedHostingUser.find({
                status: { $in: ['active', 'suspended'] }
            }).lean();
            for (const user of activeUsers) {
                try {
                    const usageData = await this.collectUserUsageData(user.user_id);
                    await this.storeUsageAnalytics(user.user_id, usageData);
                }
                catch (userError) {
                    logger_1.logger.error(`Failed to collect usage for user ${user.user_id}:`, userError);
                }
            }
            logger_1.logger.info(`Completed usage analytics collection for ${activeUsers.length} users`);
        }
        catch (error) {
            logger_1.logger.error('Failed to collect usage analytics:', error);
        }
    }
    async collectUserUsageData(userId) {
        const ssh = new SSHConnection();
        try {
            await ssh.connect();
            const user = await shared_hosting_1.SharedHostingUser.findOne({ user_id: userId });
            if (!user) {
                throw new Error('User not found');
            }
            const cpuUsage = await ssh.executeCommand(`ps -u ${user.linux_username} -o %cpu --no-headers | awk '{sum += $1} END {print sum}'`);
            const memoryUsage = await ssh.executeCommand(`ps -u ${user.linux_username} -o %mem --no-headers | awk '{sum += $1} END {print sum}'`);
            const diskUsage = await ssh.executeCommand(`du -s ${user.home_directory} | awk '{print $1}'`);
            const networkStats = await ssh.executeCommand(`cat /proc/net/dev | grep -E "(eth0|ens)" | awk '{print $2, $10}'`);
            return {
                cpu_usage_avg: parseFloat(cpuUsage.trim()) || 0,
                cpu_usage_max: parseFloat(cpuUsage.trim()) || 0,
                memory_usage_avg: parseFloat(memoryUsage.trim()) || 0,
                memory_usage_max: parseFloat(memoryUsage.trim()) || 0,
                storage_used: parseInt(diskUsage.trim()) || 0,
                bandwidth_used: this.parseNetworkStats(networkStats),
                requests_count: 0,
                uptime_percentage: 100,
                error_rate: 0
            };
        }
        catch (error) {
            logger_1.logger.error(`Failed to collect usage data for user ${userId}:`, error);
            return {
                cpu_usage_avg: 0,
                cpu_usage_max: 0,
                memory_usage_avg: 0,
                memory_usage_max: 0,
                storage_used: 0,
                bandwidth_used: 0,
                requests_count: 0,
                uptime_percentage: 100,
                error_rate: 0
            };
        }
        finally {
            await ssh.disconnect();
        }
    }
    async storeUsageAnalytics(userId, usageData) {
        try {
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            await shared_hosting_1.UserUsageAnalytics.findOneAndUpdate({ user_id: userId, date: today }, {
                user_id: userId,
                date: today,
                ...usageData
            }, { upsert: true, new: true });
            logger_1.logger.debug(`Stored usage analytics for user: ${userId}`);
        }
        catch (error) {
            logger_1.logger.error(`Failed to store usage analytics for user ${userId}:`, error);
        }
    }
    parseNetworkStats(networkStats) {
        try {
            const lines = networkStats.trim().split('\n');
            let totalBytes = 0;
            for (const line of lines) {
                const parts = line.trim().split(/\s+/);
                if (parts.length >= 2 && parts[0] && parts[1]) {
                    totalBytes += parseInt(parts[0]) + parseInt(parts[1]);
                }
            }
            return Math.round(totalBytes / (1024 * 1024));
        }
        catch (error) {
            return 0;
        }
    }
    startAnalyticsCollection() {
        logger_1.logger.info('Starting analytics collection service...');
        const analyticsInterval = setInterval(async () => {
            try {
                await this.collectUsageAnalytics();
            }
            catch (error) {
                logger_1.logger.error('Analytics collection error:', error);
            }
        }, 5 * 60 * 1000);
        this.analyticsInterval = analyticsInterval;
        const cleanupInterval = setInterval(async () => {
            try {
                await this.cleanupOldAnalytics();
            }
            catch (error) {
                logger_1.logger.error('Analytics cleanup error:', error);
            }
        }, 24 * 60 * 60 * 1000);
        this.cleanupInterval = cleanupInterval;
        logger_1.logger.info('Analytics collection service started');
    }
    stopAnalyticsCollection() {
        if (this.analyticsInterval) {
            clearInterval(this.analyticsInterval);
            delete this.analyticsInterval;
        }
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
            delete this.cleanupInterval;
        }
        logger_1.logger.info('Analytics collection service stopped');
    }
    async cleanupOldAnalytics() {
        try {
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - 90);
            const result = await shared_hosting_1.UserUsageAnalytics.deleteMany({
                date: { $lt: cutoffDate }
            });
            logger_1.logger.info(`Cleaned up ${result.deletedCount} old analytics records`);
        }
        catch (error) {
            logger_1.logger.error('Failed to cleanup old analytics:', error);
        }
    }
    shutdown() {
        logger_1.logger.info('Shutting down shared hosting service...');
        this.stopAnalyticsCollection();
        this.stopPaymentLifecycleMonitoring();
        if (this.metricsInterval) {
            clearInterval(this.metricsInterval);
            delete this.metricsInterval;
        }
        logger_1.logger.info('Shared hosting service shutdown complete');
    }
    async testSSHConnection() {
        const ssh = new SSHConnection();
        const testResults = {
            connection_test: 'failed',
            configuration: {},
            commands_test: [],
            timestamp: new Date().toISOString()
        };
        try {
            logger_1.logger.info('Starting SSH connection test...');
            testResults.configuration = {
                host: ssh['config'].host,
                port: ssh['config'].port,
                username: ssh['config'].username,
                has_password: !!ssh['config'].password,
                has_private_key: !!ssh['config'].privateKey
            };
            logger_1.logger.info('SSH Configuration:', testResults.configuration);
            logger_1.logger.info('Testing SSH connection...');
            await ssh.connect();
            testResults.connection_test = 'success';
            logger_1.logger.info('SSH connection successful!');
            const testCommands = [
                'whoami',
                'pwd',
                'ls -la /',
                'df -h',
                'free -m',
                'uname -a'
            ];
            for (const command of testCommands) {
                try {
                    logger_1.logger.info(`Testing command: ${command}`);
                    const result = await ssh.executeCommand(command);
                    testResults.commands_test.push({
                        command,
                        status: 'success',
                        stdout: result.trim(),
                        stderr: ''
                    });
                    logger_1.logger.info(`Command '${command}' executed successfully`);
                }
                catch (cmdError) {
                    const errorMessage = cmdError instanceof Error ? cmdError.message : 'Unknown error';
                    testResults.commands_test.push({
                        command,
                        status: 'failed',
                        error: errorMessage
                    });
                    logger_1.logger.error(`Command '${command}' failed:`, errorMessage);
                }
            }
            testResults.overall_status = 'success';
            logger_1.logger.info('SSH connection test completed successfully');
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            const errorStack = error instanceof Error ? error.stack : undefined;
            testResults.connection_error = errorMessage;
            testResults.overall_status = 'failed';
            logger_1.logger.error('SSH connection test failed:', {
                error: errorMessage,
                stack: errorStack,
                config: testResults.configuration
            });
        }
        finally {
            try {
                await ssh.disconnect();
                logger_1.logger.info('SSH connection closed');
            }
            catch (disconnectError) {
                logger_1.logger.warn('Error closing SSH connection:', disconnectError);
            }
        }
        return testResults;
    }
    async createLinuxUser(username, homeDirectory, server) {
        const ssh = new SSHConnection();
        try {
            logger_1.logger.info(`Starting Linux user creation for: ${username}`, {
                home_directory: homeDirectory,
                server_ip: server?.ip_address || 'default'
            });
            if (server) {
                logger_1.logger.info(`Using specific server for user creation: ${server.ip_address}`);
                ssh.config.host = server.ip_address;
            }
            logger_1.logger.info(`Connecting to SSH for user creation: ${username}`);
            await ssh.connect();
            logger_1.logger.info(`SSH connected successfully for user creation: ${username}`);
            logger_1.logger.info(`Checking if Linux user already exists: ${username}`);
            try {
                const userCheck = await ssh.executeCommand(`id ${username}`);
                if (userCheck.trim()) {
                    logger_1.logger.warn(`Linux user already exists: ${username}`, { output: userCheck });
                    return;
                }
            }
            catch (checkError) {
                logger_1.logger.info(`User does not exist, proceeding with creation: ${username}`);
            }
            logger_1.logger.info(`Creating Linux user: ${username} with home: ${homeDirectory}`);
            const createUserResult = await ssh.executeCommand(`adduser --disabled-password --gecos "" --home ${homeDirectory} ${username}`);
            logger_1.logger.info(`User creation command completed: ${username}`, {
                stdout: createUserResult,
                stderr: ''
            });
            logger_1.logger.info(`Setting directory permissions for: ${username}`);
            const chmodResult = await ssh.executeCommand(`chmod 700 ${homeDirectory}`);
            logger_1.logger.debug(`chmod result for ${username}:`, { stdout: chmodResult, stderr: '' });
            const chownResult = await ssh.executeCommand(`chown ${username}:${username} ${homeDirectory}`);
            logger_1.logger.debug(`chown result for ${username}:`, { stdout: chownResult, stderr: '' });
            logger_1.logger.info(`Disabling cron access for: ${username}`);
            const cronResult = await ssh.executeCommand(`echo "${username}" >> /etc/cron.deny`);
            logger_1.logger.debug(`cron deny result for ${username}:`, { stdout: cronResult, stderr: '' });
            logger_1.logger.info(`Successfully created Linux user: ${username}`, {
                home_directory: homeDirectory,
                permissions_set: true,
                cron_disabled: true
            });
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            const errorStack = error instanceof Error ? error.stack : undefined;
            logger_1.logger.error(`Failed to create Linux user ${username}:`, {
                error: errorMessage,
                stack: errorStack,
                home_directory: homeDirectory,
                server_ip: server?.ip_address || 'default'
            });
            throw error;
        }
        finally {
            logger_1.logger.info(`Disconnecting SSH for user creation: ${username}`);
            await ssh.disconnect();
        }
    }
    async setupResourceLimits(username, plan) {
        logger_1.logger.info(`Skipping resource limits setup for shared hosting user: ${username}, plan: ${plan} - Full server access enabled`);
    }
    async createUserDirectoryStructure(homeDirectory, username, server) {
        const ssh = new SSHConnection();
        try {
            if (server) {
                ssh.config.host = server.ip_address;
            }
            await ssh.connect();
            const directories = [
                'public_html',
                'logs',
                'tmp',
                'backups',
                'ssl',
                'apps',
                'apps/static',
                'apps/web-service',
                'apps/nodejs',
                'apps/php'
            ];
            for (const dir of directories) {
                const fullPath = `${homeDirectory}/${dir}`;
                await ssh.executeCommand(`mkdir -p ${fullPath}`);
                await ssh.executeCommand(`chown ${username}:${username} ${fullPath}`);
                await ssh.executeCommand(`chmod 755 ${fullPath}`);
            }
            const defaultHtml = `<!DOCTYPE html>
<html>
<head>
    <title>Welcome to ${username}'s Website</title>
    <style>
        body { font-family: Arial, sans-serif; text-align: center; margin-top: 50px; }
        .container { max-width: 600px; margin: 0 auto; }
        .logo { color: #007bff; font-size: 2em; margin-bottom: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🚀 Achidas Hosting</div>
        <h1>Welcome to your new website!</h1>
        <p>Your shared hosting account is ready. Upload your files to the <code>public_html</code> directory.</p>
        <p>Server: ${config_1.appConfig.ssh.host}</p>
        <p>Username: ${username}</p>
    </div>
</body>
</html>`;
            const escapedHtml = defaultHtml.replace(/'/g, "'\"'\"'");
            await ssh.executeCommand(`echo '${escapedHtml}' > ${homeDirectory}/public_html/index.html`);
            await ssh.executeCommand(`chown ${username}:${username} ${homeDirectory}/public_html/index.html`);
            logger_1.logger.info(`Created directory structure for user: ${username}`);
        }
        catch (error) {
            logger_1.logger.error(`Failed to create directory structure for ${username}:`, error);
            throw error;
        }
        finally {
            await ssh.disconnect();
        }
    }
    async setupBandwidthThrottling(username, port) {
        logger_1.logger.info(`Skipping bandwidth throttling for shared hosting user: ${username} on port: ${port} - Full bandwidth access enabled`);
    }
    getPlanLimits(_plan) {
        const sharedPlanLimits = {
            cpu_quota: 100,
            memory_max: 0,
            bandwidth_limit: 0,
            storage_limit: 0
        };
        return sharedPlanLimits;
    }
    async getNextAvailablePort() {
        return this.BASE_PORT + Math.floor(Math.random() * 1000);
    }
    async getNextAvailableSSHPort() {
        return this.SSH_BASE_PORT + Math.floor(Math.random() * 100);
    }
    async getNextAvailableFTPPort() {
        return this.FTP_BASE_PORT + Math.floor(Math.random() * 100);
    }
    generateSubdomain(applicationName) {
        return applicationName
            .toLowerCase()
            .replace(/[^a-z0-9-]/g, '-')
            .replace(/-+/g, '-')
            .replace(/^-|-$/g, '');
    }
    async generateUniqueSubdomain(applicationName, userId) {
        const baseSubdomain = this.generateSubdomain(applicationName);
        if (await this.isSubdomainUnique(baseSubdomain)) {
            return baseSubdomain;
        }
        const userIdentifier = userId.substring(0, 6).toLowerCase();
        const subdomainWithUser = `${baseSubdomain}-${userIdentifier}`;
        if (await this.isSubdomainUnique(subdomainWithUser)) {
            return subdomainWithUser;
        }
        const timestamp = Date.now().toString().slice(-6);
        const subdomainWithTimestamp = `${baseSubdomain}-${userIdentifier}-${timestamp}`;
        if (await this.isSubdomainUnique(subdomainWithTimestamp)) {
            return subdomainWithTimestamp;
        }
        const randomString = Math.random().toString(36).substring(2, 8);
        return `${baseSubdomain}-${userIdentifier}-${randomString}`;
    }
    async isSubdomainUnique(subdomain) {
        try {
            const existingApp = await shared_hosting_1.SharedHostingApplication.findOne({ subdomain: `${subdomain}.${config_1.appConfig.domain.primary}` });
            const mainAppService = (0, application_1.getApplicationService)();
            const isUniqueInMain = await mainAppService['isSubdomainUnique'](subdomain);
            return !existingApp && isUniqueInMain;
        }
        catch (error) {
            logger_1.logger.error('Error checking subdomain uniqueness:', error);
            return false;
        }
    }
    async createApplication(userId, appData) {
        try {
            const subdomain = await this.generateUniqueSubdomain(appData.name, userId);
            const appId = `app_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
            const fullDomain = `${subdomain}.${config_1.appConfig.domain.primary}`;
            const directory = `/var/www/user_${userId}/apps/${appData.type}/${appData.name}`;
            await execAsync(`sudo mkdir -p ${directory}`);
            await execAsync(`sudo chown user_${userId}:user_${userId} ${directory}`);
            await this.setupApplicationEnvironment(directory, appData.type, appData);
            const application = {
                id: appId,
                name: appData.name,
                type: appData.type,
                ...(appData.framework && { framework: appData.framework }),
                ...(appData.domain && { domain: appData.domain }),
                subdomain: fullDomain,
                directory,
                status: 'stopped',
                ssl_enabled: false,
                created_at: new Date().toISOString()
            };
            try {
                const cloudflareDNS = new cloudflare_dns_1.CloudflareDNSService();
                await cloudflareDNS.createDNSRecord({
                    type: 'A',
                    name: fullDomain,
                    content: '*************',
                    ttl: 300,
                    proxied: false
                });
                logger_1.logger.info(`Created DNS record for ${fullDomain} -> *************`);
            }
            catch (dnsError) {
                logger_1.logger.error(`Failed to create DNS record for ${fullDomain}:`, dnsError);
            }
            logger_1.logger.info(`Created application: ${appData.name} for user: ${userId} with subdomain: ${fullDomain}`);
            return application;
        }
        catch (error) {
            logger_1.logger.error('Failed to create application:', error);
            throw new Error('Failed to create application');
        }
    }
    async setupApplicationEnvironment(directory, type, appData) {
        try {
            if (type === 'static-website') {
                await this.setupStaticWebsite(directory, appData.framework);
            }
            else if (type === 'web-service') {
                await this.setupWebService(directory, appData);
            }
        }
        catch (error) {
            logger_1.logger.error(`Failed to setup ${type} environment:`, error);
            throw error;
        }
    }
    async setupStaticWebsite(directory, framework) {
        const frameworkInfo = this.getFrameworkInfo(framework);
        const indexHtml = `
<!DOCTYPE html>
<html>
<head>
    <title>${frameworkInfo.title}</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f8f9fa; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .logo { color: #007bff; font-size: 2.5em; margin-bottom: 10px; }
        .framework-badge { background: ${frameworkInfo.color}; color: white; padding: 5px 15px; border-radius: 20px; font-size: 0.9em; }
        .instructions { background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">${frameworkInfo.icon}</div>
            <h1>${frameworkInfo.title}</h1>
            <span class="framework-badge">${framework || 'HTML'}</span>
            <p>${frameworkInfo.description}</p>
        </div>
        <div class="instructions">
            <h2>Getting Started</h2>
            ${frameworkInfo.instructions}
        </div>
        <div class="content">
            <h3>File Structure</h3>
            <p>Your files should be organized as follows:</p>
            <ul>
                ${frameworkInfo.structure.map((item) => `<li><strong>${item.name}</strong> - ${item.description}</li>`).join('')}
            </ul>
        </div>
    </div>
</body>
</html>`;
        await fs.writeFile(path.join(directory, 'index.html'), indexHtml);
        const directories = ['css', 'js', 'images', 'assets'];
        if (framework === 'react' || framework === 'vue' || framework === 'angular') {
            directories.push('dist', 'build', 'public');
        }
        await execAsync(`sudo mkdir -p ${directory}/{${directories.join(',')}}`);
        if (framework === 'react') {
            await this.createReactFiles(directory);
        }
        else if (framework === 'vue') {
            await this.createVueFiles(directory);
        }
        else if (framework === 'angular') {
            await this.createAngularFiles(directory);
        }
    }
    getFrameworkInfo(framework) {
        const frameworks = {
            html: {
                title: 'Static HTML Website',
                icon: '🌐',
                color: '#e34c26',
                description: 'Upload your HTML, CSS, and JavaScript files here.',
                instructions: `
          <ol>
            <li>Replace this index.html with your own</li>
            <li>Upload your CSS files to the 'css' folder</li>
            <li>Upload your JavaScript files to the 'js' folder</li>
            <li>Upload images to the 'images' folder</li>
          </ol>`,
                structure: [
                    { name: 'index.html', description: 'Main HTML file' },
                    { name: 'css/', description: 'Stylesheets' },
                    { name: 'js/', description: 'JavaScript files' },
                    { name: 'images/', description: 'Images and media' }
                ]
            },
            react: {
                title: 'React Application',
                icon: '⚛️',
                color: '#61dafb',
                description: 'Upload your React build files (npm run build output).',
                instructions: `
          <ol>
            <li>Build your React app locally: <code>npm run build</code></li>
            <li>Upload the contents of the 'build' or 'dist' folder</li>
            <li>Ensure index.html is in the root directory</li>
            <li>Static assets should be in the 'static' folder</li>
          </ol>`,
                structure: [
                    { name: 'index.html', description: 'Main HTML file' },
                    { name: 'static/', description: 'Built CSS, JS, and media files' },
                    { name: 'build/', description: 'React build output' },
                    { name: 'public/', description: 'Public assets' }
                ]
            },
            vue: {
                title: 'Vue.js Application',
                icon: '🖖',
                color: '#4fc08d',
                description: 'Upload your Vue.js build files (npm run build output).',
                instructions: `
          <ol>
            <li>Build your Vue app locally: <code>npm run build</code></li>
            <li>Upload the contents of the 'dist' folder</li>
            <li>Ensure index.html is in the root directory</li>
            <li>Assets should be in the appropriate folders</li>
          </ol>`,
                structure: [
                    { name: 'index.html', description: 'Main HTML file' },
                    { name: 'dist/', description: 'Vue build output' },
                    { name: 'assets/', description: 'Built assets' },
                    { name: 'css/', description: 'Stylesheets' }
                ]
            },
            angular: {
                title: 'Angular Application',
                icon: '🅰️',
                color: '#dd0031',
                description: 'Upload your Angular build files (ng build output).',
                instructions: `
          <ol>
            <li>Build your Angular app: <code>ng build --prod</code></li>
            <li>Upload the contents of the 'dist' folder</li>
            <li>Ensure index.html is in the root directory</li>
            <li>Configure routing for SPA if needed</li>
          </ol>`,
                structure: [
                    { name: 'index.html', description: 'Main HTML file' },
                    { name: 'dist/', description: 'Angular build output' },
                    { name: 'assets/', description: 'Static assets' },
                    { name: 'styles/', description: 'Global styles' }
                ]
            }
        };
        return frameworks[framework || 'html'] || frameworks['html'];
    }
    async createReactFiles(directory) {
        const packageJson = {
            name: "react-static-site",
            version: "1.0.0",
            description: "React static site on Achidas hosting",
            scripts: {
                build: "react-scripts build",
                start: "serve -s build"
            }
        };
        await fs.writeFile(path.join(directory, 'package.json'), JSON.stringify(packageJson, null, 2));
    }
    async createVueFiles(directory) {
        const packageJson = {
            name: "vue-static-site",
            version: "1.0.0",
            description: "Vue.js static site on Achidas hosting",
            scripts: {
                build: "vue-cli-service build",
                start: "serve -s dist"
            }
        };
        await fs.writeFile(path.join(directory, 'package.json'), JSON.stringify(packageJson, null, 2));
    }
    async createAngularFiles(directory) {
        const packageJson = {
            name: "angular-static-site",
            version: "1.0.0",
            description: "Angular static site on Achidas hosting",
            scripts: {
                build: "ng build --prod",
                start: "serve -s dist"
            }
        };
        await fs.writeFile(path.join(directory, 'package.json'), JSON.stringify(packageJson, null, 2));
    }
    async setupWebService(directory, appData) {
        const framework = appData.framework || 'nodejs';
        await this.installFrameworkRuntime(framework);
        switch (framework) {
            case 'nodejs':
                await this.setupNodeJSService(directory, appData);
                break;
            case 'python':
                await this.setupPythonService(directory, appData);
                break;
            case 'rust':
                await this.setupRustService(directory, appData);
                break;
            case 'php':
                await this.setupPHPService(directory, appData);
                break;
            case 'go':
                await this.setupGoService(directory, appData);
                break;
            case 'java':
                await this.setupJavaService(directory, appData);
                break;
            case 'dotnet':
                await this.setupDotNetService(directory, appData);
                break;
            case 'ruby':
                await this.setupRubyService(directory, appData);
                break;
            default:
                await this.setupNodeJSService(directory, appData);
        }
    }
    async installFrameworkRuntime(framework) {
        try {
            logger_1.logger.info(`Installing runtime for framework: ${framework}`);
            switch (framework) {
                case 'nodejs':
                    await this.installNodeJS();
                    break;
                case 'python':
                    await this.installPython();
                    break;
                case 'rust':
                    await this.installRust();
                    break;
                case 'php':
                    await this.installPHP();
                    break;
                case 'go':
                    await this.installGo();
                    break;
                case 'java':
                    await this.installJava();
                    break;
                case 'dotnet':
                    await this.installDotNet();
                    break;
                case 'ruby':
                    await this.installRuby();
                    break;
                default:
                    logger_1.logger.warn(`Unknown framework: ${framework}, skipping runtime installation`);
            }
        }
        catch (error) {
            logger_1.logger.error(`Failed to install runtime for ${framework}:`, error);
            throw error;
        }
    }
    async installNodeJS() {
        try {
            const nodeCheck = await execAsync('node --version').catch(() => null);
            if (nodeCheck) {
                logger_1.logger.info('Node.js already installed:', nodeCheck.stdout.trim());
                return;
            }
            logger_1.logger.info('Installing Node.js...');
            await execAsync('curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -');
            await execAsync('sudo apt-get install -y nodejs');
            await execAsync('sudo npm install -g pm2 serve nodemon');
            logger_1.logger.info('Node.js installation completed');
        }
        catch (error) {
            logger_1.logger.error('Failed to install Node.js:', error);
            throw error;
        }
    }
    async installPython() {
        try {
            const pythonCheck = await execAsync('python3 --version').catch(() => null);
            if (pythonCheck) {
                logger_1.logger.info('Python already installed:', pythonCheck.stdout.trim());
                return;
            }
            logger_1.logger.info('Installing Python...');
            await execAsync('sudo apt-get update');
            await execAsync('sudo apt-get install -y python3 python3-pip python3-venv python3-dev');
            await execAsync('sudo pip3 install fastapi uvicorn gunicorn flask django');
            logger_1.logger.info('Python installation completed');
        }
        catch (error) {
            logger_1.logger.error('Failed to install Python:', error);
            throw error;
        }
    }
    async installRust() {
        try {
            const rustCheck = await execAsync('rustc --version').catch(() => null);
            if (rustCheck) {
                logger_1.logger.info('Rust already installed:', rustCheck.stdout.trim());
                return;
            }
            logger_1.logger.info('Installing Rust...');
            await execAsync('curl --proto "=https" --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y');
            await execAsync('source ~/.cargo/env');
            await execAsync('~/.cargo/bin/cargo install cargo-watch');
            logger_1.logger.info('Rust installation completed');
        }
        catch (error) {
            logger_1.logger.error('Failed to install Rust:', error);
            throw error;
        }
    }
    async installPHP() {
        try {
            const phpCheck = await execAsync('php --version').catch(() => null);
            if (phpCheck) {
                logger_1.logger.info('PHP already installed:', phpCheck.stdout.trim());
                return;
            }
            logger_1.logger.info('Installing PHP...');
            await execAsync('sudo apt-get update');
            await execAsync('sudo apt-get install -y php php-cli php-fpm php-json php-common php-mysql php-zip php-gd php-mbstring php-curl php-xml php-pear php-bcmath');
            await execAsync('curl -sS https://getcomposer.org/installer | php');
            await execAsync('sudo mv composer.phar /usr/local/bin/composer');
            logger_1.logger.info('PHP installation completed');
        }
        catch (error) {
            logger_1.logger.error('Failed to install PHP:', error);
            throw error;
        }
    }
    async installGo() {
        try {
            const goCheck = await execAsync('go version').catch(() => null);
            if (goCheck) {
                logger_1.logger.info('Go already installed:', goCheck.stdout.trim());
                return;
            }
            logger_1.logger.info('Installing Go...');
            await execAsync('wget https://go.dev/dl/go1.21.5.linux-amd64.tar.gz');
            await execAsync('sudo rm -rf /usr/local/go && sudo tar -C /usr/local -xzf go1.21.5.linux-amd64.tar.gz');
            await execAsync('echo "export PATH=$PATH:/usr/local/go/bin" | sudo tee -a /etc/profile');
            await execAsync('rm go1.21.5.linux-amd64.tar.gz');
            logger_1.logger.info('Go installation completed');
        }
        catch (error) {
            logger_1.logger.error('Failed to install Go:', error);
            throw error;
        }
    }
    async installJava() {
        try {
            const javaCheck = await execAsync('java --version').catch(() => null);
            if (javaCheck) {
                logger_1.logger.info('Java already installed:', javaCheck.stdout.trim());
                return;
            }
            logger_1.logger.info('Installing Java...');
            await execAsync('sudo apt-get update');
            await execAsync('sudo apt-get install -y openjdk-17-jdk maven gradle');
            logger_1.logger.info('Java installation completed');
        }
        catch (error) {
            logger_1.logger.error('Failed to install Java:', error);
            throw error;
        }
    }
    async installDotNet() {
        try {
            const dotnetCheck = await execAsync('dotnet --version').catch(() => null);
            if (dotnetCheck) {
                logger_1.logger.info('.NET already installed:', dotnetCheck.stdout.trim());
                return;
            }
            logger_1.logger.info('Installing .NET...');
            await execAsync('wget https://packages.microsoft.com/config/ubuntu/22.04/packages-microsoft-prod.deb -O packages-microsoft-prod.deb');
            await execAsync('sudo dpkg -i packages-microsoft-prod.deb');
            await execAsync('rm packages-microsoft-prod.deb');
            await execAsync('sudo apt-get update');
            await execAsync('sudo apt-get install -y dotnet-sdk-8.0');
            logger_1.logger.info('.NET installation completed');
        }
        catch (error) {
            logger_1.logger.error('Failed to install .NET:', error);
            throw error;
        }
    }
    async installRuby() {
        try {
            const rubyCheck = await execAsync('ruby --version').catch(() => null);
            if (rubyCheck) {
                logger_1.logger.info('Ruby already installed:', rubyCheck.stdout.trim());
                return;
            }
            logger_1.logger.info('Installing Ruby...');
            await execAsync('sudo apt-get update');
            await execAsync('sudo apt-get install -y ruby-full build-essential zlib1g-dev');
            await execAsync('sudo gem install bundler rails');
            logger_1.logger.info('Ruby installation completed');
        }
        catch (error) {
            logger_1.logger.error('Failed to install Ruby:', error);
            throw error;
        }
    }
    async setupNodeJSService(directory, appData) {
        const packageJsonPath = path.join(directory, 'package.json');
        const hasUserPackageJson = require('fs').existsSync(packageJsonPath);
        if (!hasUserPackageJson) {
            const packageJson = {
                name: appData.name,
                version: "1.0.0",
                description: "Node.js web service on Achidas shared hosting",
                main: "index.js",
                scripts: {
                    start: appData.start_command || "node index.js",
                    build: appData.build_command || "npm install"
                }
            };
            await fs.writeFile(packageJsonPath, JSON.stringify(packageJson, null, 2));
            const indexPath = path.join(directory, 'index.js');
            const hasUserIndex = require('fs').existsSync(indexPath);
            if (!hasUserIndex) {
                const indexJs = `
const express = require('express');
const app = express();
const port = process.env.PORT || 3000;

app.use(express.json());

app.get('/', (req, res) => {
  res.json({ message: 'Hello from ${appData.name}!' });
});

app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: '${appData.name}'
  });
});

app.listen(port, () => {
  console.log(\`🚀 ${appData.name} running on port \${port}\`);
});
`;
                await fs.writeFile(indexPath, indexJs);
            }
        }
        try {
            await execAsync(`cd ${directory} && npm install`);
            logger_1.logger.info(`Installed Node.js dependencies from package.json for ${appData.name}`);
        }
        catch (error) {
            logger_1.logger.warn(`Failed to install Node.js dependencies for ${appData.name}:`, error);
        }
        const gitignorePath = path.join(directory, '.gitignore');
        const hasGitignore = require('fs').existsSync(gitignorePath);
        if (!hasGitignore) {
            await fs.writeFile(gitignorePath, 'node_modules/\n.env\n*.log\n');
        }
    }
    async setupPythonService(directory, appData) {
        const requirementsPath = path.join(directory, 'requirements.txt');
        const hasUserRequirements = require('fs').existsSync(requirementsPath);
        if (!hasUserRequirements) {
            const requirementsTxt = `fastapi==0.104.1
uvicorn==0.24.0
pydantic==2.5.0`;
            await fs.writeFile(requirementsPath, requirementsTxt);
            const mainPath = path.join(directory, 'main.py');
            const hasUserMain = require('fs').existsSync(mainPath);
            if (!hasUserMain) {
                const mainPy = `
from fastapi import FastAPI
import uvicorn
from datetime import datetime

app = FastAPI(title="${appData.name}", version="1.0.0")

@app.get("/")
async def root():
    return {"message": "Hello from ${appData.name}!"}

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "service": "${appData.name}"
    }

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
`;
                await fs.writeFile(mainPath, mainPy.trim());
            }
        }
        try {
            await execAsync(`cd ${directory} && python3 -m pip install -r requirements.txt`);
            logger_1.logger.info(`Installed Python dependencies from requirements.txt for ${appData.name}`);
        }
        catch (error) {
            logger_1.logger.warn(`Failed to install Python dependencies for ${appData.name}:`, error);
        }
        const startSh = `#!/bin/bash
${appData.start_command || 'uvicorn main:app --host 0.0.0.0 --port 8000'}
`;
        await fs.writeFile(path.join(directory, 'start.sh'), startSh.trim());
        await execAsync(`chmod +x ${path.join(directory, 'start.sh')}`);
        const gitignorePath = path.join(directory, '.gitignore');
        const hasGitignore = require('fs').existsSync(gitignorePath);
        if (!hasGitignore) {
            await fs.writeFile(gitignorePath, '__pycache__/\n*.pyc\n.env\n*.log\nvenv/\n');
        }
    }
    async setupRustService(directory, appData) {
        const cargoTomlPath = path.join(directory, 'Cargo.toml');
        const hasUserCargo = require('fs').existsSync(cargoTomlPath);
        if (!hasUserCargo) {
            const cargoToml = `[package]
name = "${appData.name.replace(/[^a-z0-9_]/g, '_')}"
version = "0.1.0"
edition = "2021"

[dependencies]
actix-web = "4.4"
serde = { version = "1.0", features = ["derive"] }
tokio = { version = "1.0", features = ["full"] }`;
            await fs.writeFile(cargoTomlPath, cargoToml);
            await execAsync(`mkdir -p ${directory}/src`);
            const mainRsPath = path.join(directory, 'src', 'main.rs');
            const hasUserMain = require('fs').existsSync(mainRsPath);
            if (!hasUserMain) {
                const mainRs = `
use actix_web::{web, App, HttpResponse, HttpServer, Result};
use serde::Serialize;

#[derive(Serialize)]
struct HealthResponse {
    status: String,
    service: String,
}

async fn health() -> Result<HttpResponse> {
    let response = HealthResponse {
        status: "healthy".to_string(),
        service: "${appData.name}".to_string(),
    };
    Ok(HttpResponse::Ok().json(response))
}

async fn hello() -> Result<HttpResponse> {
    Ok(HttpResponse::Ok().json(serde_json::json!({
        "message": format!("Hello from {}!", "${appData.name}")
    })))
}

#[actix_web::main]
async fn main() -> std::io::Result<()> {
    println!("🚀 ${appData.name} starting on port 8080");

    HttpServer::new(|| {
        App::new()
            .route("/", web::get().to(hello))
            .route("/health", web::get().to(health))
    })
    .bind("0.0.0.0:8080")?
    .run()
    .await
}
`;
                await fs.writeFile(mainRsPath, mainRs.trim());
            }
        }
        try {
            await execAsync(`cd ${directory} && cargo build --release`);
            logger_1.logger.info(`Built Rust project from Cargo.toml for ${appData.name}`);
        }
        catch (error) {
            logger_1.logger.warn(`Failed to build Rust project for ${appData.name}:`, error);
        }
        const gitignorePath = path.join(directory, '.gitignore');
        const hasGitignore = require('fs').existsSync(gitignorePath);
        if (!hasGitignore) {
            await fs.writeFile(gitignorePath, '/target\nCargo.lock\n.env\n*.log\n');
        }
    }
    async setupPHPService(directory, appData) {
        const composerJsonPath = path.join(directory, 'composer.json');
        const hasUserComposer = require('fs').existsSync(composerJsonPath);
        if (!hasUserComposer) {
            const composerJson = {
                name: `achidas/${appData.name}`,
                description: `${appData.name} PHP web service`,
                type: "project",
                require: {
                    "php": ">=8.0"
                },
                autoload: {
                    "psr-4": {
                        "App\\": "src/"
                    }
                }
            };
            await fs.writeFile(composerJsonPath, JSON.stringify(composerJson, null, 2));
            const indexPath = path.join(directory, 'index.php');
            const hasUserIndex = require('fs').existsSync(indexPath);
            if (!hasUserIndex) {
                const indexPhp = `
<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');

$request_uri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);

switch ($request_uri) {
    case '/':
        echo json_encode(['message' => 'Hello from ${appData.name}!']);
        break;
    case '/health':
        echo json_encode([
            'status' => 'healthy',
            'timestamp' => date('c'),
            'service' => '${appData.name}'
        ]);
        break;
    default:
        http_response_code(404);
        echo json_encode(['error' => 'Not found']);
}
?>`;
                await fs.writeFile(indexPath, indexPhp.trim());
            }
        }
        try {
            await execAsync(`cd ${directory} && composer install --no-dev`);
            logger_1.logger.info(`Installed PHP dependencies from composer.json for ${appData.name}`);
        }
        catch (error) {
            logger_1.logger.warn(`Failed to install PHP dependencies for ${appData.name}:`, error);
        }
        const gitignorePath = path.join(directory, '.gitignore');
        const hasGitignore = require('fs').existsSync(gitignorePath);
        if (!hasGitignore) {
            await fs.writeFile(gitignorePath, '/vendor\n.env\n*.log\n');
        }
    }
    async setupGoService(directory, appData) {
        const goModPath = path.join(directory, 'go.mod');
        const hasUserGoMod = require('fs').existsSync(goModPath);
        if (!hasUserGoMod) {
            const goMod = `module ${appData.name}

go 1.21

require (
    github.com/gin-gonic/gin v1.9.1
)`;
            await fs.writeFile(goModPath, goMod);
            const mainGoPath = path.join(directory, 'main.go');
            const hasUserMain = require('fs').existsSync(mainGoPath);
            if (!hasUserMain) {
                const mainGo = `
package main

import (
    "net/http"
    "github.com/gin-gonic/gin"
)

func main() {
    r := gin.Default()

    r.GET("/", func(c *gin.Context) {
        c.JSON(http.StatusOK, gin.H{
            "message": "Hello from ${appData.name}!",
        })
    })

    r.GET("/health", func(c *gin.Context) {
        c.JSON(http.StatusOK, gin.H{
            "status":  "healthy",
            "service": "${appData.name}",
        })
    })

    r.Run(":8080")
}
`;
                await fs.writeFile(mainGoPath, mainGo.trim());
            }
        }
        try {
            await execAsync(`cd ${directory} && go mod tidy`);
            await execAsync(`cd ${directory} && go build`);
            logger_1.logger.info(`Built Go project from go.mod for ${appData.name}`);
        }
        catch (error) {
            logger_1.logger.warn(`Failed to build Go project for ${appData.name}:`, error);
        }
        const gitignorePath = path.join(directory, '.gitignore');
        const hasGitignore = require('fs').existsSync(gitignorePath);
        if (!hasGitignore) {
            await fs.writeFile(gitignorePath, '*.exe\n*.log\n.env\n');
        }
    }
    async setupJavaService(directory, appData) {
        const pomXmlPath = path.join(directory, 'pom.xml');
        const hasUserPom = require('fs').existsSync(pomXmlPath);
        if (!hasUserPom) {
            const pomXml = `<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.achidas</groupId>
    <artifactId>${appData.name}</artifactId>
    <version>1.0.0</version>
    <packaging>jar</packaging>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <version>3.1.0</version>
        </dependency>
    </dependencies>
</project>`;
            await fs.writeFile(pomXmlPath, pomXml);
            await execAsync(`mkdir -p ${directory}/src/main/java/com/achidas`);
            const mainJavaPath = path.join(directory, 'src/main/java/com/achidas/Application.java');
            const hasUserMain = require('fs').existsSync(mainJavaPath);
            if (!hasUserMain) {
                const mainJava = `package com.achidas;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@SpringBootApplication
@RestController
public class Application {

    @GetMapping("/")
    public String hello() {
        return "Hello from ${appData.name}!";
    }

    @GetMapping("/health")
    public String health() {
        return "{\\"status\\": \\"healthy\\", \\"service\\": \\"${appData.name}\\"}";
    }

    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}`;
                await fs.writeFile(mainJavaPath, mainJava);
            }
        }
        try {
            await execAsync(`cd ${directory} && mvn clean compile`);
            logger_1.logger.info(`Built Java project from pom.xml for ${appData.name}`);
        }
        catch (error) {
            logger_1.logger.warn(`Failed to build Java project for ${appData.name}:`, error);
        }
        const gitignorePath = path.join(directory, '.gitignore');
        const hasGitignore = require('fs').existsSync(gitignorePath);
        if (!hasGitignore) {
            await fs.writeFile(gitignorePath, '/target\n*.class\n.env\n*.log\n');
        }
    }
    async setupDotNetService(directory, appData) {
        const csprojPath = path.join(directory, `${appData.name}.csproj`);
        const hasUserCsproj = require('fs').existsSync(csprojPath);
        if (!hasUserCsproj) {
            const csproj = `<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>
</Project>`;
            await fs.writeFile(csprojPath, csproj);
            const programPath = path.join(directory, 'Program.cs');
            const hasUserProgram = require('fs').existsSync(programPath);
            if (!hasUserProgram) {
                const programCs = `var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.MapGet("/", () => "Hello from ${appData.name}!");
app.MapGet("/health", () => new { status = "healthy", service = "${appData.name}" });

app.Run();`;
                await fs.writeFile(programPath, programCs);
            }
        }
        try {
            await execAsync(`cd ${directory} && dotnet build`);
            logger_1.logger.info(`Built .NET project from .csproj for ${appData.name}`);
        }
        catch (error) {
            logger_1.logger.warn(`Failed to build .NET project for ${appData.name}:`, error);
        }
        const gitignorePath = path.join(directory, '.gitignore');
        const hasGitignore = require('fs').existsSync(gitignorePath);
        if (!hasGitignore) {
            await fs.writeFile(gitignorePath, '/bin\n/obj\n.env\n*.log\n');
        }
    }
    async setupRubyService(directory, appData) {
        const gemfilePath = path.join(directory, 'Gemfile');
        const hasUserGemfile = require('fs').existsSync(gemfilePath);
        if (!hasUserGemfile) {
            const gemfile = `source 'https://rubygems.org'

gem 'sinatra'
gem 'puma'`;
            await fs.writeFile(gemfilePath, gemfile);
            const appRbPath = path.join(directory, 'app.rb');
            const hasUserApp = require('fs').existsSync(appRbPath);
            if (!hasUserApp) {
                const appRb = `require 'sinatra'
require 'json'

get '/' do
  content_type :json
  { message: 'Hello from ${appData.name}!' }.to_json
end

get '/health' do
  content_type :json
  { status: 'healthy', service: '${appData.name}' }.to_json
end`;
                await fs.writeFile(appRbPath, appRb);
            }
        }
        try {
            await execAsync(`cd ${directory} && bundle install`);
            logger_1.logger.info(`Installed Ruby dependencies from Gemfile for ${appData.name}`);
        }
        catch (error) {
            logger_1.logger.warn(`Failed to install Ruby dependencies for ${appData.name}:`, error);
        }
        const gitignorePath = path.join(directory, '.gitignore');
        const hasGitignore = require('fs').existsSync(gitignorePath);
        if (!hasGitignore) {
            await fs.writeFile(gitignorePath, '.bundle\nvendor\n.env\n*.log\n');
        }
    }
    async getUsers(filters) {
        try {
            const query = {};
            if (filters.status) {
                query.status = filters.status;
            }
            if (filters.plan) {
                query.plan = filters.plan;
            }
            if (filters.server_id) {
                query.server_id = filters.server_id;
            }
            if (filters.search) {
                query.$or = [
                    { username: { $regex: filters.search, $options: 'i' } },
                    { linux_username: { $regex: filters.search, $options: 'i' } },
                    { email: { $regex: filters.search, $options: 'i' } }
                ];
            }
            const skip = (filters.page - 1) * filters.limit;
            const [users, total] = await Promise.all([
                shared_hosting_1.SharedHostingUser.find(query)
                    .populate('applications')
                    .sort({ created_at: -1 })
                    .skip(skip)
                    .limit(filters.limit)
                    .lean(),
                shared_hosting_1.SharedHostingUser.countDocuments(query)
            ]);
            const totalPages = Math.ceil(total / filters.limit);
            logger_1.logger.info(`Retrieved ${users.length} users (page ${filters.page}/${totalPages})`);
            return {
                users: users.map((user) => {
                    const mapStatus = (dbStatus) => {
                        switch (dbStatus) {
                            case 'active': return 'active';
                            case 'suspended': return 'suspended';
                            case 'deleted':
                            case 'pending':
                            default: return 'disabled';
                        }
                    };
                    return {
                        id: user._id.toString(),
                        user_id: user.user_id,
                        username: user.username,
                        linux_username: user.linux_username,
                        home_directory: user.home_directory,
                        plan: user.plan,
                        status: mapStatus(user.status),
                        server_id: user.server_id,
                        server_ip: user.server_ip,
                        port: user.port,
                        ssh_port: user.ssh_port,
                        ftp_port: user.ftp_port,
                        resource_limits: user.resource_limits,
                        usage: user.usage,
                        created_at: user.created_at.toISOString(),
                        applications: (user.applications || []).map((app) => ({
                            id: app._id.toString(),
                            name: app.name,
                            type: app.type,
                            framework: app.framework,
                            domain: app.domain,
                            subdomain: app.subdomain,
                            directory: app.directory,
                            status: app.status,
                            port: app.port,
                            ssl_enabled: app.ssl_enabled,
                            created_at: app.created_at.toISOString(),
                            last_deployed: app.last_deployed?.toISOString()
                        }))
                    };
                }),
                total,
                page: filters.page,
                limit: filters.limit,
                totalPages
            };
        }
        catch (error) {
            logger_1.logger.error('Failed to get users:', error);
            throw error;
        }
    }
    async getUser(userId) {
        try {
            const user = await shared_hosting_1.SharedHostingUser.findOne({ user_id: userId })
                .populate('applications')
                .lean();
            if (!user) {
                return null;
            }
            const mapStatus = (dbStatus) => {
                switch (dbStatus) {
                    case 'active': return 'active';
                    case 'suspended': return 'suspended';
                    case 'deleted':
                    case 'pending':
                    default: return 'disabled';
                }
            };
            return {
                id: user._id.toString(),
                user_id: user.user_id,
                username: user.username,
                linux_username: user.linux_username,
                home_directory: user.home_directory,
                plan: user.plan,
                status: mapStatus(user.status),
                server_id: user.server_id,
                server_ip: user.server_ip,
                port: user.port,
                ssh_port: user.ssh_port,
                ftp_port: user.ftp_port,
                resource_limits: user.resource_limits,
                usage: user.usage,
                created_at: user.created_at.toISOString(),
                applications: (user.applications || []).map((app) => ({
                    id: app._id.toString(),
                    name: app.name,
                    type: app.type,
                    framework: app.framework,
                    domain: app.domain,
                    subdomain: app.subdomain,
                    directory: app.directory,
                    status: app.status,
                    port: app.port,
                    ssl_enabled: app.ssl_enabled,
                    created_at: app.created_at.toISOString(),
                    last_deployed: app.last_deployed?.toISOString()
                }))
            };
        }
        catch (error) {
            logger_1.logger.error(`Failed to get user ${userId}:`, error);
            throw error;
        }
    }
    async updateUser(userId, updateData) {
        try {
            const user = await this.getUser(userId);
            if (!user) {
                throw new Error('User not found');
            }
            if (updateData.plan) {
                user.plan = updateData.plan;
                const limits = this.getPlanLimits(updateData.plan);
                user.resource_limits = {
                    cpu_quota: limits.cpu_quota,
                    memory_max: limits.memory_max,
                    bandwidth_limit: limits.bandwidth_limit,
                    storage_limit: limits.storage_limit
                };
                await this.setupResourceLimits(user.linux_username, updateData.plan);
            }
            if (updateData.status) {
                user.status = updateData.status;
            }
            if (updateData.resource_limits) {
                user.resource_limits = { ...user.resource_limits, ...updateData.resource_limits };
            }
            logger_1.logger.info(`Updated user ${userId}`);
            return user;
        }
        catch (error) {
            logger_1.logger.error(`Failed to update user ${userId}:`, error);
            throw error;
        }
    }
    async deleteUser(userId) {
        const ssh = new SSHConnection();
        try {
            const user = await this.getUser(userId);
            if (!user) {
                throw new Error('User not found');
            }
            await ssh.connect();
            await ssh.executeCommand(`pkill -u ${user.linux_username} || true`);
            await ssh.executeCommand(`userdel -r ${user.linux_username} || true`);
            await ssh.executeCommand(`rm -rf /etc/systemd/system/${user.linux_username}.slice.d || true`);
            await ssh.executeCommand('systemctl daemon-reexec');
            logger_1.logger.info(`Deleted user ${userId} and cleaned up resources`);
        }
        catch (error) {
            logger_1.logger.error(`Failed to delete user ${userId}:`, error);
            throw error;
        }
        finally {
            await ssh.disconnect();
        }
    }
    async getAnalytics(period, serverId) {
        try {
            const now = new Date();
            let startDate;
            switch (period) {
                case 'day':
                    startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
                    break;
                case 'week':
                    startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                    break;
                case 'month':
                    startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
                    break;
                case 'year':
                    startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
                    break;
            }
            const userQuery = serverId ? { server_id: serverId } : {};
            const [totalUsers, activeUsers, suspendedUsers] = await Promise.all([
                shared_hosting_1.SharedHostingUser.countDocuments(userQuery),
                shared_hosting_1.SharedHostingUser.countDocuments({ ...userQuery, status: 'active' }),
                shared_hosting_1.SharedHostingUser.countDocuments({ ...userQuery, status: 'suspended' })
            ]);
            const userIds = serverId ? await shared_hosting_1.SharedHostingUser.find(userQuery).distinct('user_id') : [];
            const appQuery = serverId ? { user_id: { $in: userIds } } : {};
            const [totalApplications, runningApplications, stoppedApplications] = await Promise.all([
                shared_hosting_1.SharedHostingApplication.countDocuments(appQuery),
                shared_hosting_1.SharedHostingApplication.countDocuments({ ...appQuery, status: 'running' }),
                shared_hosting_1.SharedHostingApplication.countDocuments({ ...appQuery, status: 'stopped' })
            ]);
            const usageQuery = serverId ? { user_id: { $in: userIds }, date: { $gte: startDate } } : { date: { $gte: startDate } };
            const usageAnalytics = await shared_hosting_1.UserUsageAnalytics.find(usageQuery);
            const totalBandwidth = usageAnalytics.reduce((sum, u) => sum + u.bandwidth_used, 0);
            const totalStorage = usageAnalytics.reduce((sum, u) => sum + u.storage_used, 0);
            const avgCpu = usageAnalytics.length > 0 ? usageAnalytics.reduce((sum, u) => sum + u.cpu_usage_avg, 0) / usageAnalytics.length : 0;
            const avgMemory = usageAnalytics.length > 0 ? usageAnalytics.reduce((sum, u) => sum + u.memory_usage_avg, 0) / usageAnalytics.length : 0;
            const planDistribution = await shared_hosting_1.SharedHostingUser.aggregate([
                { $match: userQuery },
                { $group: { _id: '$plan', count: { $sum: 1 } } }
            ]);
            const analytics = {
                period,
                server_id: serverId || 'all',
                total_users: totalUsers,
                active_users: activeUsers,
                suspended_users: suspendedUsers,
                inactive_users: totalUsers - activeUsers - suspendedUsers,
                total_applications: totalApplications,
                running_applications: runningApplications,
                stopped_applications: stoppedApplications,
                resource_usage: {
                    cpu_average: avgCpu,
                    memory_average: avgMemory,
                    bandwidth_total: totalBandwidth,
                    storage_total: totalStorage
                },
                plan_distribution: planDistribution.reduce((acc, p) => {
                    acc[p._id] = p.count;
                    return acc;
                }, {}),
                growth: {
                    new_users_this_period: await shared_hosting_1.SharedHostingUser.countDocuments({
                        ...userQuery,
                        created_at: { $gte: startDate }
                    }),
                    churn_rate: 0,
                    upgrade_rate: 0
                }
            };
            logger_1.logger.info(`Retrieved analytics for period: ${period}`);
            return analytics;
        }
        catch (error) {
            logger_1.logger.error('Failed to get analytics:', error);
            throw error;
        }
    }
    async getUserResourceUsage(userId, period) {
        try {
            const user = await this.getUser(userId);
            if (!user) {
                throw new Error('User not found');
            }
            const startDate = this.getStartDateForPeriod(period);
            const endDate = new Date();
            const usageAnalytics = await shared_hosting_1.UserUsageAnalytics.find({
                user_id: userId,
                date: { $gte: startDate, $lte: endDate }
            }).sort({ date: 1 });
            const usage = {
                user_id: userId,
                period,
                cpu_usage: usageAnalytics.map(u => ({
                    timestamp: u.date.toISOString(),
                    value: u.cpu_usage_avg
                })),
                memory_usage: usageAnalytics.map(u => ({
                    timestamp: u.date.toISOString(),
                    value: u.memory_usage_avg
                })),
                bandwidth_usage: usageAnalytics.map(u => ({
                    timestamp: u.date.toISOString(),
                    value: u.bandwidth_used
                })),
                storage_usage: user.usage.storage_used,
                limits: user.resource_limits,
                alerts: []
            };
            logger_1.logger.info(`Retrieved resource usage for user: ${userId}, period: ${period}`);
            return usage;
        }
        catch (error) {
            logger_1.logger.error(`Failed to get user resource usage for ${userId}:`, error);
            throw error;
        }
    }
    async getServerCapacity(serverId) {
        try {
            const targetServerId = serverId || config_1.appConfig.ssh.host;
            const latestMetrics = await shared_hosting_1.ServerMetrics.findOne({
                server_id: targetServerId
            }).sort({ timestamp: -1 });
            const userCount = await shared_hosting_1.SharedHostingUser.countDocuments({
                server_id: targetServerId,
                status: { $in: ['active', 'suspended'] }
            });
            const serverConfig = this.serverPool.find(s => s.id === targetServerId || s.ip_address === targetServerId);
            const maxUsers = serverConfig?.max_users || this.MAX_USERS_PER_SERVER;
            const cpuUsage = latestMetrics?.cpu_usage || 0;
            const memoryUsage = latestMetrics?.memory_usage || 0;
            const diskUsage = latestMetrics?.disk_usage || 0;
            const today = new Date();
            const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
            const dailyBandwidth = await shared_hosting_1.UserUsageAnalytics.aggregate([
                {
                    $match: {
                        date: { $gte: startOfDay },
                        user_id: { $in: await shared_hosting_1.SharedHostingUser.find({ server_id: targetServerId }).distinct('user_id') }
                    }
                },
                {
                    $group: {
                        _id: null,
                        totalBandwidth: { $sum: '$bandwidth_used' }
                    }
                }
            ]);
            const bandwidthUsed = dailyBandwidth[0]?.totalBandwidth || 0;
            const bandwidthLimit = 1000;
            const bandwidthPercent = (bandwidthUsed / bandwidthLimit) * 100;
            const capacity = {
                server_id: targetServerId,
                total_capacity: {
                    cpu_cores: 4,
                    memory_gb: 8,
                    storage_gb: 160,
                    bandwidth_gb: bandwidthLimit
                },
                used_capacity: {
                    cpu_percent: cpuUsage,
                    memory_percent: memoryUsage,
                    storage_percent: diskUsage,
                    bandwidth_percent: bandwidthPercent
                },
                available_capacity: {
                    cpu_percent: 100 - cpuUsage,
                    memory_percent: 100 - memoryUsage,
                    storage_percent: 100 - diskUsage,
                    bandwidth_percent: 100 - bandwidthPercent
                },
                user_count: userCount,
                max_users: maxUsers,
                status: cpuUsage > 90 || memoryUsage > 90 || diskUsage > 90 ? 'critical' :
                    cpuUsage > 70 || memoryUsage > 70 || diskUsage > 70 ? 'warning' : 'healthy',
                last_updated: latestMetrics?.timestamp?.toISOString() || new Date().toISOString()
            };
            logger_1.logger.info(`Retrieved server capacity${serverId ? ` for server: ${serverId}` : ' for all servers'}`);
            return capacity;
        }
        catch (error) {
            logger_1.logger.error('Failed to get server capacity:', error);
            throw error;
        }
    }
    async suspendUser(userId, reason) {
        const ssh = new SSHConnection();
        try {
            const user = await this.getUser(userId);
            if (!user) {
                throw new Error('User not found');
            }
            await ssh.connect();
            await ssh.executeCommand(`pkill -u ${user.linux_username} || true`);
            await ssh.executeCommand(`usermod -L ${user.linux_username}`);
            user.status = 'suspended';
            logger_1.logger.info(`Suspended user ${userId}, reason: ${reason || 'No reason provided'}`);
        }
        catch (error) {
            logger_1.logger.error(`Failed to suspend user ${userId}:`, error);
            throw error;
        }
        finally {
            await ssh.disconnect();
        }
    }
    async reactivateUser(userId) {
        const ssh = new SSHConnection();
        try {
            const user = await this.getUser(userId);
            if (!user) {
                throw new Error('User not found');
            }
            await ssh.connect();
            await ssh.executeCommand(`usermod -U ${user.linux_username}`);
            user.status = 'active';
            logger_1.logger.info(`Reactivated user ${userId}`);
        }
        catch (error) {
            logger_1.logger.error(`Failed to reactivate user ${userId}:`, error);
            throw error;
        }
        finally {
            await ssh.disconnect();
        }
    }
    async resetUserPassword(userId, newPassword) {
        const ssh = new SSHConnection();
        try {
            const user = await this.getUser(userId);
            if (!user) {
                throw new Error('User not found');
            }
            const password = newPassword || Math.random().toString(36).slice(-12);
            await ssh.connect();
            await ssh.executeCommand(`echo "${user.linux_username}:${password}" | chpasswd`);
            logger_1.logger.info(`Reset password for user ${userId}`);
            return { password };
        }
        catch (error) {
            logger_1.logger.error(`Failed to reset password for user ${userId}:`, error);
            throw error;
        }
        finally {
            await ssh.disconnect();
        }
    }
    async getUserApplications(userId, filters) {
        try {
            const user = await this.getUser(userId);
            if (!user) {
                throw new Error('User not found');
            }
            const query = { user_id: userId };
            if (filters.status) {
                query.status = filters.status;
            }
            if (filters.type) {
                query.type = filters.type;
            }
            const total = await shared_hosting_1.SharedHostingApplication.countDocuments(query);
            const totalPages = Math.ceil(total / filters.limit);
            const skip = (filters.page - 1) * filters.limit;
            const applications = await shared_hosting_1.SharedHostingApplication.find(query)
                .sort({ created_at: -1 })
                .skip(skip)
                .limit(filters.limit)
                .lean();
            const paginatedApps = applications.map(app => ({
                id: app._id.toString(),
                name: app.name,
                type: app.type,
                framework: app.framework,
                domain: app.domain,
                subdomain: app.subdomain,
                directory: app.directory,
                status: app.status,
                port: app.port,
                ssl_enabled: app.ssl_enabled,
                created_at: app.created_at.toISOString(),
                last_deployed: app.last_deployed?.toISOString()
            }));
            logger_1.logger.info(`Retrieved ${paginatedApps.length} applications for user: ${userId}`);
            return {
                applications: paginatedApps,
                total,
                page: filters.page,
                limit: filters.limit,
                totalPages
            };
        }
        catch (error) {
            logger_1.logger.error(`Failed to get applications for user ${userId}:`, error);
            throw error;
        }
    }
    async updateApplication(userId, appId, updateData) {
        try {
            const user = await this.getUser(userId);
            if (!user) {
                throw new Error('User not found');
            }
            const app = user.applications?.find(a => a.id === appId);
            if (!app) {
                throw new Error('Application not found');
            }
            Object.assign(app, updateData);
            logger_1.logger.info(`Updated application ${appId} for user: ${userId}`);
            return app;
        }
        catch (error) {
            logger_1.logger.error(`Failed to update application ${appId} for user ${userId}:`, error);
            throw error;
        }
    }
    async deleteApplication(userId, appId) {
        const ssh = new SSHConnection();
        try {
            const user = await this.getUser(userId);
            if (!user) {
                throw new Error('User not found');
            }
            const appIndex = user.applications?.findIndex(a => a.id === appId);
            if (appIndex === -1 || appIndex === undefined) {
                throw new Error('Application not found');
            }
            await ssh.connect();
            await ssh.executeCommand(`pkill -f "${appId}" || true`);
            await ssh.executeCommand(`rm -rf ${user.home_directory}/apps/${appId} || true`);
            user.applications?.splice(appIndex, 1);
            logger_1.logger.info(`Deleted application ${appId} for user: ${userId}`);
        }
        catch (error) {
            logger_1.logger.error(`Failed to delete application ${appId} for user ${userId}:`, error);
            throw error;
        }
        finally {
            await ssh.disconnect();
        }
    }
}
let sharedHostingService;
function getSharedHostingService() {
    if (!sharedHostingService) {
        sharedHostingService = new SharedHostingService();
    }
    return sharedHostingService;
}
//# sourceMappingURL=shared-hosting.js.map