"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getSharedHostingService = getSharedHostingService;
const logger_1 = require("../utils/logger");
const child_process_1 = require("child_process");
const util_1 = require("util");
const promises_1 = __importDefault(require("fs/promises"));
const path_1 = __importDefault(require("path"));
const ssh2_1 = require("ssh2");
const execAsync = (0, util_1.promisify)(child_process_1.exec);
class SSHConnection {
    client;
    config;
    constructor() {
        this.client = new ssh2_1.Client();
        this.config = {
            host: process.env['SHARED_SERVER_IP'] || '*************',
            port: parseInt(process.env['SHARED_SERVER_SSH_PORT'] || '22'),
            username: process.env['SHARED_SERVER_SSH_USER'] || 'root',
            ...(process.env['SHARED_SERVER_SSH_PASSWORD'] && { password: process.env['SHARED_SERVER_SSH_PASSWORD'] }),
        };
    }
    async connect() {
        return new Promise((resolve, reject) => {
            this.client.on('ready', () => {
                logger_1.logger.info(`SSH connected to ${this.config.host}`);
                resolve();
            });
            this.client.on('error', (err) => {
                logger_1.logger.error('SSH connection error:', err);
                reject(err);
            });
            this.client.connect(this.config);
        });
    }
    async executeCommand(command) {
        return new Promise((resolve, reject) => {
            this.client.exec(command, (err, stream) => {
                if (err) {
                    reject(err);
                    return;
                }
                let stdout = '';
                let stderr = '';
                stream.on('close', (code) => {
                    if (code !== 0) {
                        logger_1.logger.warn(`Command exited with code ${code}: ${command}`);
                    }
                    resolve({ stdout, stderr });
                });
                stream.on('data', (data) => {
                    stdout += data.toString();
                });
                stream.stderr.on('data', (data) => {
                    stderr += data.toString();
                });
            });
        });
    }
    async disconnect() {
        this.client.end();
        logger_1.logger.info('SSH connection closed');
    }
}
class SharedHostingService {
    SHARED_SERVER_IP = process.env['SHARED_SERVER_IP'] || '*************';
    BASE_PORT = 8000;
    SSH_BASE_PORT = 2200;
    FTP_BASE_PORT = 2100;
    async createUser(userData) {
        try {
            const linuxUsername = `user_${userData.username.toLowerCase().replace(/[^a-z0-9]/g, '')}`;
            const homeDirectory = `/var/www/${linuxUsername}`;
            const port = await this.getNextAvailablePort();
            const sshPort = await this.getNextAvailableSSHPort();
            const ftpPort = await this.getNextAvailableFTPPort();
            await this.createLinuxUser(linuxUsername, homeDirectory);
            await this.setupResourceLimits(linuxUsername, userData.plan);
            await this.createUserDirectoryStructure(homeDirectory, linuxUsername);
            await this.setupBandwidthThrottling(linuxUsername, port);
            const sharedUser = {
                id: `shared_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
                user_id: userData.user_id,
                username: userData.username,
                linux_username: linuxUsername,
                home_directory: homeDirectory,
                plan: userData.plan,
                status: 'active',
                server_id: 'vultr-jnb-shared-01',
                server_ip: this.SHARED_SERVER_IP,
                port,
                ssh_port: sshPort,
                ftp_port: ftpPort,
                resource_limits: this.getPlanLimits(userData.plan),
                usage: {
                    cpu_usage: 0,
                    memory_usage: 0,
                    bandwidth_used: 0,
                    storage_used: 0
                },
                created_at: new Date().toISOString(),
                applications: []
            };
            logger_1.logger.info(`Created shared hosting user: ${linuxUsername} at ${homeDirectory}`);
            return sharedUser;
        }
        catch (error) {
            logger_1.logger.error('Failed to create shared hosting user:', error);
            throw new Error('Failed to create shared hosting user');
        }
    }
    async createLinuxUser(username, homeDirectory) {
        const ssh = new SSHConnection();
        try {
            await ssh.connect();
            await ssh.executeCommand(`adduser --disabled-password --gecos "" --home ${homeDirectory} ${username}`);
            await ssh.executeCommand(`chmod 700 ${homeDirectory}`);
            await ssh.executeCommand(`chown ${username}:${username} ${homeDirectory}`);
            await ssh.executeCommand(`echo "${username}" >> /etc/cron.deny`);
            logger_1.logger.info(`Created Linux user: ${username} with home: ${homeDirectory}`);
        }
        catch (error) {
            logger_1.logger.error(`Failed to create Linux user ${username}:`, error);
            throw error;
        }
        finally {
            await ssh.disconnect();
        }
    }
    async setupResourceLimits(username, plan) {
        const ssh = new SSHConnection();
        try {
            await ssh.connect();
            const limits = this.getPlanLimits(plan);
            const sliceConfig = `[Slice]
CPUQuota=${limits.cpu_quota}%
MemoryMax=${limits.memory_max}M
TasksMax=50`;
            await ssh.executeCommand(`mkdir -p /etc/systemd/system/${username}.slice.d`);
            await ssh.executeCommand(`echo '${sliceConfig}' > /etc/systemd/system/${username}.slice.d/limits.conf`);
            await ssh.executeCommand('systemctl daemon-reexec');
            await ssh.executeCommand(`systemctl restart user-$(id -u ${username}).slice || true`);
            logger_1.logger.info(`Set up resource limits for user: ${username}, plan: ${plan}`);
        }
        catch (error) {
            logger_1.logger.error(`Failed to setup resource limits for ${username}:`, error);
            throw error;
        }
        finally {
            await ssh.disconnect();
        }
    }
    async createUserDirectoryStructure(homeDirectory, username) {
        const ssh = new SSHConnection();
        try {
            await ssh.connect();
            const directories = [
                'public_html',
                'logs',
                'tmp',
                'backups',
                'ssl',
                'apps',
                'apps/static',
                'apps/web-service',
                'apps/nodejs',
                'apps/php'
            ];
            for (const dir of directories) {
                const fullPath = `${homeDirectory}/${dir}`;
                await ssh.executeCommand(`mkdir -p ${fullPath}`);
                await ssh.executeCommand(`chown ${username}:${username} ${fullPath}`);
                await ssh.executeCommand(`chmod 755 ${fullPath}`);
            }
            const defaultHtml = `<!DOCTYPE html>
<html>
<head>
    <title>Welcome to ${username}'s Website</title>
    <style>
        body { font-family: Arial, sans-serif; text-align: center; margin-top: 50px; }
        .container { max-width: 600px; margin: 0 auto; }
        .logo { color: #007bff; font-size: 2em; margin-bottom: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🚀 Achidas Hosting</div>
        <h1>Welcome to your new website!</h1>
        <p>Your shared hosting account is ready. Upload your files to the <code>public_html</code> directory.</p>
        <p>Server: ${this.SHARED_SERVER_IP}</p>
        <p>Username: ${username}</p>
    </div>
</body>
</html>`;
            const escapedHtml = defaultHtml.replace(/'/g, "'\"'\"'");
            await ssh.executeCommand(`echo '${escapedHtml}' > ${homeDirectory}/public_html/index.html`);
            await ssh.executeCommand(`chown ${username}:${username} ${homeDirectory}/public_html/index.html`);
            logger_1.logger.info(`Created directory structure for user: ${username}`);
        }
        catch (error) {
            logger_1.logger.error(`Failed to create directory structure for ${username}:`, error);
            throw error;
        }
        finally {
            await ssh.disconnect();
        }
    }
    async setupBandwidthThrottling(username, port) {
        try {
            await execAsync(`sudo tc qdisc add dev eth0 root handle 1: htb default 30`);
            await execAsync(`sudo tc class add dev eth0 parent 1: classid 1:1 htb rate 100mbit`);
            await execAsync(`sudo tc class add dev eth0 parent 1:1 classid 1:${port} htb rate 5mbit ceil 10mbit`);
            await execAsync(`sudo tc filter add dev eth0 protocol ip parent 1:0 prio 1 u32 match ip dport ${port} 0xffff flowid 1:${port}`);
            logger_1.logger.info(`Set up bandwidth throttling for user: ${username} on port: ${port}`);
        }
        catch (error) {
            logger_1.logger.error(`Failed to setup bandwidth throttling for ${username}:`, error);
        }
    }
    getPlanLimits(plan) {
        const planLimits = {
            'free': {
                cpu_quota: 2,
                memory_max: 128,
                bandwidth_limit: 5,
                storage_limit: 1
            },
            'starter': {
                cpu_quota: 5,
                memory_max: 256,
                bandwidth_limit: 25,
                storage_limit: 5
            },
            'basic': {
                cpu_quota: 10,
                memory_max: 512,
                bandwidth_limit: 50,
                storage_limit: 10
            },
            'standard': {
                cpu_quota: 15,
                memory_max: 1024,
                bandwidth_limit: 100,
                storage_limit: 20
            },
            'pro': {
                cpu_quota: 25,
                memory_max: 2048,
                bandwidth_limit: 250,
                storage_limit: 50
            }
        };
        return planLimits[plan] ?? planLimits['starter'];
    }
    async getNextAvailablePort() {
        return this.BASE_PORT + Math.floor(Math.random() * 1000);
    }
    async getNextAvailableSSHPort() {
        return this.SSH_BASE_PORT + Math.floor(Math.random() * 100);
    }
    async getNextAvailableFTPPort() {
        return this.FTP_BASE_PORT + Math.floor(Math.random() * 100);
    }
    async createApplication(userId, appData) {
        try {
            const appId = `app_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
            const subdomain = `${appData.name.toLowerCase().replace(/[^a-z0-9]/g, '')}-${userId.substring(userId.length - 6)}`;
            const directory = `/var/www/user_${userId}/apps/${appData.type}/${appData.name}`;
            await execAsync(`sudo mkdir -p ${directory}`);
            await execAsync(`sudo chown user_${userId}:user_${userId} ${directory}`);
            await this.setupApplicationEnvironment(directory, appData.type, appData);
            const application = {
                id: appId,
                name: appData.name,
                type: appData.type,
                ...(appData.framework && { framework: appData.framework }),
                ...(appData.domain && { domain: appData.domain }),
                subdomain: `${subdomain}.achidas.com`,
                directory,
                status: 'stopped',
                ssl_enabled: false,
                created_at: new Date().toISOString()
            };
            logger_1.logger.info(`Created application: ${appData.name} for user: ${userId}`);
            return application;
        }
        catch (error) {
            logger_1.logger.error('Failed to create application:', error);
            throw new Error('Failed to create application');
        }
    }
    async setupApplicationEnvironment(directory, type, appData) {
        try {
            if (type === 'static-website') {
                await this.setupStaticWebsite(directory, appData.framework);
            }
            else if (type === 'web-service') {
                await this.setupWebService(directory, appData);
            }
        }
        catch (error) {
            logger_1.logger.error(`Failed to setup ${type} environment:`, error);
            throw error;
        }
    }
    async setupStaticWebsite(directory, framework) {
        const frameworkInfo = this.getFrameworkInfo(framework);
        const indexHtml = `
<!DOCTYPE html>
<html>
<head>
    <title>${frameworkInfo.title}</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f8f9fa; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .logo { color: #007bff; font-size: 2.5em; margin-bottom: 10px; }
        .framework-badge { background: ${frameworkInfo.color}; color: white; padding: 5px 15px; border-radius: 20px; font-size: 0.9em; }
        .instructions { background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">${frameworkInfo.icon}</div>
            <h1>${frameworkInfo.title}</h1>
            <span class="framework-badge">${framework || 'HTML'}</span>
            <p>${frameworkInfo.description}</p>
        </div>
        <div class="instructions">
            <h2>Getting Started</h2>
            ${frameworkInfo.instructions}
        </div>
        <div class="content">
            <h3>File Structure</h3>
            <p>Your files should be organized as follows:</p>
            <ul>
                ${frameworkInfo.structure.map((item) => `<li><strong>${item.name}</strong> - ${item.description}</li>`).join('')}
            </ul>
        </div>
    </div>
</body>
</html>`;
        await promises_1.default.writeFile(path_1.default.join(directory, 'index.html'), indexHtml);
        const directories = ['css', 'js', 'images', 'assets'];
        if (framework === 'react' || framework === 'vue' || framework === 'angular') {
            directories.push('dist', 'build', 'public');
        }
        await execAsync(`sudo mkdir -p ${directory}/{${directories.join(',')}}`);
        if (framework === 'react') {
            await this.createReactFiles(directory);
        }
        else if (framework === 'vue') {
            await this.createVueFiles(directory);
        }
        else if (framework === 'angular') {
            await this.createAngularFiles(directory);
        }
    }
    getFrameworkInfo(framework) {
        const frameworks = {
            html: {
                title: 'Static HTML Website',
                icon: '🌐',
                color: '#e34c26',
                description: 'Upload your HTML, CSS, and JavaScript files here.',
                instructions: `
          <ol>
            <li>Replace this index.html with your own</li>
            <li>Upload your CSS files to the 'css' folder</li>
            <li>Upload your JavaScript files to the 'js' folder</li>
            <li>Upload images to the 'images' folder</li>
          </ol>`,
                structure: [
                    { name: 'index.html', description: 'Main HTML file' },
                    { name: 'css/', description: 'Stylesheets' },
                    { name: 'js/', description: 'JavaScript files' },
                    { name: 'images/', description: 'Images and media' }
                ]
            },
            react: {
                title: 'React Application',
                icon: '⚛️',
                color: '#61dafb',
                description: 'Upload your React build files (npm run build output).',
                instructions: `
          <ol>
            <li>Build your React app locally: <code>npm run build</code></li>
            <li>Upload the contents of the 'build' or 'dist' folder</li>
            <li>Ensure index.html is in the root directory</li>
            <li>Static assets should be in the 'static' folder</li>
          </ol>`,
                structure: [
                    { name: 'index.html', description: 'Main HTML file' },
                    { name: 'static/', description: 'Built CSS, JS, and media files' },
                    { name: 'build/', description: 'React build output' },
                    { name: 'public/', description: 'Public assets' }
                ]
            },
            vue: {
                title: 'Vue.js Application',
                icon: '🖖',
                color: '#4fc08d',
                description: 'Upload your Vue.js build files (npm run build output).',
                instructions: `
          <ol>
            <li>Build your Vue app locally: <code>npm run build</code></li>
            <li>Upload the contents of the 'dist' folder</li>
            <li>Ensure index.html is in the root directory</li>
            <li>Assets should be in the appropriate folders</li>
          </ol>`,
                structure: [
                    { name: 'index.html', description: 'Main HTML file' },
                    { name: 'dist/', description: 'Vue build output' },
                    { name: 'assets/', description: 'Built assets' },
                    { name: 'css/', description: 'Stylesheets' }
                ]
            },
            angular: {
                title: 'Angular Application',
                icon: '🅰️',
                color: '#dd0031',
                description: 'Upload your Angular build files (ng build output).',
                instructions: `
          <ol>
            <li>Build your Angular app: <code>ng build --prod</code></li>
            <li>Upload the contents of the 'dist' folder</li>
            <li>Ensure index.html is in the root directory</li>
            <li>Configure routing for SPA if needed</li>
          </ol>`,
                structure: [
                    { name: 'index.html', description: 'Main HTML file' },
                    { name: 'dist/', description: 'Angular build output' },
                    { name: 'assets/', description: 'Static assets' },
                    { name: 'styles/', description: 'Global styles' }
                ]
            }
        };
        return frameworks[framework || 'html'] || frameworks['html'];
    }
    async createReactFiles(directory) {
        const packageJson = {
            name: "react-static-site",
            version: "1.0.0",
            description: "React static site on Achidas hosting",
            scripts: {
                build: "react-scripts build",
                start: "serve -s build"
            }
        };
        await promises_1.default.writeFile(path_1.default.join(directory, 'package.json'), JSON.stringify(packageJson, null, 2));
    }
    async createVueFiles(directory) {
        const packageJson = {
            name: "vue-static-site",
            version: "1.0.0",
            description: "Vue.js static site on Achidas hosting",
            scripts: {
                build: "vue-cli-service build",
                start: "serve -s dist"
            }
        };
        await promises_1.default.writeFile(path_1.default.join(directory, 'package.json'), JSON.stringify(packageJson, null, 2));
    }
    async createAngularFiles(directory) {
        const packageJson = {
            name: "angular-static-site",
            version: "1.0.0",
            description: "Angular static site on Achidas hosting",
            scripts: {
                build: "ng build --prod",
                start: "serve -s dist"
            }
        };
        await promises_1.default.writeFile(path_1.default.join(directory, 'package.json'), JSON.stringify(packageJson, null, 2));
    }
    async setupWebService(directory, appData) {
        const framework = appData.framework || 'nodejs';
        await this.installFrameworkRuntime(framework);
        switch (framework) {
            case 'nodejs':
                await this.setupNodeJSService(directory, appData);
                break;
            case 'python':
                await this.setupPythonService(directory, appData);
                break;
            case 'rust':
                await this.setupRustService(directory, appData);
                break;
            case 'php':
                await this.setupPHPService(directory, appData);
                break;
            case 'go':
                await this.setupGoService(directory, appData);
                break;
            case 'java':
                await this.setupJavaService(directory, appData);
                break;
            case 'dotnet':
                await this.setupDotNetService(directory, appData);
                break;
            case 'ruby':
                await this.setupRubyService(directory, appData);
                break;
            default:
                await this.setupNodeJSService(directory, appData);
        }
    }
    async installFrameworkRuntime(framework) {
        try {
            logger_1.logger.info(`Installing runtime for framework: ${framework}`);
            switch (framework) {
                case 'nodejs':
                    await this.installNodeJS();
                    break;
                case 'python':
                    await this.installPython();
                    break;
                case 'rust':
                    await this.installRust();
                    break;
                case 'php':
                    await this.installPHP();
                    break;
                case 'go':
                    await this.installGo();
                    break;
                case 'java':
                    await this.installJava();
                    break;
                case 'dotnet':
                    await this.installDotNet();
                    break;
                case 'ruby':
                    await this.installRuby();
                    break;
                default:
                    logger_1.logger.warn(`Unknown framework: ${framework}, skipping runtime installation`);
            }
        }
        catch (error) {
            logger_1.logger.error(`Failed to install runtime for ${framework}:`, error);
            throw error;
        }
    }
    async installNodeJS() {
        try {
            const nodeCheck = await execAsync('node --version').catch(() => null);
            if (nodeCheck) {
                logger_1.logger.info('Node.js already installed:', nodeCheck.stdout.trim());
                return;
            }
            logger_1.logger.info('Installing Node.js...');
            await execAsync('curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -');
            await execAsync('sudo apt-get install -y nodejs');
            await execAsync('sudo npm install -g pm2 serve nodemon');
            logger_1.logger.info('Node.js installation completed');
        }
        catch (error) {
            logger_1.logger.error('Failed to install Node.js:', error);
            throw error;
        }
    }
    async installPython() {
        try {
            const pythonCheck = await execAsync('python3 --version').catch(() => null);
            if (pythonCheck) {
                logger_1.logger.info('Python already installed:', pythonCheck.stdout.trim());
                return;
            }
            logger_1.logger.info('Installing Python...');
            await execAsync('sudo apt-get update');
            await execAsync('sudo apt-get install -y python3 python3-pip python3-venv python3-dev');
            await execAsync('sudo pip3 install fastapi uvicorn gunicorn flask django');
            logger_1.logger.info('Python installation completed');
        }
        catch (error) {
            logger_1.logger.error('Failed to install Python:', error);
            throw error;
        }
    }
    async installRust() {
        try {
            const rustCheck = await execAsync('rustc --version').catch(() => null);
            if (rustCheck) {
                logger_1.logger.info('Rust already installed:', rustCheck.stdout.trim());
                return;
            }
            logger_1.logger.info('Installing Rust...');
            await execAsync('curl --proto "=https" --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y');
            await execAsync('source ~/.cargo/env');
            await execAsync('~/.cargo/bin/cargo install cargo-watch');
            logger_1.logger.info('Rust installation completed');
        }
        catch (error) {
            logger_1.logger.error('Failed to install Rust:', error);
            throw error;
        }
    }
    async installPHP() {
        try {
            const phpCheck = await execAsync('php --version').catch(() => null);
            if (phpCheck) {
                logger_1.logger.info('PHP already installed:', phpCheck.stdout.trim());
                return;
            }
            logger_1.logger.info('Installing PHP...');
            await execAsync('sudo apt-get update');
            await execAsync('sudo apt-get install -y php php-cli php-fpm php-json php-common php-mysql php-zip php-gd php-mbstring php-curl php-xml php-pear php-bcmath');
            await execAsync('curl -sS https://getcomposer.org/installer | php');
            await execAsync('sudo mv composer.phar /usr/local/bin/composer');
            logger_1.logger.info('PHP installation completed');
        }
        catch (error) {
            logger_1.logger.error('Failed to install PHP:', error);
            throw error;
        }
    }
    async installGo() {
        try {
            const goCheck = await execAsync('go version').catch(() => null);
            if (goCheck) {
                logger_1.logger.info('Go already installed:', goCheck.stdout.trim());
                return;
            }
            logger_1.logger.info('Installing Go...');
            await execAsync('wget https://go.dev/dl/go1.21.5.linux-amd64.tar.gz');
            await execAsync('sudo rm -rf /usr/local/go && sudo tar -C /usr/local -xzf go1.21.5.linux-amd64.tar.gz');
            await execAsync('echo "export PATH=$PATH:/usr/local/go/bin" | sudo tee -a /etc/profile');
            await execAsync('rm go1.21.5.linux-amd64.tar.gz');
            logger_1.logger.info('Go installation completed');
        }
        catch (error) {
            logger_1.logger.error('Failed to install Go:', error);
            throw error;
        }
    }
    async installJava() {
        try {
            const javaCheck = await execAsync('java --version').catch(() => null);
            if (javaCheck) {
                logger_1.logger.info('Java already installed:', javaCheck.stdout.trim());
                return;
            }
            logger_1.logger.info('Installing Java...');
            await execAsync('sudo apt-get update');
            await execAsync('sudo apt-get install -y openjdk-17-jdk maven gradle');
            logger_1.logger.info('Java installation completed');
        }
        catch (error) {
            logger_1.logger.error('Failed to install Java:', error);
            throw error;
        }
    }
    async installDotNet() {
        try {
            const dotnetCheck = await execAsync('dotnet --version').catch(() => null);
            if (dotnetCheck) {
                logger_1.logger.info('.NET already installed:', dotnetCheck.stdout.trim());
                return;
            }
            logger_1.logger.info('Installing .NET...');
            await execAsync('wget https://packages.microsoft.com/config/ubuntu/22.04/packages-microsoft-prod.deb -O packages-microsoft-prod.deb');
            await execAsync('sudo dpkg -i packages-microsoft-prod.deb');
            await execAsync('rm packages-microsoft-prod.deb');
            await execAsync('sudo apt-get update');
            await execAsync('sudo apt-get install -y dotnet-sdk-8.0');
            logger_1.logger.info('.NET installation completed');
        }
        catch (error) {
            logger_1.logger.error('Failed to install .NET:', error);
            throw error;
        }
    }
    async installRuby() {
        try {
            const rubyCheck = await execAsync('ruby --version').catch(() => null);
            if (rubyCheck) {
                logger_1.logger.info('Ruby already installed:', rubyCheck.stdout.trim());
                return;
            }
            logger_1.logger.info('Installing Ruby...');
            await execAsync('sudo apt-get update');
            await execAsync('sudo apt-get install -y ruby-full build-essential zlib1g-dev');
            await execAsync('sudo gem install bundler rails');
            logger_1.logger.info('Ruby installation completed');
        }
        catch (error) {
            logger_1.logger.error('Failed to install Ruby:', error);
            throw error;
        }
    }
    async setupNodeJSService(directory, appData) {
        const packageJsonPath = path_1.default.join(directory, 'package.json');
        const hasUserPackageJson = require('fs').existsSync(packageJsonPath);
        if (!hasUserPackageJson) {
            const packageJson = {
                name: appData.name,
                version: "1.0.0",
                description: "Node.js web service on Achidas shared hosting",
                main: "index.js",
                scripts: {
                    start: appData.start_command || "node index.js",
                    build: appData.build_command || "npm install"
                }
            };
            await promises_1.default.writeFile(packageJsonPath, JSON.stringify(packageJson, null, 2));
            const indexPath = path_1.default.join(directory, 'index.js');
            const hasUserIndex = require('fs').existsSync(indexPath);
            if (!hasUserIndex) {
                const indexJs = `
const express = require('express');
const app = express();
const port = process.env.PORT || 3000;

app.use(express.json());

app.get('/', (req, res) => {
  res.json({ message: 'Hello from ${appData.name}!' });
});

app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: '${appData.name}'
  });
});

app.listen(port, () => {
  console.log(\`🚀 ${appData.name} running on port \${port}\`);
});
`;
                await promises_1.default.writeFile(indexPath, indexJs);
            }
        }
        try {
            await execAsync(`cd ${directory} && npm install`);
            logger_1.logger.info(`Installed Node.js dependencies from package.json for ${appData.name}`);
        }
        catch (error) {
            logger_1.logger.warn(`Failed to install Node.js dependencies for ${appData.name}:`, error);
        }
        const gitignorePath = path_1.default.join(directory, '.gitignore');
        const hasGitignore = require('fs').existsSync(gitignorePath);
        if (!hasGitignore) {
            await promises_1.default.writeFile(gitignorePath, 'node_modules/\n.env\n*.log\n');
        }
    }
    async setupPythonService(directory, appData) {
        const requirementsPath = path_1.default.join(directory, 'requirements.txt');
        const hasUserRequirements = require('fs').existsSync(requirementsPath);
        if (!hasUserRequirements) {
            const requirementsTxt = `fastapi==0.104.1
uvicorn==0.24.0
pydantic==2.5.0`;
            await promises_1.default.writeFile(requirementsPath, requirementsTxt);
            const mainPath = path_1.default.join(directory, 'main.py');
            const hasUserMain = require('fs').existsSync(mainPath);
            if (!hasUserMain) {
                const mainPy = `
from fastapi import FastAPI
import uvicorn
from datetime import datetime

app = FastAPI(title="${appData.name}", version="1.0.0")

@app.get("/")
async def root():
    return {"message": "Hello from ${appData.name}!"}

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "service": "${appData.name}"
    }

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
`;
                await promises_1.default.writeFile(mainPath, mainPy.trim());
            }
        }
        try {
            await execAsync(`cd ${directory} && python3 -m pip install -r requirements.txt`);
            logger_1.logger.info(`Installed Python dependencies from requirements.txt for ${appData.name}`);
        }
        catch (error) {
            logger_1.logger.warn(`Failed to install Python dependencies for ${appData.name}:`, error);
        }
        const startSh = `#!/bin/bash
${appData.start_command || 'uvicorn main:app --host 0.0.0.0 --port 8000'}
`;
        await promises_1.default.writeFile(path_1.default.join(directory, 'start.sh'), startSh.trim());
        await execAsync(`chmod +x ${path_1.default.join(directory, 'start.sh')}`);
        const gitignorePath = path_1.default.join(directory, '.gitignore');
        const hasGitignore = require('fs').existsSync(gitignorePath);
        if (!hasGitignore) {
            await promises_1.default.writeFile(gitignorePath, '__pycache__/\n*.pyc\n.env\n*.log\nvenv/\n');
        }
    }
    async setupRustService(directory, appData) {
        const cargoTomlPath = path_1.default.join(directory, 'Cargo.toml');
        const hasUserCargo = require('fs').existsSync(cargoTomlPath);
        if (!hasUserCargo) {
            const cargoToml = `[package]
name = "${appData.name.replace(/[^a-z0-9_]/g, '_')}"
version = "0.1.0"
edition = "2021"

[dependencies]
actix-web = "4.4"
serde = { version = "1.0", features = ["derive"] }
tokio = { version = "1.0", features = ["full"] }`;
            await promises_1.default.writeFile(cargoTomlPath, cargoToml);
            await execAsync(`mkdir -p ${directory}/src`);
            const mainRsPath = path_1.default.join(directory, 'src', 'main.rs');
            const hasUserMain = require('fs').existsSync(mainRsPath);
            if (!hasUserMain) {
                const mainRs = `
use actix_web::{web, App, HttpResponse, HttpServer, Result};
use serde::Serialize;

#[derive(Serialize)]
struct HealthResponse {
    status: String,
    service: String,
}

async fn health() -> Result<HttpResponse> {
    let response = HealthResponse {
        status: "healthy".to_string(),
        service: "${appData.name}".to_string(),
    };
    Ok(HttpResponse::Ok().json(response))
}

async fn hello() -> Result<HttpResponse> {
    Ok(HttpResponse::Ok().json(serde_json::json!({
        "message": format!("Hello from {}!", "${appData.name}")
    })))
}

#[actix_web::main]
async fn main() -> std::io::Result<()> {
    println!("🚀 ${appData.name} starting on port 8080");

    HttpServer::new(|| {
        App::new()
            .route("/", web::get().to(hello))
            .route("/health", web::get().to(health))
    })
    .bind("0.0.0.0:8080")?
    .run()
    .await
}
`;
                await promises_1.default.writeFile(mainRsPath, mainRs.trim());
            }
        }
        try {
            await execAsync(`cd ${directory} && cargo build --release`);
            logger_1.logger.info(`Built Rust project from Cargo.toml for ${appData.name}`);
        }
        catch (error) {
            logger_1.logger.warn(`Failed to build Rust project for ${appData.name}:`, error);
        }
        const gitignorePath = path_1.default.join(directory, '.gitignore');
        const hasGitignore = require('fs').existsSync(gitignorePath);
        if (!hasGitignore) {
            await promises_1.default.writeFile(gitignorePath, '/target\nCargo.lock\n.env\n*.log\n');
        }
    }
    async setupPHPService(directory, appData) {
        const composerJsonPath = path_1.default.join(directory, 'composer.json');
        const hasUserComposer = require('fs').existsSync(composerJsonPath);
        if (!hasUserComposer) {
            const composerJson = {
                name: `achidas/${appData.name}`,
                description: `${appData.name} PHP web service`,
                type: "project",
                require: {
                    "php": ">=8.0"
                },
                autoload: {
                    "psr-4": {
                        "App\\": "src/"
                    }
                }
            };
            await promises_1.default.writeFile(composerJsonPath, JSON.stringify(composerJson, null, 2));
            const indexPath = path_1.default.join(directory, 'index.php');
            const hasUserIndex = require('fs').existsSync(indexPath);
            if (!hasUserIndex) {
                const indexPhp = `
<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');

$request_uri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);

switch ($request_uri) {
    case '/':
        echo json_encode(['message' => 'Hello from ${appData.name}!']);
        break;
    case '/health':
        echo json_encode([
            'status' => 'healthy',
            'timestamp' => date('c'),
            'service' => '${appData.name}'
        ]);
        break;
    default:
        http_response_code(404);
        echo json_encode(['error' => 'Not found']);
}
?>`;
                await promises_1.default.writeFile(indexPath, indexPhp.trim());
            }
        }
        try {
            await execAsync(`cd ${directory} && composer install --no-dev`);
            logger_1.logger.info(`Installed PHP dependencies from composer.json for ${appData.name}`);
        }
        catch (error) {
            logger_1.logger.warn(`Failed to install PHP dependencies for ${appData.name}:`, error);
        }
        const gitignorePath = path_1.default.join(directory, '.gitignore');
        const hasGitignore = require('fs').existsSync(gitignorePath);
        if (!hasGitignore) {
            await promises_1.default.writeFile(gitignorePath, '/vendor\n.env\n*.log\n');
        }
    }
    async setupGoService(directory, appData) {
        const goModPath = path_1.default.join(directory, 'go.mod');
        const hasUserGoMod = require('fs').existsSync(goModPath);
        if (!hasUserGoMod) {
            const goMod = `module ${appData.name}

go 1.21

require (
    github.com/gin-gonic/gin v1.9.1
)`;
            await promises_1.default.writeFile(goModPath, goMod);
            const mainGoPath = path_1.default.join(directory, 'main.go');
            const hasUserMain = require('fs').existsSync(mainGoPath);
            if (!hasUserMain) {
                const mainGo = `
package main

import (
    "net/http"
    "github.com/gin-gonic/gin"
)

func main() {
    r := gin.Default()

    r.GET("/", func(c *gin.Context) {
        c.JSON(http.StatusOK, gin.H{
            "message": "Hello from ${appData.name}!",
        })
    })

    r.GET("/health", func(c *gin.Context) {
        c.JSON(http.StatusOK, gin.H{
            "status":  "healthy",
            "service": "${appData.name}",
        })
    })

    r.Run(":8080")
}
`;
                await promises_1.default.writeFile(mainGoPath, mainGo.trim());
            }
        }
        try {
            await execAsync(`cd ${directory} && go mod tidy`);
            await execAsync(`cd ${directory} && go build`);
            logger_1.logger.info(`Built Go project from go.mod for ${appData.name}`);
        }
        catch (error) {
            logger_1.logger.warn(`Failed to build Go project for ${appData.name}:`, error);
        }
        const gitignorePath = path_1.default.join(directory, '.gitignore');
        const hasGitignore = require('fs').existsSync(gitignorePath);
        if (!hasGitignore) {
            await promises_1.default.writeFile(gitignorePath, '*.exe\n*.log\n.env\n');
        }
    }
    async setupJavaService(directory, appData) {
        const pomXmlPath = path_1.default.join(directory, 'pom.xml');
        const hasUserPom = require('fs').existsSync(pomXmlPath);
        if (!hasUserPom) {
            const pomXml = `<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.achidas</groupId>
    <artifactId>${appData.name}</artifactId>
    <version>1.0.0</version>
    <packaging>jar</packaging>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <version>3.1.0</version>
        </dependency>
    </dependencies>
</project>`;
            await promises_1.default.writeFile(pomXmlPath, pomXml);
            await execAsync(`mkdir -p ${directory}/src/main/java/com/achidas`);
            const mainJavaPath = path_1.default.join(directory, 'src/main/java/com/achidas/Application.java');
            const hasUserMain = require('fs').existsSync(mainJavaPath);
            if (!hasUserMain) {
                const mainJava = `package com.achidas;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@SpringBootApplication
@RestController
public class Application {

    @GetMapping("/")
    public String hello() {
        return "Hello from ${appData.name}!";
    }

    @GetMapping("/health")
    public String health() {
        return "{\\"status\\": \\"healthy\\", \\"service\\": \\"${appData.name}\\"}";
    }

    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}`;
                await promises_1.default.writeFile(mainJavaPath, mainJava);
            }
        }
        try {
            await execAsync(`cd ${directory} && mvn clean compile`);
            logger_1.logger.info(`Built Java project from pom.xml for ${appData.name}`);
        }
        catch (error) {
            logger_1.logger.warn(`Failed to build Java project for ${appData.name}:`, error);
        }
        const gitignorePath = path_1.default.join(directory, '.gitignore');
        const hasGitignore = require('fs').existsSync(gitignorePath);
        if (!hasGitignore) {
            await promises_1.default.writeFile(gitignorePath, '/target\n*.class\n.env\n*.log\n');
        }
    }
    async setupDotNetService(directory, appData) {
        const csprojPath = path_1.default.join(directory, `${appData.name}.csproj`);
        const hasUserCsproj = require('fs').existsSync(csprojPath);
        if (!hasUserCsproj) {
            const csproj = `<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>
</Project>`;
            await promises_1.default.writeFile(csprojPath, csproj);
            const programPath = path_1.default.join(directory, 'Program.cs');
            const hasUserProgram = require('fs').existsSync(programPath);
            if (!hasUserProgram) {
                const programCs = `var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.MapGet("/", () => "Hello from ${appData.name}!");
app.MapGet("/health", () => new { status = "healthy", service = "${appData.name}" });

app.Run();`;
                await promises_1.default.writeFile(programPath, programCs);
            }
        }
        try {
            await execAsync(`cd ${directory} && dotnet build`);
            logger_1.logger.info(`Built .NET project from .csproj for ${appData.name}`);
        }
        catch (error) {
            logger_1.logger.warn(`Failed to build .NET project for ${appData.name}:`, error);
        }
        const gitignorePath = path_1.default.join(directory, '.gitignore');
        const hasGitignore = require('fs').existsSync(gitignorePath);
        if (!hasGitignore) {
            await promises_1.default.writeFile(gitignorePath, '/bin\n/obj\n.env\n*.log\n');
        }
    }
    async setupRubyService(directory, appData) {
        const gemfilePath = path_1.default.join(directory, 'Gemfile');
        const hasUserGemfile = require('fs').existsSync(gemfilePath);
        if (!hasUserGemfile) {
            const gemfile = `source 'https://rubygems.org'

gem 'sinatra'
gem 'puma'`;
            await promises_1.default.writeFile(gemfilePath, gemfile);
            const appRbPath = path_1.default.join(directory, 'app.rb');
            const hasUserApp = require('fs').existsSync(appRbPath);
            if (!hasUserApp) {
                const appRb = `require 'sinatra'
require 'json'

get '/' do
  content_type :json
  { message: 'Hello from ${appData.name}!' }.to_json
end

get '/health' do
  content_type :json
  { status: 'healthy', service: '${appData.name}' }.to_json
end`;
                await promises_1.default.writeFile(appRbPath, appRb);
            }
        }
        try {
            await execAsync(`cd ${directory} && bundle install`);
            logger_1.logger.info(`Installed Ruby dependencies from Gemfile for ${appData.name}`);
        }
        catch (error) {
            logger_1.logger.warn(`Failed to install Ruby dependencies for ${appData.name}:`, error);
        }
        const gitignorePath = path_1.default.join(directory, '.gitignore');
        const hasGitignore = require('fs').existsSync(gitignorePath);
        if (!hasGitignore) {
            await promises_1.default.writeFile(gitignorePath, '.bundle\nvendor\n.env\n*.log\n');
        }
    }
    async getUsers(filters) {
        try {
            const mockUsers = [
                {
                    id: 'user_1',
                    user_id: 'user_1',
                    username: 'user_testuser1',
                    linux_username: 'user_testuser1',
                    home_directory: '/home/<USER>',
                    plan: 'starter',
                    status: 'active',
                    server_id: this.SHARED_SERVER_IP,
                    server_ip: this.SHARED_SERVER_IP,
                    port: 8001,
                    ssh_port: 2201,
                    ftp_port: 2101,
                    resource_limits: {
                        cpu_quota: 5,
                        memory_max: 256,
                        bandwidth_limit: 25,
                        storage_limit: 5
                    },
                    usage: {
                        cpu_usage: 2.5,
                        memory_usage: 128,
                        bandwidth_used: 5.2,
                        storage_used: 1.8
                    },
                    created_at: new Date('2024-01-15').toISOString(),
                    applications: []
                }
            ];
            let filteredUsers = mockUsers;
            if (filters.status) {
                filteredUsers = filteredUsers.filter(user => user.status === filters.status);
            }
            if (filters.plan) {
                filteredUsers = filteredUsers.filter(user => user.plan === filters.plan);
            }
            if (filters.server_id) {
                filteredUsers = filteredUsers.filter(user => user.server_id === filters.server_id);
            }
            if (filters.search) {
                const searchLower = filters.search.toLowerCase();
                filteredUsers = filteredUsers.filter(user => user.username.toLowerCase().includes(searchLower) ||
                    user.linux_username.toLowerCase().includes(searchLower));
            }
            const total = filteredUsers.length;
            const totalPages = Math.ceil(total / filters.limit);
            const startIndex = (filters.page - 1) * filters.limit;
            const endIndex = startIndex + filters.limit;
            const users = filteredUsers.slice(startIndex, endIndex);
            logger_1.logger.info(`Retrieved ${users.length} users (page ${filters.page}/${totalPages})`);
            return {
                users,
                total,
                page: filters.page,
                limit: filters.limit,
                totalPages
            };
        }
        catch (error) {
            logger_1.logger.error('Failed to get users:', error);
            throw error;
        }
    }
    async getUser(userId) {
        try {
            if (userId === 'user_1') {
                return {
                    id: 'user_1',
                    user_id: 'user_1',
                    username: 'user_testuser1',
                    linux_username: 'user_testuser1',
                    home_directory: '/home/<USER>',
                    plan: 'starter',
                    status: 'active',
                    server_id: this.SHARED_SERVER_IP,
                    server_ip: this.SHARED_SERVER_IP,
                    port: 8001,
                    ssh_port: 2201,
                    ftp_port: 2101,
                    resource_limits: {
                        cpu_quota: 5,
                        memory_max: 256,
                        bandwidth_limit: 25,
                        storage_limit: 5
                    },
                    usage: {
                        cpu_usage: 2.5,
                        memory_usage: 128,
                        bandwidth_used: 5.2,
                        storage_used: 1.8
                    },
                    created_at: new Date('2024-01-15').toISOString(),
                    applications: [
                        {
                            id: 'app_1',
                            name: 'My Website',
                            type: 'static-website',
                            framework: 'html',
                            status: 'running',
                            subdomain: 'mywebsite-user1.achidas.com',
                            directory: '/home/<USER>/apps/static-website/My Website',
                            ssl_enabled: false,
                            domain: 'mywebsite.com',
                            created_at: new Date('2024-01-16').toISOString()
                        }
                    ]
                };
            }
            return null;
        }
        catch (error) {
            logger_1.logger.error(`Failed to get user ${userId}:`, error);
            throw error;
        }
    }
    async updateUser(userId, updateData) {
        try {
            const user = await this.getUser(userId);
            if (!user) {
                throw new Error('User not found');
            }
            if (updateData.plan) {
                user.plan = updateData.plan;
                const limits = this.getPlanLimits(updateData.plan);
                user.resource_limits = {
                    cpu_quota: limits.cpu_quota,
                    memory_max: limits.memory_max,
                    bandwidth_limit: limits.bandwidth_limit,
                    storage_limit: limits.storage_limit
                };
                await this.setupResourceLimits(user.linux_username, updateData.plan);
            }
            if (updateData.status) {
                user.status = updateData.status;
            }
            if (updateData.resource_limits) {
                user.resource_limits = { ...user.resource_limits, ...updateData.resource_limits };
            }
            logger_1.logger.info(`Updated user ${userId}`);
            return user;
        }
        catch (error) {
            logger_1.logger.error(`Failed to update user ${userId}:`, error);
            throw error;
        }
    }
    async deleteUser(userId) {
        const ssh = new SSHConnection();
        try {
            const user = await this.getUser(userId);
            if (!user) {
                throw new Error('User not found');
            }
            await ssh.connect();
            await ssh.executeCommand(`pkill -u ${user.linux_username} || true`);
            await ssh.executeCommand(`userdel -r ${user.linux_username} || true`);
            await ssh.executeCommand(`rm -rf /etc/systemd/system/${user.linux_username}.slice.d || true`);
            await ssh.executeCommand('systemctl daemon-reexec');
            logger_1.logger.info(`Deleted user ${userId} and cleaned up resources`);
        }
        catch (error) {
            logger_1.logger.error(`Failed to delete user ${userId}:`, error);
            throw error;
        }
        finally {
            await ssh.disconnect();
        }
    }
    async getAnalytics(period, serverId) {
        try {
            const analytics = {
                period,
                server_id: serverId || 'all',
                total_users: 25,
                active_users: 22,
                suspended_users: 2,
                inactive_users: 1,
                total_applications: 45,
                running_applications: 38,
                stopped_applications: 7,
                resource_usage: {
                    cpu_average: 15.5,
                    memory_average: 68.2,
                    bandwidth_total: 1250.5,
                    storage_total: 89.3
                },
                plan_distribution: {
                    free: 8,
                    starter: 12,
                    basic: 3,
                    standard: 1,
                    pro: 1
                },
                revenue: {
                    monthly: 245.50,
                    projected_annual: 2946.00
                },
                growth: {
                    new_users_this_period: 5,
                    churn_rate: 2.1,
                    upgrade_rate: 8.5
                }
            };
            logger_1.logger.info(`Retrieved analytics for period: ${period}`);
            return analytics;
        }
        catch (error) {
            logger_1.logger.error('Failed to get analytics:', error);
            throw error;
        }
    }
    async getUserResourceUsage(userId, period) {
        try {
            const user = await this.getUser(userId);
            if (!user) {
                throw new Error('User not found');
            }
            const usage = {
                user_id: userId,
                period,
                cpu_usage: [
                    { timestamp: '2024-01-01T00:00:00Z', value: 2.1 },
                    { timestamp: '2024-01-01T01:00:00Z', value: 3.2 },
                    { timestamp: '2024-01-01T02:00:00Z', value: 1.8 }
                ],
                memory_usage: [
                    { timestamp: '2024-01-01T00:00:00Z', value: 128 },
                    { timestamp: '2024-01-01T01:00:00Z', value: 145 },
                    { timestamp: '2024-01-01T02:00:00Z', value: 132 }
                ],
                bandwidth_usage: [
                    { timestamp: '2024-01-01T00:00:00Z', value: 0.5 },
                    { timestamp: '2024-01-01T01:00:00Z', value: 1.2 },
                    { timestamp: '2024-01-01T02:00:00Z', value: 0.8 }
                ],
                storage_usage: user.usage.storage_used,
                limits: user.resource_limits,
                alerts: []
            };
            logger_1.logger.info(`Retrieved resource usage for user: ${userId}, period: ${period}`);
            return usage;
        }
        catch (error) {
            logger_1.logger.error(`Failed to get user resource usage for ${userId}:`, error);
            throw error;
        }
    }
    async getServerCapacity(serverId) {
        try {
            const capacity = {
                server_id: serverId || this.SHARED_SERVER_IP,
                total_capacity: {
                    cpu_cores: 4,
                    memory_gb: 8,
                    storage_gb: 160,
                    bandwidth_gb: 1000
                },
                used_capacity: {
                    cpu_percent: 35.5,
                    memory_percent: 68.2,
                    storage_percent: 45.8,
                    bandwidth_percent: 12.5
                },
                available_capacity: {
                    cpu_percent: 64.5,
                    memory_percent: 31.8,
                    storage_percent: 54.2,
                    bandwidth_percent: 87.5
                },
                user_count: 25,
                max_users: 50,
                status: 'healthy',
                last_updated: new Date().toISOString()
            };
            logger_1.logger.info(`Retrieved server capacity${serverId ? ` for server: ${serverId}` : ' for all servers'}`);
            return capacity;
        }
        catch (error) {
            logger_1.logger.error('Failed to get server capacity:', error);
            throw error;
        }
    }
    async suspendUser(userId, reason) {
        const ssh = new SSHConnection();
        try {
            const user = await this.getUser(userId);
            if (!user) {
                throw new Error('User not found');
            }
            await ssh.connect();
            await ssh.executeCommand(`pkill -u ${user.linux_username} || true`);
            await ssh.executeCommand(`usermod -L ${user.linux_username}`);
            user.status = 'suspended';
            logger_1.logger.info(`Suspended user ${userId}, reason: ${reason || 'No reason provided'}`);
        }
        catch (error) {
            logger_1.logger.error(`Failed to suspend user ${userId}:`, error);
            throw error;
        }
        finally {
            await ssh.disconnect();
        }
    }
    async reactivateUser(userId) {
        const ssh = new SSHConnection();
        try {
            const user = await this.getUser(userId);
            if (!user) {
                throw new Error('User not found');
            }
            await ssh.connect();
            await ssh.executeCommand(`usermod -U ${user.linux_username}`);
            user.status = 'active';
            logger_1.logger.info(`Reactivated user ${userId}`);
        }
        catch (error) {
            logger_1.logger.error(`Failed to reactivate user ${userId}:`, error);
            throw error;
        }
        finally {
            await ssh.disconnect();
        }
    }
    async resetUserPassword(userId, newPassword) {
        const ssh = new SSHConnection();
        try {
            const user = await this.getUser(userId);
            if (!user) {
                throw new Error('User not found');
            }
            const password = newPassword || Math.random().toString(36).slice(-12);
            await ssh.connect();
            await ssh.executeCommand(`echo "${user.linux_username}:${password}" | chpasswd`);
            logger_1.logger.info(`Reset password for user ${userId}`);
            return { password };
        }
        catch (error) {
            logger_1.logger.error(`Failed to reset password for user ${userId}:`, error);
            throw error;
        }
        finally {
            await ssh.disconnect();
        }
    }
    async getUserApplications(userId, filters) {
        try {
            const user = await this.getUser(userId);
            if (!user) {
                throw new Error('User not found');
            }
            let applications = user.applications || [];
            if (filters.status) {
                applications = applications.filter(app => app.status === filters.status);
            }
            if (filters.type) {
                applications = applications.filter(app => app.type === filters.type);
            }
            const total = applications.length;
            const totalPages = Math.ceil(total / filters.limit);
            const startIndex = (filters.page - 1) * filters.limit;
            const endIndex = startIndex + filters.limit;
            const paginatedApps = applications.slice(startIndex, endIndex);
            logger_1.logger.info(`Retrieved ${paginatedApps.length} applications for user: ${userId}`);
            return {
                applications: paginatedApps,
                total,
                page: filters.page,
                limit: filters.limit,
                totalPages
            };
        }
        catch (error) {
            logger_1.logger.error(`Failed to get applications for user ${userId}:`, error);
            throw error;
        }
    }
    async updateApplication(userId, appId, updateData) {
        try {
            const user = await this.getUser(userId);
            if (!user) {
                throw new Error('User not found');
            }
            const app = user.applications?.find(a => a.id === appId);
            if (!app) {
                throw new Error('Application not found');
            }
            Object.assign(app, updateData);
            logger_1.logger.info(`Updated application ${appId} for user: ${userId}`);
            return app;
        }
        catch (error) {
            logger_1.logger.error(`Failed to update application ${appId} for user ${userId}:`, error);
            throw error;
        }
    }
    async deleteApplication(userId, appId) {
        const ssh = new SSHConnection();
        try {
            const user = await this.getUser(userId);
            if (!user) {
                throw new Error('User not found');
            }
            const appIndex = user.applications?.findIndex(a => a.id === appId);
            if (appIndex === -1 || appIndex === undefined) {
                throw new Error('Application not found');
            }
            await ssh.connect();
            await ssh.executeCommand(`pkill -f "${appId}" || true`);
            await ssh.executeCommand(`rm -rf ${user.home_directory}/apps/${appId} || true`);
            user.applications?.splice(appIndex, 1);
            logger_1.logger.info(`Deleted application ${appId} for user: ${userId}`);
        }
        catch (error) {
            logger_1.logger.error(`Failed to delete application ${appId} for user ${userId}:`, error);
            throw error;
        }
        finally {
            await ssh.disconnect();
        }
    }
}
let sharedHostingService;
function getSharedHostingService() {
    if (!sharedHostingService) {
        sharedHostingService = new SharedHostingService();
    }
    return sharedHostingService;
}
//# sourceMappingURL=shared-hosting.js.map