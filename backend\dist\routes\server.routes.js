"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.serverRoutes = serverRoutes;
const auth_1 = require("../middleware/auth");
const server_controller_1 = require("../controllers/server.controller");
const hosting_controller_1 = require("../controllers/hosting.controller");
const application_controller_1 = require("../controllers/application.controller");
async function serverRoutes(fastify) {
    fastify.get('/regions', server_controller_1.getServerRegionsController);
    fastify.get('/plans', server_controller_1.getServerPlansController);
    fastify.get('/hosting/plans', hosting_controller_1.getHostingPlansController);
    fastify.get('/hosting/plans/recommend', hosting_controller_1.getRecommendedPlanController);
    fastify.get('/hosting/plans/:planName', hosting_controller_1.getHostingPlanController);
    fastify.register(async function (fastify) {
        fastify.addHook('preHandler', auth_1.authMiddleware);
        fastify.get('/', server_controller_1.getServersController);
        fastify.get('/shared/status', server_controller_1.getSharedServerStatusController);
        fastify.get('/:serverId', server_controller_1.getServerController);
        fastify.get('/:serverId/metrics', server_controller_1.getServerMetricsController);
        fastify.post('/provision', server_controller_1.provisionServerController);
        fastify.get('/hosting/shared/users', hosting_controller_1.getSharedUsersController);
        fastify.post('/hosting/shared/users', hosting_controller_1.createSharedUserController);
        fastify.get('/hosting/shared/users/:userId', hosting_controller_1.getSharedUserController);
        fastify.put('/hosting/shared/users/:userId', hosting_controller_1.updateSharedUserController);
        fastify.delete('/hosting/shared/users/:userId', hosting_controller_1.deleteSharedUserController);
        fastify.get('/hosting/shared/analytics', hosting_controller_1.getSharedHostingAnalyticsController);
        fastify.get('/hosting/shared/users/:userId/usage', hosting_controller_1.getUserResourceUsageController);
        fastify.get('/hosting/shared/servers/capacity', hosting_controller_1.getServerCapacityController);
        fastify.get('/hosting/shared/servers/:serverId/capacity', hosting_controller_1.getServerCapacityController);
        fastify.post('/hosting/shared/users/:userId/suspend', hosting_controller_1.suspendUserController);
        fastify.post('/hosting/shared/users/:userId/reactivate', hosting_controller_1.reactivateUserController);
        fastify.post('/hosting/shared/users/:userId/reset-password', hosting_controller_1.resetUserPasswordController);
        fastify.get('/hosting/shared/test-ssh', hosting_controller_1.testSSHConnectionController);
        fastify.get('/hosting/shared/users/:userId/applications', hosting_controller_1.getUserApplicationsController);
        fastify.post('/hosting/shared/users/:userId/applications', hosting_controller_1.createApplicationController);
        fastify.put('/hosting/shared/users/:userId/applications/:appId', hosting_controller_1.updateUserApplicationController);
        fastify.delete('/hosting/shared/users/:userId/applications/:appId', hosting_controller_1.deleteUserApplicationController);
        fastify.post('/applications', application_controller_1.createApplicationController);
        fastify.get('/applications', application_controller_1.getApplicationsController);
        fastify.get('/applications/stats', application_controller_1.getApplicationStatsController);
        fastify.get('/applications/:id', application_controller_1.getApplicationController);
        fastify.put('/applications/:id', application_controller_1.updateApplicationController);
        fastify.delete('/applications/:id', application_controller_1.deleteApplicationController);
        fastify.post('/applications/:id/deploy', application_controller_1.deployApplicationController);
        fastify.post('/applications/:id/stop', application_controller_1.stopApplicationController);
    });
}
//# sourceMappingURL=server.routes.js.map