export declare const appConfig: {
    readonly server: {
        readonly port: number;
        readonly host: string;
        readonly environment: "development" | "staging" | "production";
        readonly env: "development" | "staging" | "production";
    };
    readonly database: {
        readonly url: string;
        readonly mysqlRootPassword: string | undefined;
        readonly postgresPassword: string | undefined;
    };
    readonly auth: {
        readonly jwtSecret: string;
        readonly jwtExpiresIn: string;
    };
    readonly vultr: {
        readonly apiKey: string;
        readonly baseUrl: string;
    };
    readonly resellerBiz: {
        readonly authUserId: string;
        readonly apiKey: string;
        readonly baseUrl: string;
        readonly testMode: boolean;
    };
    readonly logging: {
        readonly level: "fatal" | "error" | "warn" | "info" | "debug" | "trace";
        readonly pretty: boolean;
    };
    readonly rateLimit: {
        readonly max: number;
        readonly window: number;
        readonly timeWindow: number;
    };
    readonly cors: {
        readonly origin: true | string[];
        readonly credentials: boolean;
        readonly methods: readonly ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"];
        readonly allowedHeaders: readonly ["Content-Type", "Authorization", "X-Requested-With", "X-Request-ID"];
    };
    readonly monitoring: {
        readonly jaegerEndpoint: string | undefined;
        readonly metricsPort: number;
        readonly enabled: boolean;
        readonly intervalSeconds: number;
        readonly retentionDays: number;
    };
    readonly circuitBreaker: {
        readonly failureThreshold: number;
        readonly timeoutSeconds: number;
    };
    readonly sharedHosting: {
        readonly servers: any;
        readonly maxUsersPerServer: number;
        readonly basePort: number;
        readonly sshBasePort: number;
        readonly ftpBasePort: number;
    };
    readonly ssh: {
        readonly host: string | undefined;
        readonly port: number;
        readonly username: string;
        readonly password: string | undefined;
        readonly privateKeyPath: string | undefined;
        readonly timeout: number;
    };
    readonly payment: {
        readonly gracePeriodDaysDefault: number;
        readonly gracePeriodDaysPremium: number;
        readonly deletionDelayDays: number;
        readonly checkIntervalHours: number;
    };
    readonly sync: {
        readonly intervalMinutes: number;
        readonly enabled: boolean;
    };
    readonly security: {
        readonly monitoringEnabled: boolean;
        readonly maxLoginAttempts: number;
        readonly lockoutDurationMinutes: number;
        readonly abuseCheckIntervalMinutes: number;
    };
    readonly domain: {
        readonly primary: string;
    };
    readonly cloudflare: {
        readonly apiToken: string | undefined;
        readonly zoneId: string | undefined;
        readonly dnsEnabled: boolean;
    };
    readonly backup: {
        readonly enabled: boolean;
        readonly intervalHours: number;
        readonly retentionDays: number;
        readonly storagePath: string;
        readonly encryptionKey: string | undefined;
        readonly bucketName: string | undefined;
    };
    readonly deployment: {
        readonly timeoutMinutes: number;
        readonly buildTimeoutMinutes: number;
        readonly maxConcurrentDeployments: number;
    };
};
export type AppConfig = typeof appConfig;
//# sourceMappingURL=index.d.ts.map