"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserUsageAnalytics = exports.PaymentStatus = exports.ServerMetrics = exports.SharedHostingApplication = exports.SharedHostingUser = void 0;
const mongoose_1 = require("mongoose");
const SharedHostingUserSchema = new mongoose_1.Schema({
    user_id: { type: String, required: true, unique: true, index: true },
    username: { type: String, required: true, unique: true, index: true },
    linux_username: { type: String, required: true, unique: true, index: true },
    email: { type: String, required: true, index: true },
    home_directory: { type: String, required: true },
    plan: { type: String, required: true, index: true },
    status: {
        type: String,
        enum: ['active', 'suspended', 'deleted', 'pending'],
        default: 'pending',
        index: true
    },
    server_id: { type: String, required: true, index: true },
    server_ip: { type: String, required: true },
    port: { type: Number, required: true },
    ssh_port: { type: Number, required: true },
    ftp_port: { type: Number, required: true },
    resource_limits: {
        cpu_quota: { type: Number, required: true },
        memory_max: { type: Number, required: true },
        bandwidth_limit: { type: Number, required: true },
        storage_limit: { type: Number, required: true }
    },
    usage: {
        cpu_usage: { type: Number, default: 0 },
        memory_usage: { type: Number, default: 0 },
        bandwidth_used: { type: Number, default: 0 },
        storage_used: { type: Number, default: 0 }
    },
    created_at: { type: Date, default: Date.now, index: true },
    updated_at: { type: Date, default: Date.now },
    last_login: { type: Date },
    applications: [{ type: mongoose_1.Schema.Types.ObjectId, ref: 'SharedHostingApplication' }]
}, {
    timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
    collection: 'shared_hosting_users'
});
const SharedHostingApplicationSchema = new mongoose_1.Schema({
    user_id: { type: String, required: true, index: true },
    name: { type: String, required: true },
    type: {
        type: String,
        enum: ['static-website', 'web-service', 'api', 'database'],
        required: true,
        index: true
    },
    framework: { type: String, required: true },
    language: { type: String, required: true },
    status: {
        type: String,
        enum: ['running', 'stopped', 'building', 'failed', 'pending'],
        default: 'pending',
        index: true
    },
    subdomain: { type: String, required: true, unique: true, index: true },
    directory: { type: String, required: true },
    ssl_enabled: { type: Boolean, default: false },
    domain: { type: String, index: true },
    environment_variables: { type: Map, of: String, default: {} },
    build_command: { type: String },
    start_command: { type: String },
    port: { type: Number },
    created_at: { type: Date, default: Date.now, index: true },
    updated_at: { type: Date, default: Date.now },
    last_deployed: { type: Date },
    deployment_logs: [{ type: mongoose_1.Schema.Types.ObjectId, ref: 'DeploymentLog' }]
}, {
    timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
    collection: 'shared_hosting_applications'
});
const ServerMetricsSchema = new mongoose_1.Schema({
    server_id: { type: String, required: true, index: true },
    server_ip: { type: String, required: true },
    timestamp: { type: Date, default: Date.now, index: true },
    cpu_usage: { type: Number, required: true },
    memory_usage: { type: Number, required: true },
    disk_usage: { type: Number, required: true },
    load_average: { type: Number, required: true },
    network_in: { type: Number, default: 0 },
    network_out: { type: Number, default: 0 },
    active_users: { type: Number, default: 0 },
    active_applications: { type: Number, default: 0 }
}, {
    collection: 'server_metrics'
});
const PaymentStatusSchema = new mongoose_1.Schema({
    user_id: { type: String, required: true, unique: true, index: true },
    status: {
        type: String,
        enum: ['active', 'grace_period', 'suspended', 'deleted'],
        default: 'active',
        index: true
    },
    last_payment_date: { type: Date },
    next_payment_due: { type: Date, index: true },
    grace_period_start: { type: Date },
    grace_period_end: { type: Date, index: true },
    deletion_scheduled: { type: Date, index: true },
    payment_method: { type: String },
    subscription_id: { type: String },
    created_at: { type: Date, default: Date.now },
    updated_at: { type: Date, default: Date.now }
}, {
    timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
    collection: 'payment_status'
});
const UserUsageAnalyticsSchema = new mongoose_1.Schema({
    user_id: { type: String, required: true, index: true },
    date: { type: Date, required: true, index: true },
    cpu_usage_avg: { type: Number, default: 0 },
    cpu_usage_max: { type: Number, default: 0 },
    memory_usage_avg: { type: Number, default: 0 },
    memory_usage_max: { type: Number, default: 0 },
    bandwidth_used: { type: Number, default: 0 },
    storage_used: { type: Number, default: 0 },
    requests_count: { type: Number, default: 0 },
    uptime_percentage: { type: Number, default: 100 }
}, {
    collection: 'user_usage_analytics'
});
SharedHostingUserSchema.index({ server_id: 1, status: 1 });
SharedHostingUserSchema.index({ plan: 1, status: 1 });
SharedHostingApplicationSchema.index({ user_id: 1, status: 1 });
ServerMetricsSchema.index({ server_id: 1, timestamp: -1 });
UserUsageAnalyticsSchema.index({ user_id: 1, date: -1 });
PaymentStatusSchema.index({ status: 1, grace_period_end: 1 });
PaymentStatusSchema.index({ status: 1, deletion_scheduled: 1 });
exports.SharedHostingUser = (0, mongoose_1.model)('SharedHostingUser', SharedHostingUserSchema);
exports.SharedHostingApplication = (0, mongoose_1.model)('SharedHostingApplication', SharedHostingApplicationSchema);
exports.ServerMetrics = (0, mongoose_1.model)('ServerMetrics', ServerMetricsSchema);
exports.PaymentStatus = (0, mongoose_1.model)('PaymentStatus', PaymentStatusSchema);
exports.UserUsageAnalytics = (0, mongoose_1.model)('UserUsageAnalytics', UserUsageAnalyticsSchema);
//# sourceMappingURL=shared-hosting.js.map