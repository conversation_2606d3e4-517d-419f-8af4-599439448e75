import React from 'react';
import { GlassStyle, LiquidGlass as LiquidGlass$1 } from '@specy/liquid-glass';
export { LiquidGlass as CoreLiquidGlass, GlassStyle, PaintLayerCache } from '@specy/liquid-glass';

interface LiquidGlassProps {
    /**
     * Custom CSS styles to apply to the glass container
     */
    style?: string;
    /**
     * React CSS properties to apply to the wrapper div
     */
    wrapperStyle?: React.CSSProperties;
    /**
     * Glass material properties
     */
    glassStyle?: GlassStyle;
    /**
     * Children to render inside the glass container
     */
    children?: React.ReactNode;
    /**
     * Callback when the liquid glass instance is ready
     */
    onReady?: (instance: LiquidGlass$1) => void;
    /**
     * The target element to capture for the glass background effect.
     * If not provided, document.body will be used.
     */
    targetElement?: HTMLElement;
    /**
     * Optional key to force re-rendering of the component
     * Useful for cases where you want to reset the component state
     */
    renderKey?: string | number;
}
interface LiquidGlassRef {
    /**
     * Get the underlying LiquidGlass instance
     */
    getInstance: () => LiquidGlass$1 | null;
    /**
     * Update the screenshot of the background
     */
    updateScreenshot: () => Promise<void>;
    /**
     * Force update the glass effect
     */
    forceUpdate: () => Promise<void>;
    /**
     * Update glass style properties
     */
    updateGlassStyle: (style: Partial<GlassStyle>) => void;
    /**
     * Get current glass style properties
     */
    getGlassStyle: () => Required<GlassStyle> | null;
    /**
     * Get the glass DOM element
     */
    getElement: () => HTMLElement | null;
    /**
     * Get the content container element
     */
    getContent: () => HTMLDivElement | null;
    /**
     * Force update the position of the glass effect
     */
    forcePositionUpdate: () => void;
    /**
     * Force update the size of the glass effect
     */
    forceSizeUpdate: () => void;
}
declare const LiquidGlass: React.ForwardRefExoticComponent<LiquidGlassProps & React.RefAttributes<LiquidGlassRef>>;

export { LiquidGlass, type LiquidGlassProps, type LiquidGlassRef };
