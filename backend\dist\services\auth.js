"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
exports.getAuthService = getAuthService;
const bcrypt = __importStar(require("bcryptjs"));
const jwt = __importStar(require("jsonwebtoken"));
const mongodb_1 = require("mongodb");
const config_1 = require("../config");
const connection_1 = require("../database/connection");
const models_1 = require("../models");
const logger_1 = require("../utils/logger");
class AuthService {
    usersCollection;
    constructor() {
        this.usersCollection = connection_1.databaseConnection.getCollection('users');
    }
    async registerUser(userData) {
        try {
            const existingUser = await this.usersCollection.findOne({
                $or: [
                    { email: userData.email },
                    { username: userData.username }
                ]
            });
            if (existingUser) {
                if (existingUser.email === userData.email) {
                    throw new Error('User with this email already exists');
                }
                if (existingUser.username === userData.username) {
                    throw new Error('User with this username already exists');
                }
            }
            const saltRounds = 12;
            const passwordHash = await bcrypt.hash(userData.password, saltRounds);
            const now = new Date();
            const newUser = {
                email: userData.email,
                username: userData.username,
                password_hash: passwordHash,
                first_name: userData.first_name,
                last_name: userData.last_name,
                company: userData.company,
                phone: userData.phone,
                role: models_1.UserRole.USER,
                status: models_1.UserStatus.ACTIVE,
                email_verified: false,
                created_at: now,
                updated_at: now,
                metadata: {
                    login_attempts: 0,
                },
            };
            const result = await this.usersCollection.insertOne(newUser);
            const createdUser = await this.usersCollection.findOne({ _id: result.insertedId });
            if (!createdUser) {
                throw new Error('Failed to create user');
            }
            logger_1.logger.info(`User registered successfully: ${userData.email}`);
            return (0, models_1.toUserResponse)(createdUser);
        }
        catch (error) {
            logger_1.logger.error('User registration error:', error);
            throw error;
        }
    }
    async loginUser(loginData) {
        try {
            const user = await this.usersCollection.findOne({ email: loginData.email });
            if (!user) {
                throw new Error('Invalid email or password');
            }
            if (user.status !== models_1.UserStatus.ACTIVE) {
                throw new Error('Account is not active');
            }
            const isPasswordValid = await bcrypt.compare(loginData.password, user.password_hash);
            if (!isPasswordValid) {
                await this.usersCollection.updateOne({ _id: user._id }, {
                    $inc: { 'metadata.login_attempts': 1 },
                    $set: { updated_at: new Date() }
                });
                throw new Error('Invalid email or password');
            }
            await this.usersCollection.updateOne({ _id: user._id }, {
                $set: {
                    last_login: new Date(),
                    'metadata.login_attempts': 0,
                    updated_at: new Date()
                }
            });
            const tokenPayload = {
                sub: user._id.toString(),
                email: user.email,
                username: user.username,
                role: user.role,
            };
            const token = jwt.sign(tokenPayload, config_1.appConfig.auth.jwtSecret, {
                expiresIn: config_1.appConfig.auth.jwtExpiresIn,
            });
            const expiresAt = new Date();
            expiresAt.setDate(expiresAt.getDate() + 7);
            logger_1.logger.info(`User logged in successfully: ${loginData.email}`);
            return {
                token,
                user: (0, models_1.toUserResponse)(user),
                expires_at: expiresAt.toISOString(),
            };
        }
        catch (error) {
            logger_1.logger.error('User login error:', error);
            throw error;
        }
    }
    async getUserById(userId) {
        try {
            const user = await this.usersCollection.findOne({ _id: new mongodb_1.ObjectId(userId) });
            if (!user) {
                throw new Error('User not found');
            }
            return (0, models_1.toUserResponse)(user);
        }
        catch (error) {
            logger_1.logger.error('Get user by ID error:', error);
            throw error;
        }
    }
    async getUserByEmail(email) {
        try {
            const user = await this.usersCollection.findOne({ email });
            if (!user) {
                return null;
            }
            return (0, models_1.toUserResponse)(user);
        }
        catch (error) {
            logger_1.logger.error('Get user by email error:', error);
            throw error;
        }
    }
    async updateUserLastLogin(userId, ipAddress) {
        try {
            await this.usersCollection.updateOne({ _id: new mongodb_1.ObjectId(userId) }, {
                $set: {
                    last_login: new Date(),
                    'metadata.last_login_ip': ipAddress,
                    updated_at: new Date()
                }
            });
        }
        catch (error) {
            logger_1.logger.error('Update user last login error:', error);
            throw error;
        }
    }
    verifyToken(token) {
        try {
            return jwt.verify(token, config_1.appConfig.auth.jwtSecret);
        }
        catch (error) {
            logger_1.logger.warn('Token verification failed:', error);
            throw new Error('Invalid or expired token');
        }
    }
    async changePassword(userId, currentPassword, newPassword) {
        try {
            const user = await this.usersCollection.findOne({ _id: new mongodb_1.ObjectId(userId) });
            if (!user) {
                throw new Error('User not found');
            }
            const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password_hash);
            if (!isCurrentPasswordValid) {
                throw new Error('Current password is incorrect');
            }
            const saltRounds = 12;
            const newPasswordHash = await bcrypt.hash(newPassword, saltRounds);
            await this.usersCollection.updateOne({ _id: user._id }, {
                $set: {
                    password_hash: newPasswordHash,
                    updated_at: new Date()
                }
            });
            logger_1.logger.info(`Password changed for user: ${user.email}`);
        }
        catch (error) {
            logger_1.logger.error('Change password error:', error);
            throw error;
        }
    }
}
exports.AuthService = AuthService;
let authServiceInstance = null;
function getAuthService() {
    if (!authServiceInstance) {
        authServiceInstance = new AuthService();
    }
    return authServiceInstance;
}
//# sourceMappingURL=auth.js.map