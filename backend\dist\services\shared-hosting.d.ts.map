{"version": 3, "file": "shared-hosting.d.ts", "sourceRoot": "", "sources": ["../../src/services/shared-hosting.ts"], "names": [], "mappings": "AAsBA,qBAAa,aAAa;IACxB,OAAO,CAAC,MAAM,CAAS;IACvB,OAAO,CAAC,MAAM,CAOZ;;IA+BI,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC;IAgDxB,cAAc,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;IA8BhD,sBAAsB,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC;QAAE,MAAM,EAAE,MAAM,CAAC;QAAC,MAAM,EAAE,MAAM,CAAA;KAAE,CAAC;IA6BpF,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC;CAIlC;AAGD,MAAM,WAAW,iBAAiB;IAChC,EAAE,EAAE,MAAM,CAAC;IACX,OAAO,EAAE,MAAM,CAAC;IAChB,QAAQ,EAAE,MAAM,CAAC;IACjB,cAAc,EAAE,MAAM,CAAC;IACvB,cAAc,EAAE,MAAM,CAAC;IACvB,IAAI,EAAE,MAAM,CAAC;IACb,MAAM,EAAE,QAAQ,GAAG,WAAW,GAAG,UAAU,CAAC;IAC5C,SAAS,EAAE,MAAM,CAAC;IAClB,SAAS,EAAE,MAAM,CAAC;IAClB,IAAI,EAAE,MAAM,CAAC;IACb,QAAQ,EAAE,MAAM,CAAC;IACjB,QAAQ,EAAE,MAAM,CAAC;IACjB,eAAe,EAAE;QACf,SAAS,EAAE,MAAM,CAAC;QAClB,UAAU,EAAE,MAAM,CAAC;QACnB,eAAe,EAAE,MAAM,CAAC;QACxB,aAAa,EAAE,MAAM,CAAC;KACvB,CAAC;IACF,KAAK,EAAE;QACL,SAAS,EAAE,MAAM,CAAC;QAClB,YAAY,EAAE,MAAM,CAAC;QACrB,cAAc,EAAE,MAAM,CAAC;QACvB,YAAY,EAAE,MAAM,CAAC;KACtB,CAAC;IACF,UAAU,EAAE,MAAM,CAAC;IACnB,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,YAAY,EAAE,wBAAwB,EAAE,CAAC;CAC1C;AAED,MAAM,WAAW,wBAAwB;IACvC,EAAE,EAAE,MAAM,CAAC;IACX,IAAI,EAAE,MAAM,CAAC;IACb,IAAI,EAAE,gBAAgB,GAAG,aAAa,CAAC;IACvC,SAAS,CAAC,EAAE,MAAM,GAAG,OAAO,GAAG,KAAK,GAAG,SAAS,GAAG,QAAQ,GAAG,QAAQ,GAAG,MAAM,GAAG,KAAK,GAAG,IAAI,GAAG,MAAM,GAAG,QAAQ,GAAG,MAAM,CAAC;IAC5H,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,SAAS,EAAE,MAAM,CAAC;IAClB,SAAS,EAAE,MAAM,CAAC;IAClB,MAAM,EAAE,SAAS,GAAG,SAAS,GAAG,UAAU,GAAG,OAAO,CAAC;IACrD,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,WAAW,EAAE,OAAO,CAAC;IACrB,UAAU,EAAE,MAAM,CAAC;IACnB,aAAa,CAAC,EAAE,MAAM,CAAC;CACxB;AAED,MAAM,WAAW,uBAAuB;IACtC,OAAO,EAAE,MAAM,CAAC;IAChB,QAAQ,EAAE,MAAM,CAAC;IACjB,IAAI,EAAE,MAAM,CAAC;IACb,SAAS,CAAC,EAAE,MAAM,CAAC;CACpB;AAED,MAAM,WAAW,wBAAwB;IACvC,IAAI,EAAE,MAAM,CAAC;IACb,IAAI,EAAE,gBAAgB,GAAG,aAAa,CAAC;IACvC,SAAS,CAAC,EAAE,MAAM,GAAG,OAAO,GAAG,KAAK,GAAG,SAAS,GAAG,QAAQ,GAAG,QAAQ,GAAG,MAAM,GAAG,KAAK,GAAG,IAAI,GAAG,MAAM,GAAG,QAAQ,GAAG,MAAM,CAAC;IAC5H,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,aAAa,CAAC,EAAE,MAAM,CAAC;IACvB,aAAa,CAAC,EAAE,MAAM,CAAC;CACxB;AAED,MAAM,WAAW,iBAAiB;IAChC,IAAI,EAAE,MAAM,CAAC;IACb,IAAI,EAAE,MAAM,GAAG,WAAW,CAAC;IAC3B,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,WAAW,EAAE,MAAM,CAAC;IACpB,KAAK,EAAE,MAAM,CAAC;IACd,QAAQ,EAAE,MAAM,CAAC;IACjB,IAAI,EAAE,MAAM,CAAC;CACd;AAGD,MAAM,WAAW,mBAAmB;IAClC,EAAE,EAAE,MAAM,CAAC;IACX,UAAU,EAAE,MAAM,CAAC;IACnB,QAAQ,EAAE,MAAM,CAAC;IACjB,MAAM,EAAE,MAAM,CAAC;IACf,MAAM,EAAE,QAAQ,GAAG,aAAa,GAAG,SAAS,CAAC;IAC7C,SAAS,EAAE,MAAM,CAAC;IAClB,aAAa,EAAE,MAAM,CAAC;IACtB,SAAS,EAAE,MAAM,CAAC;IAClB,YAAY,EAAE,MAAM,CAAC;IACrB,UAAU,EAAE,MAAM,CAAC;IACnB,YAAY,EAAE,MAAM,CAAC;IACrB,SAAS,EAAE,IAAI,CAAC;IAChB,UAAU,EAAE,IAAI,CAAC;CAClB;AAGD,MAAM,WAAW,aAAa;IAC5B,OAAO,EAAE,MAAM,CAAC;IAChB,MAAM,EAAE,QAAQ,GAAG,cAAc,GAAG,WAAW,GAAG,SAAS,CAAC;IAC5D,iBAAiB,CAAC,EAAE,IAAI,CAAC;IACzB,kBAAkB,CAAC,EAAE,IAAI,CAAC;IAC1B,gBAAgB,CAAC,EAAE,IAAI,CAAC;IACxB,iBAAiB,EAAE,MAAM,CAAC;IAC1B,kBAAkB,CAAC,EAAE,IAAI,CAAC;CAC3B;AAED,cAAM,oBAAoB;IACxB,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAoC;IAC9D,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAuC;IACrE,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAuC;IACrE,OAAO,CAAC,QAAQ,CAAC,oBAAoB,CAA6C;IAGlF,OAAO,CAAC,UAAU,CAA6B;IAG/C,OAAO,CAAC,eAAe,CAAyC;;IAShE,OAAO,CAAC,4BAA4B;YAWtB,uBAAuB;YAqBvB,oBAAoB;YAyDpB,yBAAyB;YAoBzB,gBAAgB;YA8DhB,mBAAmB;IAgE3B,uBAAuB,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,GAAG,cAAc,GAAG,WAAW,GAAG,SAAS,EAAE,eAAe,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IA8C7I,4BAA4B,IAAI,OAAO,CAAC,IAAI,CAAC;IAsCnD,oBAAoB,CAAC,MAAM,EAAE,MAAM,GAAG,aAAa,GAAG,SAAS;IAK/D,mBAAmB,IAAI,mBAAmB,EAAE;IAKtC,eAAe,CAAC,YAAY,EAAE,IAAI,CAAC,mBAAmB,EAAE,eAAe,GAAG,WAAW,GAAG,cAAc,GAAG,YAAY,GAAG,cAAc,GAAG,WAAW,GAAG,YAAY,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC;IAiBpL,oBAAoB,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAkBrD,kBAAkB,IAAI,OAAO,CAAC,IAAI,CAAC;YAiC3B,kBAAkB;IA2ChC,aAAa,IAAI,IAAI;IAmBrB,YAAY,IAAI,IAAI;IASpB,+BAA+B,IAAI,IAAI;IAmBvC,8BAA8B,IAAI,IAAI;IAShC,UAAU,CAAC,QAAQ,EAAE,uBAAuB,GAAG,OAAO,CAAC,iBAAiB,CAAC;IA2M/E,OAAO,CAAC,kBAAkB;IAmC1B,OAAO,CAAC,SAAS;YAWH,qBAAqB;YAgBrB,mBAAmB;IAoBjC,OAAO,CAAC,qBAAqB;IAiBvB,qBAAqB,IAAI,OAAO,CAAC,IAAI,CAAC;YA6B9B,oBAAoB;YA6DpB,mBAAmB;IAuBjC,OAAO,CAAC,iBAAiB;IAmBzB,wBAAwB,IAAI,IAAI;IA+BhC,uBAAuB,IAAI,IAAI;YAejB,mBAAmB;IAgBjC,QAAQ,IAAI,IAAI;IAmBV,iBAAiB,IAAI,OAAO,CAAC,GAAG,CAAC;YAyFzB,eAAe;YA4Ef,mBAAmB;YAWnB,4BAA4B;YAkE5B,wBAAwB;IAWtC,OAAO,CAAC,aAAa;YAgBP,oBAAoB;YAKpB,uBAAuB;YAIvB,uBAAuB;IAKrC,OAAO,CAAC,iBAAiB;YASX,uBAAuB;YA8BvB,iBAAiB;IAiBzB,iBAAiB,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,wBAAwB,GAAG,OAAO,CAAC,wBAAwB,CAAC;YAwD/F,2BAA2B;YAc3B,kBAAkB;IA+DhC,OAAO,CAAC,gBAAgB;YAoFV,gBAAgB;YAehB,cAAc;YAed,kBAAkB;YAelB,eAAe;YAqCf,uBAAuB;YAuCvB,aAAa;YAwBb,aAAa;YAwBb,WAAW;YAwBX,UAAU;YAyBV,SAAS;YAuBT,WAAW;YAqBX,aAAa;YAwBb,WAAW;YAwBX,kBAAkB;YAoElB,kBAAkB;YAmElB,gBAAgB;YAmFhB,eAAe;YAuEf,cAAc;YAsEd,gBAAgB;YAqFhB,kBAAkB;YAiDlB,gBAAgB;IAyDxB,QAAQ,CAAC,OAAO,EAAE;QACtB,IAAI,EAAE,MAAM,CAAC;QACb,KAAK,EAAE,MAAM,CAAC;QACd,MAAM,CAAC,EAAE,MAAM,CAAC;QAChB,IAAI,CAAC,EAAE,MAAM,CAAC;QACd,SAAS,CAAC,EAAE,MAAM,CAAC;QACnB,MAAM,CAAC,EAAE,MAAM,CAAC;KACjB,GAAG,OAAO,CAAC;QACV,KAAK,EAAE,iBAAiB,EAAE,CAAC;QAC3B,KAAK,EAAE,MAAM,CAAC;QACd,IAAI,EAAE,MAAM,CAAC;QACb,KAAK,EAAE,MAAM,CAAC;QACd,UAAU,EAAE,MAAM,CAAC;KACpB,CAAC;IAoGI,OAAO,CAAC,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,iBAAiB,GAAG,IAAI,CAAC;IA2D1D,UAAU,CAAC,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE;QAC3C,IAAI,CAAC,EAAE,MAAM,CAAC;QACd,MAAM,CAAC,EAAE,QAAQ,GAAG,WAAW,GAAG,UAAU,CAAC;QAC7C,eAAe,CAAC,EAAE;YAChB,SAAS,CAAC,EAAE,MAAM,CAAC;YACnB,UAAU,CAAC,EAAE,MAAM,CAAC;YACpB,eAAe,CAAC,EAAE,MAAM,CAAC;YACzB,aAAa,CAAC,EAAE,MAAM,CAAC;SACxB,CAAC;KACH,GAAG,OAAO,CAAC,iBAAiB,CAAC;IAwCxB,UAAU,CAAC,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAkCzC,YAAY,CAAC,MAAM,EAAE,KAAK,GAAG,MAAM,GAAG,OAAO,GAAG,MAAM,EAAE,QAAQ,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC;IAgGxF,oBAAoB,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,GAAG,MAAM,GAAG,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC;IA+CpF,iBAAiB,CAAC,QAAQ,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC;IAuFlD,WAAW,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IA6B3D,cAAc,CAAC,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IA0B7C,iBAAiB,CAAC,MAAM,EAAE,MAAM,EAAE,WAAW,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC;QAAE,QAAQ,EAAE,MAAM,CAAA;KAAE,CAAC;IA+BtF,mBAAmB,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE;QACjD,IAAI,EAAE,MAAM,CAAC;QACb,KAAK,EAAE,MAAM,CAAC;QACd,MAAM,CAAC,EAAE,MAAM,CAAC;QAChB,IAAI,CAAC,EAAE,MAAM,CAAC;KACf,GAAG,OAAO,CAAC;QACV,YAAY,EAAE,GAAG,EAAE,CAAC;QACpB,KAAK,EAAE,MAAM,CAAC;QACd,IAAI,EAAE,MAAM,CAAC;QACb,KAAK,EAAE,MAAM,CAAC;QACd,UAAU,EAAE,MAAM,CAAC;KACpB,CAAC;IA8DI,iBAAiB,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE;QACjE,IAAI,CAAC,EAAE,MAAM,CAAC;QACd,MAAM,CAAC,EAAE,MAAM,CAAC;QAChB,QAAQ,CAAC,EAAE,MAAM,CAAC;QAClB,aAAa,CAAC,EAAE,MAAM,CAAC;QACvB,aAAa,CAAC,EAAE,MAAM,CAAC;QACvB,MAAM,CAAC,EAAE,SAAS,GAAG,SAAS,CAAC;KAChC,GAAG,OAAO,CAAC,GAAG,CAAC;IAwBV,iBAAiB,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;CAgCtE;AAID,wBAAgB,uBAAuB,IAAI,oBAAoB,CAK9D"}