{"version": 3, "file": "security-monitoring.js", "sourceRoot": "", "sources": ["../../src/services/security-monitoring.ts"], "names": [], "mappings": ";;AAuUA,oEAKC;AA5UD,4CAAyC;AACzC,wCAA0C;AA2C1C,MAAM,yBAAyB;IACZ,gBAAgB,GAAG,eAAe,CAAC;IACnC,MAAM,GAAoB,EAAE,CAAC;IAG9C,KAAK,CAAC,oBAAoB;QACxB,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;YAE3D,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;YACjD,MAAM,QAAQ,GAA0B,EAAE,CAAC;YAE3C,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;gBACzB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;gBACnD,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAGtB,IAAI,MAAM,CAAC,gBAAgB,KAAK,QAAQ,EAAE,CAAC;oBACzC,MAAM,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;gBACjD,CAAC;YACH,CAAC;YAED,eAAM,CAAC,IAAI,CAAC,aAAa,QAAQ,CAAC,MAAM,gCAAgC,CAAC,CAAC;YAC1E,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YACzD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAGO,KAAK,CAAC,kBAAkB,CAAC,IAAS;QACxC,IAAI,CAAC;YACH,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAC/E,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAC3E,MAAM,qBAAqB,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YACvF,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAE3E,MAAM,UAAU,GAAa,EAAE,CAAC;YAChC,IAAI,CAAC,iBAAiB;gBAAE,UAAU,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;YACxE,IAAI,CAAC,eAAe;gBAAE,UAAU,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;YACjE,IAAI,CAAC,qBAAqB;gBAAE,UAAU,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;YAE7E,IAAI,eAAe,GAAyC,QAAQ,CAAC;YACrE,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC1B,eAAe,GAAG,UAAU,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,SAAS,CAAC;YACvE,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,cAAc,EAAE,IAAI,CAAC,cAAc;gBACnC,gBAAgB,EAAE,eAAe;gBACjC,mBAAmB,EAAE,iBAAiB;gBACtC,iBAAiB,EAAE,eAAe;gBAClC,uBAAuB,EAAE,qBAAqB;gBAC9C,cAAc,EAAE,aAAa;gBAC7B,YAAY,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACtC,UAAU;aACX,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,sCAAsC,IAAI,CAAC,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;YAC5E,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAGO,KAAK,CAAC,oBAAoB,CAAC,aAAqB;QACtD,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,iBAAiB,aAAa,EAAE,CAAC;YAGjD,MAAM,YAAY,GAAG,MAAM,IAAA,gBAAS,EAAC,YAAY,IAAI,CAAC,gBAAgB,kBAAkB,OAAO,GAAG,CAAC,CAAC;YACpG,IAAI,YAAY,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,KAAK,EAAE,CAAC;gBACzC,eAAM,CAAC,IAAI,CAAC,4CAA4C,aAAa,KAAK,YAAY,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;gBACxG,OAAO,KAAK,CAAC;YACf,CAAC;YAGD,MAAM,UAAU,GAAG,MAAM,IAAA,gBAAS,EAAC,YAAY,IAAI,CAAC,gBAAgB,aAAa,aAAa,wBAAwB,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC;YAC1I,IAAI,UAAU,IAAI,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC;gBACzD,eAAM,CAAC,IAAI,CAAC,QAAQ,aAAa,oCAAoC,CAAC,CAAC;gBACvE,OAAO,KAAK,CAAC;YACf,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,wCAAwC,aAAa,GAAG,EAAE,KAAK,CAAC,CAAC;YAC9E,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAGO,KAAK,CAAC,kBAAkB,CAAC,aAAqB;QACpD,IAAI,CAAC;YAEH,MAAM,WAAW,GAAG,MAAM,IAAA,gBAAS,EAAC,YAAY,IAAI,CAAC,gBAAgB,8BAA8B,aAAa,SAAS,CAAC,CAAC;YAC3H,IAAI,WAAW,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,QAAQ,EAAE,CAAC;gBAC3C,eAAM,CAAC,IAAI,CAAC,gCAAgC,aAAa,EAAE,CAAC,CAAC;gBAC7D,OAAO,KAAK,CAAC;YACf,CAAC;YAGD,MAAM,QAAQ,GAAG,MAAM,IAAA,gBAAS,EAAC,YAAY,IAAI,CAAC,gBAAgB,yBAAyB,aAAa,iCAAiC,CAAC,CAAC;YAC3I,MAAM,WAAW,GAAG,MAAM,IAAA,gBAAS,EAAC,YAAY,IAAI,CAAC,gBAAgB,yBAAyB,aAAa,kCAAkC,CAAC,CAAC;YAG/I,OAAO,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,IAAI,WAAW,CAAC,MAAM,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;QACpG,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,sCAAsC,aAAa,GAAG,EAAE,KAAK,CAAC,CAAC;YAC5E,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAGO,KAAK,CAAC,wBAAwB,CAAC,aAAqB;QAC1D,IAAI,CAAC;YAEH,MAAM,IAAA,gBAAS,EAAC,YAAY,IAAI,CAAC,gBAAgB,mCAAmC,aAAa,GAAG,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC;YAGxH,MAAM,IAAA,gBAAS,EAAC,YAAY,IAAI,CAAC,gBAAgB,2BAA2B,aAAa,GAAG,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC;YAEhH,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,4CAA4C,aAAa,GAAG,EAAE,KAAK,CAAC,CAAC;YAClF,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAGO,KAAK,CAAC,oBAAoB,CAAC,aAAqB;QACtD,IAAI,CAAC;YAEH,MAAM,QAAQ,GAAG,MAAM,IAAA,gBAAS,EAAC,YAAY,IAAI,CAAC,gBAAgB,WAAW,aAAa,4DAA4D,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;YAGvL,MAAM,WAAW,GAAG,MAAM,IAAA,gBAAS,EAAC,YAAY,IAAI,CAAC,gBAAgB,WAAW,aAAa,4DAA4D,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;YAG1L,MAAM,SAAS,GAAG,MAAM,IAAA,gBAAS,EAAC,YAAY,IAAI,CAAC,gBAAgB,0BAA0B,aAAa,aAAa,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;YAExJ,OAAO;gBACL,WAAW,EAAE,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC;gBACpD,cAAc,EAAE,UAAU,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC;gBAC1D,aAAa,EAAE,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC;gBACrD,gBAAgB,EAAE,CAAC;aACpB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,oCAAoC,aAAa,GAAG,EAAE,KAAK,CAAC,CAAC;YAC1E,OAAO,EAAE,WAAW,EAAE,CAAC,EAAE,cAAc,EAAE,CAAC,EAAE,aAAa,EAAE,CAAC,EAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;QACtF,CAAC;IACH,CAAC;IAGO,KAAK,CAAC,qBAAqB,CAAC,IAAS,EAAE,MAA2B;QACxE,MAAM,KAAK,GAAkB;YAC3B,EAAE,EAAE,SAAS,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE;YACxE,IAAI,EAAE,MAAM,CAAC,gBAAgB,KAAK,aAAa,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,iBAAiB;YAC3F,QAAQ,EAAE,MAAM,CAAC,gBAAgB,KAAK,aAAa,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ;YACvE,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,WAAW,EAAE,kBAAkB,MAAM,CAAC,gBAAgB,WAAW;YACjE,OAAO,EAAE;gBACP,UAAU,EAAE,MAAM,CAAC,UAAU;gBAC7B,mBAAmB,EAAE,MAAM,CAAC,mBAAmB;gBAC/C,iBAAiB,EAAE,MAAM,CAAC,iBAAiB;gBAC3C,uBAAuB,EAAE,MAAM,CAAC,uBAAuB;gBACvD,cAAc,EAAE,MAAM,CAAC,cAAc;aACtC;YACD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,QAAQ,EAAE,KAAK;YACf,aAAa,EAAE,EAAE;SAClB,CAAC;QAEF,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxB,eAAM,CAAC,IAAI,CAAC,6BAA6B,KAAK,CAAC,EAAE,aAAa,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;QAG/E,IAAI,KAAK,CAAC,QAAQ,KAAK,MAAM,IAAI,KAAK,CAAC,QAAQ,KAAK,UAAU,EAAE,CAAC;YAC/D,MAAM,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAClC,CAAC;IACH,CAAC;IAGO,KAAK,CAAC,aAAa,CAAC,KAAoB;QAC9C,IAAI,CAAC;YACH,MAAM,OAAO,GAAa,EAAE,CAAC;YAE7B,IAAI,KAAK,CAAC,IAAI,KAAK,qBAAqB,IAAI,KAAK,CAAC,IAAI,KAAK,iBAAiB,EAAE,CAAC;gBAE7E,MAAM,IAAA,gBAAS,EAAC,YAAY,IAAI,CAAC,gBAAgB,6BAA6B,KAAK,CAAC,QAAQ,GAAG,CAAC,CAAC;gBACjG,MAAM,IAAA,gBAAS,EAAC,YAAY,IAAI,CAAC,gBAAgB,cAAc,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,QAAQ,kBAAkB,KAAK,CAAC,QAAQ,GAAG,CAAC,CAAC;gBACpI,OAAO,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;YACzC,CAAC;YAED,IAAI,KAAK,CAAC,IAAI,KAAK,gBAAgB,EAAE,CAAC;gBAEpC,MAAM,IAAA,gBAAS,EAAC,YAAY,IAAI,CAAC,gBAAgB,4BAA4B,KAAK,CAAC,QAAQ,SAAS,CAAC,CAAC;gBACtG,OAAO,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;YAC5C,CAAC;YAED,KAAK,CAAC,aAAa,GAAG,OAAO,CAAC;YAC9B,eAAM,CAAC,IAAI,CAAC,wCAAwC,KAAK,CAAC,EAAE,KAAK,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACzF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,kCAAkC,KAAK,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;QACrE,CAAC;IACH,CAAC;IAGO,KAAK,CAAC,qBAAqB;QACjC,IAAI,CAAC;YAEH,MAAM,QAAQ,GAAG,MAAM,IAAA,gBAAS,EAAC,YAAY,IAAI,CAAC,gBAAgB,8BAA8B,CAAC,CAAC;YAClG,MAAM,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,GAAW,EAAE,EAAE,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC;YAErG,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAW,EAAE,EAAE;gBAClC,MAAM,QAAQ,GAAG,GAAG,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;gBAC1C,OAAO;oBACL,OAAO,EAAE,QAAQ,QAAQ,EAAE;oBAC3B,QAAQ,EAAE,QAAQ;oBAClB,cAAc,EAAE,QAAQ;iBACzB,CAAC;YACJ,CAAC,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;YAC3D,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAGD,KAAK,CAAC,kBAAkB;QACtB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAC;YACnD,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC;YACzE,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAC7C,KAAK,CAAC,QAAQ;gBACd,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CACvE,CAAC,MAAM,CAAC;YAET,MAAM,WAAW,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,gBAAgB,KAAK,QAAQ,CAAC,CAAC,MAAM,CAAC;YACjF,MAAM,YAAY,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,gBAAgB,KAAK,SAAS,CAAC,CAAC,MAAM,CAAC;YACnF,MAAM,gBAAgB,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,gBAAgB,KAAK,aAAa,CAAC,CAAC,MAAM,CAAC;YAE3F,MAAM,WAAW,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;YAEtF,OAAO;gBACL,WAAW,EAAE,QAAQ,CAAC,MAAM;gBAC5B,YAAY,EAAE,WAAW;gBACzB,aAAa,EAAE,YAAY;gBAC3B,iBAAiB,EAAE,gBAAgB;gBACnC,aAAa,EAAE,YAAY;gBAC3B,mBAAmB,EAAE,WAAW;gBAChC,mBAAmB,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC;aAC7C,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YACvD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAGD,SAAS,CAAC,QAAkB;QAC1B,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;YAC3B,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC;QAClE,CAAC;QACD,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAGD,KAAK,CAAC,YAAY,CAAC,OAAe,EAAE,UAAkB;QACpD,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,CAAC;QACtD,IAAI,KAAK,EAAE,CAAC;YACV,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC;YACtB,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,sBAAsB,UAAU,EAAE,CAAC,CAAC;YAC7D,eAAM,CAAC,IAAI,CAAC,SAAS,OAAO,cAAc,UAAU,EAAE,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC;CACF;AAGD,IAAI,yBAAoD,CAAC;AAEzD,SAAgB,4BAA4B;IAC1C,IAAI,CAAC,yBAAyB,EAAE,CAAC;QAC/B,yBAAyB,GAAG,IAAI,yBAAyB,EAAE,CAAC;IAC9D,CAAC;IACD,OAAO,yBAAyB,CAAC;AACnC,CAAC"}