import { SSHConnection } from './shared-hosting';
interface DatabaseConfig {
    type: 'mysql' | 'postgresql' | 'mongodb';
    name: string;
    username: string;
    password: string;
    host: string;
    port: number;
    maxConnections?: number;
    storageLimit?: number;
}
export declare class DatabaseServicesManager {
    private mysqlRootPassword;
    constructor();
    createUserDatabase(userId: string, config: {
        type: 'mysql' | 'postgresql' | 'mongodb';
        databaseName: string;
        storageLimit?: number;
    }): Promise<DatabaseConfig>;
    createMySQLDatabase(username: string, password: string, dbName: string, storageLimit?: number): Promise<DatabaseConfig>;
    createPostgreSQLDatabase(username: string, password: string, dbName: string, storageLimit?: number): Promise<DatabaseConfig>;
    createMongoDatabase(username: string, password: string, dbName: string, storageLimit?: number): Promise<DatabaseConfig>;
    setMySQLStorageLimit(ssh: SSHConnection, dbName: string, limitMB: number): Promise<void>;
    setPostgreSQLStorageLimit(ssh: SSHConnection, dbName: string, limitMB: number): Promise<void>;
    setMongoStorageLimit(ssh: SSHConnection, dbName: string, limitMB: number): Promise<void>;
    setupDatabaseEnvironment(user: any, dbConfig: DatabaseConfig): Promise<void>;
    generateConnectionString(config: DatabaseConfig): string;
    deleteUserDatabase(userId: string, databaseName: string, type: 'mysql' | 'postgresql' | 'mongodb'): Promise<void>;
    getDatabaseUsage(userId: string): Promise<any[]>;
    private generateSecurePassword;
    backupDatabase(userId: string, databaseName: string, type: 'mysql' | 'postgresql' | 'mongodb'): Promise<string>;
}
export declare const databaseServices: DatabaseServicesManager;
export {};
//# sourceMappingURL=database-services.d.ts.map