"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.databaseServices = exports.DatabaseServicesManager = void 0;
const logger_1 = require("../utils/logger");
const config_1 = require("../config");
const shared_hosting_1 = require("../models/shared-hosting");
const shared_hosting_2 = require("./shared-hosting");
class DatabaseServicesManager {
    mysqlRootPassword;
    constructor() {
        this.mysqlRootPassword = config_1.appConfig.database.mysqlRootPassword || 'defaultpassword';
    }
    async createUserDatabase(userId, config) {
        try {
            const user = await shared_hosting_1.SharedHostingUser.findOne({ user_id: userId });
            if (!user) {
                throw new Error('User not found');
            }
            const dbUsername = `${user.linux_username}_db`;
            const dbPassword = this.generateSecurePassword();
            const dbName = `${user.linux_username}_${config.databaseName}`;
            let dbConfig;
            switch (config.type) {
                case 'mysql':
                    dbConfig = await this.createMySQLDatabase(dbUsername, dbPassword, dbName, config.storageLimit);
                    break;
                case 'postgresql':
                    dbConfig = await this.createPostgreSQLDatabase(dbUsername, dbPassword, dbName, config.storageLimit);
                    break;
                case 'mongodb':
                    dbConfig = await this.createMongoDatabase(dbUsername, dbPassword, dbName, config.storageLimit);
                    break;
                default:
                    throw new Error(`Unsupported database type: ${config.type}`);
            }
            await this.setupDatabaseEnvironment(user, dbConfig);
            logger_1.logger.info(`Created ${config.type} database ${dbName} for user ${user.username}`);
            return dbConfig;
        }
        catch (error) {
            logger_1.logger.error(`Failed to create database for user ${userId}:`, error);
            throw error;
        }
    }
    async createMySQLDatabase(username, password, dbName, storageLimit) {
        const ssh = new shared_hosting_2.SSHConnection();
        try {
            await ssh.connect();
            await ssh.executeCommand(`mysql -u root -p${this.mysqlRootPassword} -e "CREATE DATABASE IF NOT EXISTS ${dbName} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"`);
            await ssh.executeCommand(`mysql -u root -p${this.mysqlRootPassword} -e "CREATE USER IF NOT EXISTS '${username}'@'localhost' IDENTIFIED BY '${password}';"`);
            await ssh.executeCommand(`mysql -u root -p${this.mysqlRootPassword} -e "GRANT ALL PRIVILEGES ON ${dbName}.* TO '${username}'@'localhost';"`);
            if (storageLimit) {
                await this.setMySQLStorageLimit(ssh, dbName, storageLimit);
            }
            await ssh.executeCommand(`mysql -u root -p${this.mysqlRootPassword} -e "FLUSH PRIVILEGES;"`);
            const config = {
                type: 'mysql',
                name: dbName,
                username,
                password,
                host: 'localhost',
                port: 3306
            };
            if (storageLimit !== undefined) {
                config.storageLimit = storageLimit;
            }
            return config;
        }
        catch (error) {
            logger_1.logger.error(`Failed to create MySQL database ${dbName}:`, error);
            throw error;
        }
        finally {
            await ssh.disconnect();
        }
    }
    async createPostgreSQLDatabase(username, password, dbName, storageLimit) {
        const ssh = new shared_hosting_2.SSHConnection();
        try {
            await ssh.connect();
            await ssh.executeCommand(`sudo -u postgres psql -c "CREATE USER ${username} WITH PASSWORD '${password}';"`);
            await ssh.executeCommand(`sudo -u postgres createdb -O ${username} ${dbName}`);
            await ssh.executeCommand(`sudo -u postgres psql -c "ALTER USER ${username} CONNECTION LIMIT 10;"`);
            if (storageLimit) {
                await this.setPostgreSQLStorageLimit(ssh, dbName, storageLimit);
            }
            const config = {
                type: 'postgresql',
                name: dbName,
                username,
                password,
                host: 'localhost',
                port: 5432,
                maxConnections: 10
            };
            if (storageLimit !== undefined) {
                config.storageLimit = storageLimit;
            }
            return config;
        }
        catch (error) {
            logger_1.logger.error(`Failed to create PostgreSQL database ${dbName}:`, error);
            throw error;
        }
        finally {
            await ssh.disconnect();
        }
    }
    async createMongoDatabase(username, password, dbName, storageLimit) {
        const ssh = new shared_hosting_2.SSHConnection();
        try {
            await ssh.connect();
            const mongoScript = `
use ${dbName}
db.createUser({
  user: "${username}",
  pwd: "${password}",
  roles: [
    { role: "readWrite", db: "${dbName}" },
    { role: "dbAdmin", db: "${dbName}" }
  ]
})
`;
            await ssh.executeCommand(`cat > /tmp/create_mongo_user.js << 'EOF'\n${mongoScript}\nEOF`);
            await ssh.executeCommand(`mongo /tmp/create_mongo_user.js`);
            await ssh.executeCommand(`rm /tmp/create_mongo_user.js`);
            if (storageLimit) {
                await this.setMongoStorageLimit(ssh, dbName, storageLimit);
            }
            const config = {
                type: 'mongodb',
                name: dbName,
                username,
                password,
                host: 'localhost',
                port: 27017
            };
            if (storageLimit !== undefined) {
                config.storageLimit = storageLimit;
            }
            return config;
        }
        catch (error) {
            logger_1.logger.error(`Failed to create MongoDB database ${dbName}:`, error);
            throw error;
        }
        finally {
            await ssh.disconnect();
        }
    }
    async setMySQLStorageLimit(ssh, dbName, limitMB) {
        try {
            const triggerSQL = `
CREATE TRIGGER IF NOT EXISTS ${dbName}_size_check
BEFORE INSERT ON information_schema.tables
FOR EACH ROW
BEGIN
  DECLARE db_size BIGINT;
  SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 1) INTO db_size
  FROM information_schema.tables
  WHERE table_schema = '${dbName}';
  
  IF db_size > ${limitMB} THEN
    SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Database size limit exceeded';
  END IF;
END;
`;
            await ssh.executeCommand(`mysql -u root -p${this.mysqlRootPassword} -e "${triggerSQL}"`);
        }
        catch (error) {
            logger_1.logger.error(`Failed to set MySQL storage limit for ${dbName}:`, error);
        }
    }
    async setPostgreSQLStorageLimit(ssh, dbName, limitMB) {
        try {
            await ssh.executeCommand(`sudo -u postgres psql -d ${dbName} -c "CREATE TABLESPACE ${dbName}_space LOCATION '/var/lib/postgresql/tablespaces/${dbName}';"`);
            await ssh.executeCommand(`mkdir -p /var/lib/postgresql/tablespaces/${dbName}`);
            await ssh.executeCommand(`chown postgres:postgres /var/lib/postgresql/tablespaces/${dbName}`);
            await ssh.executeCommand(`setquota -u postgres ${limitMB * 1024} ${limitMB * 1024} 0 0 /var/lib/postgresql/tablespaces/${dbName}`);
        }
        catch (error) {
            logger_1.logger.error(`Failed to set PostgreSQL storage limit for ${dbName}:`, error);
        }
    }
    async setMongoStorageLimit(ssh, dbName, limitMB) {
        try {
            const mongoDataPath = `/var/lib/mongodb/${dbName}`;
            await ssh.executeCommand(`mkdir -p ${mongoDataPath}`);
            await ssh.executeCommand(`chown mongodb:mongodb ${mongoDataPath}`);
            await ssh.executeCommand(`setquota -u mongodb ${limitMB * 1024} ${limitMB * 1024} 0 0 ${mongoDataPath}`);
        }
        catch (error) {
            logger_1.logger.error(`Failed to set MongoDB storage limit for ${dbName}:`, error);
        }
    }
    async setupDatabaseEnvironment(user, dbConfig) {
        const ssh = new shared_hosting_2.SSHConnection();
        try {
            await ssh.connect();
            const envVars = `
# Database Configuration
DB_TYPE=${dbConfig.type}
DB_HOST=${dbConfig.host}
DB_PORT=${dbConfig.port}
DB_NAME=${dbConfig.name}
DB_USERNAME=${dbConfig.username}
DB_PASSWORD=${dbConfig.password}
DATABASE_URL=${this.generateConnectionString(dbConfig)}
`;
            await ssh.executeCommand(`echo '${envVars}' >> ${user.home_directory}/.bashrc`);
            await ssh.executeCommand(`echo '${envVars}' > ${user.home_directory}/.env`);
            await ssh.executeCommand(`chown ${user.linux_username}:${user.linux_username} ${user.home_directory}/.env`);
            await ssh.executeCommand(`chmod 600 ${user.home_directory}/.env`);
        }
        catch (error) {
            logger_1.logger.error(`Failed to setup database environment for user ${user.username}:`, error);
            throw error;
        }
        finally {
            await ssh.disconnect();
        }
    }
    generateConnectionString(config) {
        switch (config.type) {
            case 'mysql':
                return `mysql://${config.username}:${config.password}@${config.host}:${config.port}/${config.name}`;
            case 'postgresql':
                return `postgresql://${config.username}:${config.password}@${config.host}:${config.port}/${config.name}`;
            case 'mongodb':
                return `mongodb://${config.username}:${config.password}@${config.host}:${config.port}/${config.name}`;
            default:
                throw new Error(`Unsupported database type: ${config.type}`);
        }
    }
    async deleteUserDatabase(userId, databaseName, type) {
        const ssh = new shared_hosting_2.SSHConnection();
        try {
            await ssh.connect();
            const user = await shared_hosting_1.SharedHostingUser.findOne({ user_id: userId });
            if (!user) {
                throw new Error('User not found');
            }
            const dbUsername = `${user.linux_username}_db`;
            const dbName = `${user.linux_username}_${databaseName}`;
            switch (type) {
                case 'mysql':
                    await ssh.executeCommand(`mysql -u root -p${this.mysqlRootPassword} -e "DROP DATABASE IF EXISTS ${dbName};"`);
                    await ssh.executeCommand(`mysql -u root -p${this.mysqlRootPassword} -e "DROP USER IF EXISTS '${dbUsername}'@'localhost';"`);
                    break;
                case 'postgresql':
                    await ssh.executeCommand(`sudo -u postgres dropdb ${dbName}`);
                    await ssh.executeCommand(`sudo -u postgres psql -c "DROP USER IF EXISTS ${dbUsername};"`);
                    break;
                case 'mongodb':
                    await ssh.executeCommand(`mongo ${dbName} --eval "db.dropUser('${dbUsername}')"`);
                    await ssh.executeCommand(`mongo ${dbName} --eval "db.dropDatabase()"`);
                    break;
            }
            logger_1.logger.info(`Deleted ${type} database ${dbName} for user ${user.username}`);
        }
        catch (error) {
            logger_1.logger.error(`Failed to delete database ${databaseName} for user ${userId}:`, error);
            throw error;
        }
        finally {
            await ssh.disconnect();
        }
    }
    async getDatabaseUsage(userId) {
        const ssh = new shared_hosting_2.SSHConnection();
        try {
            await ssh.connect();
            const user = await shared_hosting_1.SharedHostingUser.findOne({ user_id: userId });
            if (!user) {
                throw new Error('User not found');
            }
            const usage = [];
            const mysqlDbs = await ssh.executeCommand(`mysql -u root -p${this.mysqlRootPassword} -e "SELECT table_schema as 'database', ROUND(SUM(data_length + index_length) / 1024 / 1024, 1) AS 'size_mb' FROM information_schema.tables WHERE table_schema LIKE '${user.linux_username}_%' GROUP BY table_schema;" --skip-column-names`);
            if (mysqlDbs.trim()) {
                const mysqlLines = mysqlDbs.trim().split('\n');
                for (const line of mysqlLines) {
                    const [dbName, sizeMB] = line.split('\t');
                    usage.push({
                        type: 'mysql',
                        name: dbName,
                        sizeMB: parseFloat(sizeMB || '0') || 0
                    });
                }
            }
            const pgDbs = await ssh.executeCommand(`sudo -u postgres psql -c "SELECT datname, pg_size_pretty(pg_database_size(datname)) FROM pg_database WHERE datname LIKE '${user.linux_username}_%';" --tuples-only --no-align --field-separator='|'`);
            if (pgDbs.trim()) {
                const pgLines = pgDbs.trim().split('\n');
                for (const line of pgLines) {
                    const [dbName, sizeStr] = line.split('|');
                    usage.push({
                        type: 'postgresql',
                        name: dbName,
                        size: sizeStr
                    });
                }
            }
            return usage;
        }
        catch (error) {
            logger_1.logger.error(`Failed to get database usage for user ${userId}:`, error);
            throw error;
        }
        finally {
            await ssh.disconnect();
        }
    }
    generateSecurePassword(length = 16) {
        const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
        let password = '';
        for (let i = 0; i < length; i++) {
            password += charset.charAt(Math.floor(Math.random() * charset.length));
        }
        return password;
    }
    async backupDatabase(userId, databaseName, type) {
        const ssh = new shared_hosting_2.SSHConnection();
        try {
            await ssh.connect();
            const user = await shared_hosting_1.SharedHostingUser.findOne({ user_id: userId });
            if (!user) {
                throw new Error('User not found');
            }
            const dbName = `${user.linux_username}_${databaseName}`;
            const backupDir = `${user.home_directory}/backups`;
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const backupFile = `${backupDir}/${dbName}_${timestamp}.sql`;
            await ssh.executeCommand(`mkdir -p ${backupDir}`);
            switch (type) {
                case 'mysql':
                    await ssh.executeCommand(`mysqldump -u root -p${this.mysqlRootPassword} ${dbName} > ${backupFile}`);
                    break;
                case 'postgresql':
                    await ssh.executeCommand(`sudo -u postgres pg_dump ${dbName} > ${backupFile}`);
                    break;
                case 'mongodb':
                    await ssh.executeCommand(`mongodump --db ${dbName} --out ${backupDir}/${dbName}_${timestamp}`);
                    break;
            }
            await ssh.executeCommand(`chown -R ${user.linux_username}:${user.linux_username} ${backupDir}`);
            logger_1.logger.info(`Created backup for ${type} database ${dbName}: ${backupFile}`);
            return backupFile;
        }
        catch (error) {
            logger_1.logger.error(`Failed to backup database ${databaseName} for user ${userId}:`, error);
            throw error;
        }
        finally {
            await ssh.disconnect();
        }
    }
}
exports.DatabaseServicesManager = DatabaseServicesManager;
exports.databaseServices = new DatabaseServicesManager();
//# sourceMappingURL=database-services.js.map