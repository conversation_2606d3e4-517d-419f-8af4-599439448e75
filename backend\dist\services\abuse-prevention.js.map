{"version": 3, "file": "abuse-prevention.js", "sourceRoot": "", "sources": ["../../src/services/abuse-prevention.ts"], "names": [], "mappings": ";;;AAAA,4CAAyC;AACzC,sCAAsC;AACtC,6DAA0H;AAC1H,qDAAiD;AAoBjD,MAAa,sBAAsB;IACzB,kBAAkB,GAA0B,IAAI,CAAC;IACjD,YAAY,GAAG,KAAK,CAAC;IAErB,UAAU,GAAoB;QACpC,GAAG,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE;QAC/C,MAAM,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE;QAClD,SAAS,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE;QACrD,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE;QACnD,QAAQ,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE;KAC3D,CAAC;IAEF;QACE,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IAGD,eAAe;QACb,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC5B,aAAa,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACzC,CAAC;QAED,MAAM,UAAU,GAAG,kBAAS,CAAC,QAAQ,CAAC,yBAAyB,GAAG,EAAE,GAAG,IAAI,CAAC;QAE5E,IAAI,CAAC,kBAAkB,GAAG,WAAW,CAAC,KAAK,IAAI,EAAE;YAC/C,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;gBACvB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;gBACzB,IAAI,CAAC;oBACH,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;gBAC7B,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,eAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;gBAClD,CAAC;wBAAS,CAAC;oBACT,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;gBAC5B,CAAC;YACH,CAAC;QACH,CAAC,EAAE,UAAU,CAAC,CAAC;QAEf,eAAM,CAAC,IAAI,CAAC,4CAA4C,kBAAS,CAAC,QAAQ,CAAC,yBAAyB,cAAc,CAAC,CAAC;IACtH,CAAC;IAGD,cAAc;QACZ,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC5B,aAAa,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YACvC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QACjC,CAAC;QACD,eAAM,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;IACrD,CAAC;IAGD,KAAK,CAAC,aAAa;QACjB,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,MAAM,kCAAiB,CAAC,IAAI,CAAC;gBAC/C,MAAM,EAAE,QAAQ;gBAChB,SAAS,EAAE,kBAAS,CAAC,GAAG,CAAC,IAAI;aAC9B,CAAC,CAAC;YAEH,eAAM,CAAC,KAAK,CAAC,YAAY,WAAW,CAAC,MAAM,kBAAkB,CAAC,CAAC;YAE/D,KAAK,MAAM,IAAI,IAAI,WAAW,EAAE,CAAC;gBAC/B,IAAI,CAAC;oBACH,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;gBAClC,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,eAAM,CAAC,KAAK,CAAC,kCAAkC,IAAI,CAAC,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;gBAC1E,CAAC;YACH,CAAC;YAGD,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAEhC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YAClD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAGD,KAAK,CAAC,cAAc,CAAC,IAAS;QAC5B,MAAM,MAAM,GAAiB,EAAE,CAAC;QAGhC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC;QAGpC,MAAM,UAAU,GAAG,CAAC,KAAK,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC,GAAG,GAAG,CAAC;QAC9D,IAAI,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;YAC9C,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC;QACzG,CAAC;aAAM,IAAI,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;YACtD,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC3G,CAAC;aAAM,IAAI,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;YACrD,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC;QACzG,CAAC;QAGD,MAAM,aAAa,GAAG,CAAC,KAAK,CAAC,YAAY,GAAG,MAAM,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC;QACrE,IAAI,aAAa,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;YACpD,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,aAAa,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;QAClH,CAAC;aAAM,IAAI,aAAa,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;YAC5D,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,aAAa,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;QACpH,CAAC;aAAM,IAAI,aAAa,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;YAC3D,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,aAAa,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;QAClH,CAAC;QAGD,MAAM,gBAAgB,GAAG,CAAC,KAAK,CAAC,cAAc,GAAG,MAAM,CAAC,eAAe,CAAC,GAAG,GAAG,CAAC;QAC/E,IAAI,gBAAgB,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;YAC1D,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,gBAAgB,EAAE,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;QAC3H,CAAC;aAAM,IAAI,gBAAgB,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;YAClE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,EAAE,WAAW,EAAE,UAAU,EAAE,gBAAgB,EAAE,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC7H,CAAC;aAAM,IAAI,gBAAgB,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;YACjE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,gBAAgB,EAAE,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;QAC3H,CAAC;QAGD,MAAM,cAAc,GAAG,CAAC,KAAK,CAAC,YAAY,GAAG,MAAM,CAAC,aAAa,CAAC,GAAG,GAAG,CAAC;QACzE,IAAI,cAAc,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;YACtD,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,cAAc,EAAE,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC;QACrH,CAAC;aAAM,IAAI,cAAc,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;YAC9D,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,cAAc,EAAE,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;QACvH,CAAC;aAAM,IAAI,cAAc,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;YAC7D,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,cAAc,EAAE,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC;QACrH,CAAC;QAGD,MAAM,eAAe,GAAG,MAAM,mCAAkB,CAAC,OAAO,CAAC;YACvD,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,IAAI,EAAE,EAAE,IAAI,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,EAAE;SACtD,CAAC,CAAC;QAEH,IAAI,eAAe,EAAE,CAAC;YACpB,MAAM,iBAAiB,GAAG,eAAe,CAAC,cAAc,GAAG,EAAE,CAAC;YAC9D,IAAI,iBAAiB,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;gBAC1D,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,iBAAiB,EAAE,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;YAC1H,CAAC;iBAAM,IAAI,iBAAiB,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;gBAClE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,iBAAiB,EAAE,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;YAC5H,CAAC;iBAAM,IAAI,iBAAiB,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;gBACjE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,iBAAiB,EAAE,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;YAC1H,CAAC;QACH,CAAC;QAGD,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YAC3B,MAAM,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QACjC,CAAC;IACH,CAAC;IAGD,KAAK,CAAC,gBAAgB;QACpB,IAAI,CAAC;YACH,MAAM,aAAa,GAAG,MAAM,8BAAa,CAAC,OAAO,CAAC;gBAChD,SAAS,EAAE,kBAAS,CAAC,GAAG,CAAC,IAAI;aAC9B,CAAC,CAAC,IAAI,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;YAE3B,IAAI,CAAC,aAAa,EAAE,CAAC;gBACnB,OAAO;YACT,CAAC;YAGD,IAAI,aAAa,CAAC,SAAS,GAAG,EAAE,EAAE,CAAC;gBACjC,MAAM,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,aAAa,CAAC,SAAS,CAAC,CAAC;YAClE,CAAC;YAGD,IAAI,aAAa,CAAC,YAAY,GAAG,EAAE,EAAE,CAAC;gBACpC,MAAM,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,aAAa,CAAC,YAAY,CAAC,CAAC;YACxE,CAAC;YAGD,IAAI,aAAa,CAAC,UAAU,GAAG,EAAE,EAAE,CAAC;gBAClC,MAAM,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,aAAa,CAAC,UAAU,CAAC,CAAC;YACpE,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;IAGO,WAAW,CAAC,MAAc,EAAE,IAAS,EAAE,QAAa,EAAE,KAAa,EAAE,SAAiB;QAC5F,OAAO;YACL,MAAM;YACN,IAAI;YACJ,QAAQ;YACR,KAAK;YACL,SAAS;YACT,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,MAAM,EAAE,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC;SAC5C,CAAC;IACJ,CAAC;IAGO,oBAAoB,CAAC,QAAgB;QAC3C,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,SAAS;gBACZ,OAAO,gBAAgB,CAAC;YAC1B,KAAK,UAAU;gBACb,OAAO,oBAAoB,CAAC;YAC9B,KAAK,SAAS;gBACZ,OAAO,cAAc,CAAC;YACxB;gBACE,OAAO,UAAU,CAAC;QACtB,CAAC;IACH,CAAC;IAGD,KAAK,CAAC,YAAY,CAAC,KAAiB;QAClC,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,2BAA2B,KAAK,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,aAAa,KAAK,CAAC,SAAS,aAAa,CAAC,CAAC;YAE7I,QAAQ,KAAK,CAAC,MAAM,EAAE,CAAC;gBACrB,KAAK,gBAAgB;oBACnB,MAAM,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;oBAC/B,MAAM;gBACR,KAAK,oBAAoB;oBACvB,MAAM,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;oBACxC,MAAM;gBACR,KAAK,cAAc;oBACjB,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;oBAC9B,MAAM;gBACR;oBACE,eAAM,CAAC,IAAI,CAAC,yBAAyB,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;YACzD,CAAC;QAKH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,oCAAoC,KAAK,CAAC,MAAM,GAAG,EAAE,KAAK,CAAC,CAAC;QAC3E,CAAC;IACH,CAAC;IAGD,KAAK,CAAC,YAAY,CAAC,KAAiB;QAClC,eAAM,CAAC,IAAI,CAAC,iBAAiB,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,IAAI,aAAa,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAIjG,CAAC;IAGD,KAAK,CAAC,qBAAqB,CAAC,KAAiB;QAC3C,MAAM,GAAG,GAAG,IAAI,8BAAa,EAAE,CAAC;QAEhC,IAAI,CAAC;YACH,MAAM,GAAG,CAAC,OAAO,EAAE,CAAC;YAEpB,MAAM,IAAI,GAAG,MAAM,kCAAiB,CAAC,OAAO,CAAC,EAAE,OAAO,EAAE,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;YACxE,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,OAAO;YACT,CAAC;YAED,eAAM,CAAC,IAAI,CAAC,oBAAoB,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,IAAI,aAAa,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YAGlG,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;gBACnB,KAAK,KAAK;oBACR,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;oBACtC,MAAM;gBACR,KAAK,QAAQ;oBACX,MAAM,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;oBACzC,MAAM;gBACR,KAAK,WAAW;oBACd,MAAM,IAAI,CAAC,iBAAiB,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;oBAC5C,MAAM;gBACR,KAAK,UAAU;oBACb,MAAM,IAAI,CAAC,gBAAgB,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;oBACvC,MAAM;YACV,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,2BAA2B,KAAK,CAAC,MAAM,GAAG,EAAE,KAAK,CAAC,CAAC;QAClE,CAAC;gBAAS,CAAC;YACT,MAAM,GAAG,CAAC,UAAU,EAAE,CAAC;QACzB,CAAC;IACH,CAAC;IAGD,KAAK,CAAC,WAAW,CAAC,KAAiB;QACjC,IAAI,CAAC;YACH,eAAM,CAAC,KAAK,CAAC,oBAAoB,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,IAAI,aAAa,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YAGnG,MAAM,kCAAiB,CAAC,gBAAgB,CACtC,EAAE,OAAO,EAAE,KAAK,CAAC,MAAM,EAAE,EACzB;gBACE,MAAM,EAAE,WAAW;gBACnB,iBAAiB,EAAE,mBAAmB,KAAK,CAAC,IAAI,mBAAmB,KAAK,CAAC,SAAS,GAAG;gBACrF,YAAY,EAAE,IAAI,IAAI,EAAE;aACzB,CACF,CAAC;YAGF,MAAM,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QAE5C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,0BAA0B,KAAK,CAAC,MAAM,GAAG,EAAE,KAAK,CAAC,CAAC;QACjE,CAAC;IACH,CAAC;IAGD,KAAK,CAAC,WAAW,CAAC,GAAkB,EAAE,IAAS,EAAE,UAAkB;QACjE,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,SAAS,GAAG,CAAC,UAAU,GAAG,GAAG,CAAC,CAAC,CAAC;QACjF,MAAM,GAAG,CAAC,cAAc,CAAC,+BAA+B,IAAI,CAAC,cAAc,mBAAmB,QAAQ,GAAG,CAAC,CAAC;IAC7G,CAAC;IAGD,KAAK,CAAC,cAAc,CAAC,GAAkB,EAAE,IAAS,EAAE,UAAkB;QACpE,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,UAAU,GAAG,CAAC,UAAU,GAAG,GAAG,CAAC,CAAC,CAAC;QAClF,MAAM,GAAG,CAAC,cAAc,CAAC,+BAA+B,IAAI,CAAC,cAAc,oBAAoB,QAAQ,GAAG,CAAC,CAAC;IAC9G,CAAC;IAGD,KAAK,CAAC,iBAAiB,CAAC,GAAkB,EAAE,IAAS,EAAE,UAAkB;QACvE,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,eAAe,GAAG,CAAC,UAAU,GAAG,GAAG,CAAC,CAAC,CAAC;QACvF,MAAM,GAAG,CAAC,cAAc,CAAC,gDAAgD,IAAI,CAAC,IAAI,aAAa,QAAQ,MAAM,CAAC,CAAC;IACjH,CAAC;IAGD,KAAK,CAAC,gBAAgB,CAAC,GAAkB,EAAE,IAAS;QAElD,MAAM,GAAG,CAAC,cAAc,CAAC,oCAAoC,IAAI,CAAC,IAAI,oCAAoC,CAAC,CAAC;IAC9G,CAAC;IAGD,KAAK,CAAC,gBAAgB,CAAC,MAAc;QACnC,MAAM,GAAG,GAAG,IAAI,8BAAa,EAAE,CAAC;QAEhC,IAAI,CAAC;YACH,MAAM,GAAG,CAAC,OAAO,EAAE,CAAC;YAEpB,MAAM,IAAI,GAAG,MAAM,kCAAiB,CAAC,OAAO,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC;YAClE,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,OAAO;YACT,CAAC;YAGD,MAAM,GAAG,CAAC,cAAc,CAAC,uBAAuB,IAAI,CAAC,cAAc,QAAQ,CAAC,CAAC;YAG7E,MAAM,YAAY,GAAG,MAAM,yCAAwB,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC;YAC9E,KAAK,MAAM,GAAG,IAAI,YAAY,EAAE,CAAC;gBAC/B,IAAI,GAAG,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;oBAC7B,MAAM,GAAG,CAAC,cAAc,CAAC,kBAAkB,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;oBAC9E,MAAM,yCAAwB,CAAC,gBAAgB,CAC7C,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,EAChB,EAAE,MAAM,EAAE,SAAS,EAAE,CACtB,CAAC;gBACJ,CAAC;YACH,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,oCAAoC,MAAM,GAAG,EAAE,KAAK,CAAC,CAAC;QACrE,CAAC;gBAAS,CAAC;YACT,MAAM,GAAG,CAAC,UAAU,EAAE,CAAC;QACzB,CAAC;IACH,CAAC;IAGD,KAAK,CAAC,oBAAoB,CAAC,IAAY,EAAE,KAAa;QACpD,eAAM,CAAC,KAAK,CAAC,oBAAoB,IAAI,aAAa,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAGvE,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;QAG7D,KAAK,MAAM,IAAI,IAAI,QAAQ,EAAE,CAAC;YAC5B,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,EAAE,IAAW,EAAE,UAAU,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;YACjF,MAAM,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;QAC1C,CAAC;IACH,CAAC;IAGD,KAAK,CAAC,uBAAuB,CAAC,IAAY,EAAE,KAAa;QACvD,MAAM,SAAS,GAAG,IAAI,KAAK,KAAK,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC;YACrC,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC;gBAC1C,oBAAoB,CAAC;QAEtC,OAAO,MAAM,kCAAiB,CAAC,IAAI,CAAC;YAClC,MAAM,EAAE,QAAQ;YAChB,SAAS,EAAE,kBAAS,CAAC,GAAG,CAAC,IAAI;SAC9B,CAAC;aACD,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;aACzB,KAAK,CAAC,KAAK,CAAC,CAAC;IAChB,CAAC;IAGD,KAAK,CAAC,aAAa;QAEjB,OAAO;YACL,WAAW,EAAE,CAAC;YACd,cAAc,EAAE,MAAM,kCAAiB,CAAC,cAAc,CAAC,EAAE,MAAM,EAAE,WAAW,EAAE,CAAC;YAC/E,cAAc,EAAE,CAAC;YACjB,eAAe,EAAE,CAAC;SACnB,CAAC;IACJ,CAAC;CACF;AA5YD,wDA4YC"}