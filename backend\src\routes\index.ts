import { FastifyInstance } from 'fastify';
import { authRoutes } from './auth.routes';
import { vultrRoutes } from './vultr.routes';
import { serverRoutes } from './server.routes';
import { domainRoutes } from './domain.routes';
import { ResponseHelper } from '../utils/response';

export async function registerRoutes(fastify: FastifyInstance): Promise<void> {
  try {
    console.log('Registering health check route...');
    // Health check route
    fastify.get('/health', async (_request, reply) => {
      return ResponseHelper.success(reply, {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        version: process.env['npm_package_version'] || '1.0.0',
      });
    });

    console.log('Registering API v1 routes...');
    // API version prefix
    fastify.register(async function (fastify) {
      console.log('Registering auth routes...');
      await fastify.register(authRoutes, { prefix: '/auth' });

      console.log('Registering vultr routes...');
      await fastify.register(vultrRoutes, { prefix: '/compute' }); // Hide Vultr from routes

      console.log('Registering server routes...');
      // UNIFIED ROUTES: All server, hosting, and application routes are now in serverRoutes
      // This includes:
      // - Server management (dedicated/enterprise provisioning)
      // - Hosting plans and shared hosting management
      // - Application management for all tiers
      await fastify.register(serverRoutes, { prefix: '/services' });

      console.log('Registering domain routes...');
      // Domain management routes
      await fastify.register(domainRoutes, { prefix: '/domains' });
      console.log('Domain routes registered successfully');
    }, { prefix: '/api/v1' });

    console.log('Setting up 404 handler...');
    // 404 handler for unmatched routes
    fastify.setNotFoundHandler(async (request, reply) => {
      return ResponseHelper.notFound(reply, `Route ${request.method} ${request.url} not found`);
    });

    console.log('All routes registered successfully');
  } catch (error) {
    console.error('Error registering routes:', error);
    throw error;
  }
}
