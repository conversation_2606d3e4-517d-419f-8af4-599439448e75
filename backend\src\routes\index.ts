import { FastifyInstance } from 'fastify';
import { authRoutes } from './auth.routes';
import { vultrRoutes } from './vultr.routes';
import { serverRoutes } from './server.routes';
import { domainRoutes } from './domain.routes';
import { ResponseHelper } from '../utils/response';

export async function registerRoutes(fastify: FastifyInstance): Promise<void> {
  // Health check route
  fastify.get('/health', async (_request, reply) => {
    return ResponseHelper.success(reply, {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: process.env['npm_package_version'] || '1.0.0',
    });
  });

  // API version prefix
  fastify.register(async function (fastify) {
    // Register route modules with v1 prefix
    await fastify.register(authRoutes, { prefix: '/auth' });
    await fastify.register(vultrRoutes, { prefix: '/compute' }); // Hide Vultr from routes

    // UNIFIED ROUTES: All server, hosting, and application routes are now in serverRoutes
    // This includes:
    // - Server management (dedicated/enterprise provisioning)
    // - Hosting plans and shared hosting management
    // - Application management for all tiers
    await fastify.register(serverRoutes, { prefix: '/services' });

    // Domain management routes
    await fastify.register(domainRoutes, { prefix: '/domains' });
  }, { prefix: '/api/v1' });

  // 404 handler for unmatched routes
  fastify.setNotFoundHandler(async (request, reply) => {
    return ResponseHelper.notFound(reply, `Route ${request.method} ${request.url} not found`);
  });
}
