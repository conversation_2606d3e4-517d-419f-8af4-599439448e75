# 🏠 SHARED HOSTING USER CREATION - COMPLETE GUIDE

## 📋 **OVERVIEW**
This guide shows you how to create shared hosting users on the Achidas platform using the unified API. The system now uses Vultr API for proper infrastructure provisioning.

## 🔧 **PREREQUISITES**

### **1. Environment Setup**
Ensure your `.env` file has the required Vultr API configuration:

```bash
# Vultr API Configuration
VULTR_API_KEY=your_vultr_api_key_here
VULTR_SHARED_SERVER_IP=*************

# Database
DATABASE_URL=mongodb://localhost:27017/achidas

# JWT
JWT_SECRET=your_jwt_secret_here
```

### **2. Get Vultr API Key**
1. Login to [Vultr Account](https://my.vultr.com)
2. Go to Account → API → Generate API Key
3. Copy the API key to your `.env` file

### **3. Authentication Token**
Get a JWT token by logging in:

```bash
curl -X POST "http://localhost:3000/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "your-password"
  }'
```

## 🚀 **UNIFIED API ENDPOINT**

### **Base URL**
```
POST /api/v1/services/hosting/shared/users
```

### **Request Headers**
```
Authorization: Bearer your-jwt-token
Content-Type: application/json
```

### **Request Body Schema**
```json
{
  "user_id": "string (required)",     // Unique user identifier
  "username": "string (required)",    // Username for the hosting account
  "plan": "string (required)",        // Hosting plan: free, starter, basic, standard, pro
  "server_id": "string (optional)"    // Specific server ID (auto-assigned if not provided)
}
```

## 📊 **AVAILABLE SHARED HOSTING PLANS**

| Plan | CPU | RAM | Storage | Bandwidth | Price/Month | African Pricing |
|------|-----|-----|---------|-----------|-------------|-----------------|
| **free** | 2% | 128MB | 1GB | 5GB | $0 | Free |
| **starter** | 5% | 256MB | 5GB | 25GB | $2.16 | ₦3,500 / R40 / KSh280 / GH₵26 |
| **basic** | 10% | 512MB | 10GB | 50GB | $5.00 | ₦8,100 / R92 / KSh650 / GH₵60 |
| **standard** | 15% | 1GB | 20GB | 100GB | $10.00 | ₦16,200 / R185 / KSh1,300 / GH₵120 |
| **pro** | 25% | 2GB | 50GB | 250GB | $15.00 | ₦24,300 / R277 / KSh1,950 / GH₵180 |

## 🎯 **COMPLETE EXAMPLES**

### **Example 1: Create Free Plan User**
```bash
curl -X POST "http://localhost:3000/api/v1/services/hosting/shared/users" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "user_12345",
    "username": "johndoe",
    "plan": "free"
  }'
```

### **Example 2: Create Starter Plan User**
```bash
curl -X POST "http://localhost:3000/api/v1/services/hosting/shared/users" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "user_67890",
    "username": "janesmithbiz",
    "plan": "starter"
  }'
```

### **Example 3: Create Pro Plan User with Custom Server**
```bash
curl -X POST "http://localhost:3000/api/v1/services/hosting/shared/users" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "user_11111",
    "username": "enterprise-app",
    "plan": "pro",
    "server_id": "vultr-jnb-shared-01"
  }'
```

## ✅ **SUCCESS RESPONSE**
```json
{
  "success": true,
  "data": {
    "id": "shared_1703123456789_abc123def",
    "user_id": "user_12345",
    "username": "johndoe",
    "linux_username": "user_johndoe",
    "home_directory": "/var/www/user_johndoe",
    "plan": "free",
    "status": "active",
    "server_id": "vultr-jnb-shared-01",
    "server_ip": "*************",
    "port": 3001,
    "ssh_port": 2201,
    "ftp_port": 2101,
    "resource_limits": {
      "cpu_quota": 2,
      "memory_max": 128,
      "bandwidth_limit": 5,
      "storage_limit": 1
    },
    "usage": {
      "cpu_usage": 0,
      "memory_usage": 0,
      "bandwidth_used": 0,
      "storage_used": 0
    },
    "created_at": "2024-12-21T10:30:45.789Z",
    "applications": []
  },
  "meta": {
    "timestamp": "2024-12-21T10:30:45.789Z",
    "request_id": "req_abc123def456",
    "trace_id": "trace_789xyz123",
    "version": "1.0.0",
    "status_code": 201
  }
}
```

## ❌ **ERROR RESPONSES**

### **400 - Validation Error**
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Missing required fields: user_id, username, plan"
  }
}
```

### **401 - Unauthorized**
```json
{
  "success": false,
  "error": {
    "code": "UNAUTHORIZED",
    "message": "Authentication required"
  }
}
```

### **500 - Internal Server Error**
```json
{
  "success": false,
  "error": {
    "code": "INTERNAL_ERROR",
    "message": "Failed to create shared hosting user"
  }
}
```

## 🔧 **TROUBLESHOOTING**

### **Issue: 500 Internal Server Error**

**Most Common Causes:**
1. **Missing Vultr API Key**: Check your `.env` file
2. **Invalid Vultr API Key**: Verify the key is correct
3. **Network Issues**: Backend can't reach Vultr API
4. **Rate Limiting**: Too many requests (30/second limit)

**Solutions:**

#### **1. Verify Vultr API Key**
```bash
# Test your Vultr API key
curl "https://api.vultr.com/v2/account" \
  -H "Authorization: Bearer YOUR_VULTR_API_KEY"
```

#### **2. Check Environment Variables**
```bash
# In your backend directory
cat .env | grep VULTR
```

#### **3. Test Vultr API Connection**
```bash
# Test creating a simple instance (will be charged!)
curl "https://api.vultr.com/v2/instances" \
  -X POST \
  -H "Authorization: Bearer YOUR_VULTR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "region": "jnb",
    "plan": "vc2-1c-1gb",
    "os_id": 387,
    "hostname": "test-instance"
  }'
```

#### **4. Check Backend Logs**
```bash
# Check backend logs for detailed error
tail -f backend/logs/achidas.log

# Or check console output
npm start
```

### **Issue: Authentication Required**
Make sure you're including the JWT token:
```bash
# Get token first
TOKEN=$(curl -s -X POST "http://localhost:3000/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email":"your-email","password":"your-password"}' | \
  jq -r '.data.token')

# Use token in request
curl -X POST "http://localhost:3000/api/v1/services/hosting/shared/users" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"user_id":"test123","username":"testuser","plan":"free"}'
```

### **Issue: Invalid Plan**
Valid plan names:
- `free` - Free tier with basic resources
- `starter` - Entry-level paid plan
- `basic` - Small business plan
- `standard` - Growing business plan
- `pro` - High-traffic plan

## 🏗️ **WHAT HAPPENS WHEN YOU CREATE A USER**

### **Backend Process:**
1. **Validates Request**: Checks required fields and authentication
2. **Calls Vultr API**: Uses Vultr API to manage infrastructure
3. **Creates User Record**: Stores user data in MongoDB
4. **Assigns Resources**: Allocates CPU, memory, storage based on plan
5. **Sets Up Directory Structure**: Creates isolated user directories
6. **Configures Security**: Applies Linux user isolation and permissions
7. **Returns Response**: Provides user details and access information

### **Directory Structure Created:**
```
/var/www/user_username/
├── public_html/     # Web root
├── logs/           # Application logs
├── tmp/            # Temporary files
├── backups/        # User backups
├── ssl/            # SSL certificates
└── apps/           # Application directories
    ├── static/     # Static websites (HTML, React, Vue)
    ├── web-service/ # Web services (Node.js, Python, etc.)
    ├── nodejs/     # Node.js specific apps
    └── php/        # PHP specific apps
```

### **Security Features:**
- **Linux User Isolation**: Each user gets their own Linux user account
- **Directory Permissions**: chmod 700 ensures users can't access each other's files
- **Resource Limits**: systemd slices prevent resource abuse
- **Bandwidth Throttling**: Traffic control (tc) limits bandwidth per user
- **Process Limits**: Maximum 50 processes per user
- **Cron Restrictions**: Users can't run background cron jobs

## 🔄 **NEXT STEPS AFTER USER CREATION**

### **1. Create Applications**
After creating a shared user, you can create applications:

```bash
curl -X POST "http://localhost:3000/api/v1/services/hosting/shared/users/user_12345/applications" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "my-nodejs-app",
    "type": "web-service",
    "framework": "nodejs"
  }'
```

### **2. Supported Application Types**
- **Static Websites**: `html`, `react`, `vue`, `angular`
- **Web Services**: `nodejs`, `python`, `rust`, `php`, `go`, `java`, `dotnet`, `ruby`

### **3. User-Driven Dependencies**
The system automatically uses your dependency files:
- **Node.js**: `package.json`
- **Python**: `requirements.txt`
- **Rust**: `Cargo.toml`
- **PHP**: `composer.json`
- **Go**: `go.mod`
- **Java**: `pom.xml`
- **.NET**: `.csproj`
- **Ruby**: `Gemfile`

## 📞 **SUPPORT**

If you continue to have issues:
1. **Check Vultr API Status**: [Vultr Status Page](https://status.vultr.com/)
2. **Verify API Key**: Test with Vultr API directly
3. **Check Rate Limits**: Ensure you're not exceeding 30 requests/second
4. **Review Logs**: Check backend logs for detailed error messages
5. **Test Network**: Ensure backend can reach `api.vultr.com`

**The shared hosting system provides secure, isolated hosting for multiple users with African market pricing and Vultr infrastructure!**
