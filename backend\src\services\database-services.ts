import { logger } from '../utils/logger';
import { appConfig } from '../config';
import { SharedHostingUser } from '../models/shared-hosting';
import { SSHConnection } from './shared-hosting';

interface DatabaseConfig {
  type: 'mysql' | 'postgresql' | 'mongodb';
  name: string;
  username: string;
  password: string;
  host: string;
  port: number;
  maxConnections?: number;
  storageLimit?: number; // in MB
}



export class DatabaseServicesManager {
  private mysqlRootPassword: string;

  constructor() {
    this.mysqlRootPassword = appConfig.database.mysqlRootPassword || 'defaultpassword';
  }

  // Create database for user
  async createUserDatabase(userId: string, config: {
    type: 'mysql' | 'postgresql' | 'mongodb';
    databaseName: string;
    storageLimit?: number;
  }): Promise<DatabaseConfig> {
    try {
      const user = await SharedHostingUser.findOne({ user_id: userId });
      if (!user) {
        throw new Error('User not found');
      }

      // Generate database credentials
      const dbUsername = `${user.linux_username}_db`;
      const dbPassword = this.generateSecurePassword();
      const dbName = `${user.linux_username}_${config.databaseName}`;

      let dbConfig: DatabaseConfig;

      switch (config.type) {
        case 'mysql':
          dbConfig = await this.createMySQLDatabase(dbUsername, dbPassword, dbName, config.storageLimit);
          break;
        case 'postgresql':
          dbConfig = await this.createPostgreSQLDatabase(dbUsername, dbPassword, dbName, config.storageLimit);
          break;
        case 'mongodb':
          dbConfig = await this.createMongoDatabase(dbUsername, dbPassword, dbName, config.storageLimit);
          break;
        default:
          throw new Error(`Unsupported database type: ${config.type}`);
      }

      // Store database configuration in user's environment
      await this.setupDatabaseEnvironment(user, dbConfig);

      logger.info(`Created ${config.type} database ${dbName} for user ${user.username}`);
      return dbConfig;

    } catch (error) {
      logger.error(`Failed to create database for user ${userId}:`, error);
      throw error;
    }
  }

  // Create MySQL database
  async createMySQLDatabase(username: string, password: string, dbName: string, storageLimit?: number): Promise<DatabaseConfig> {
    const ssh = new SSHConnection();
    
    try {
      await ssh.connect();

      // Create database
      await ssh.executeCommand(`mysql -u root -p${this.mysqlRootPassword} -e "CREATE DATABASE IF NOT EXISTS ${dbName} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"`);

      // Create user
      await ssh.executeCommand(`mysql -u root -p${this.mysqlRootPassword} -e "CREATE USER IF NOT EXISTS '${username}'@'localhost' IDENTIFIED BY '${password}';"`);

      // Grant privileges
      await ssh.executeCommand(`mysql -u root -p${this.mysqlRootPassword} -e "GRANT ALL PRIVILEGES ON ${dbName}.* TO '${username}'@'localhost';"`);

      // Set storage limit if specified
      if (storageLimit) {
        await this.setMySQLStorageLimit(ssh, dbName, storageLimit);
      }

      // Flush privileges
      await ssh.executeCommand(`mysql -u root -p${this.mysqlRootPassword} -e "FLUSH PRIVILEGES;"`);

      const config: DatabaseConfig = {
        type: 'mysql',
        name: dbName,
        username,
        password,
        host: 'localhost',
        port: 3306
      };

      if (storageLimit !== undefined) {
        config.storageLimit = storageLimit;
      }

      return config;

    } catch (error) {
      logger.error(`Failed to create MySQL database ${dbName}:`, error);
      throw error;
    } finally {
      await ssh.disconnect();
    }
  }

  // Create PostgreSQL database
  async createPostgreSQLDatabase(username: string, password: string, dbName: string, storageLimit?: number): Promise<DatabaseConfig> {
    const ssh = new SSHConnection();
    
    try {
      await ssh.connect();

      // Create user
      await ssh.executeCommand(`sudo -u postgres psql -c "CREATE USER ${username} WITH PASSWORD '${password}';"`);

      // Create database
      await ssh.executeCommand(`sudo -u postgres createdb -O ${username} ${dbName}`);

      // Set connection limit
      await ssh.executeCommand(`sudo -u postgres psql -c "ALTER USER ${username} CONNECTION LIMIT 10;"`);

      // Set storage limit if specified
      if (storageLimit) {
        await this.setPostgreSQLStorageLimit(ssh, dbName, storageLimit);
      }

      const config: DatabaseConfig = {
        type: 'postgresql',
        name: dbName,
        username,
        password,
        host: 'localhost',
        port: 5432,
        maxConnections: 10
      };

      if (storageLimit !== undefined) {
        config.storageLimit = storageLimit;
      }

      return config;

    } catch (error) {
      logger.error(`Failed to create PostgreSQL database ${dbName}:`, error);
      throw error;
    } finally {
      await ssh.disconnect();
    }
  }

  // Create MongoDB database
  async createMongoDatabase(username: string, password: string, dbName: string, storageLimit?: number): Promise<DatabaseConfig> {
    const ssh = new SSHConnection();
    
    try {
      await ssh.connect();

      // Create user and database in MongoDB
      const mongoScript = `
use ${dbName}
db.createUser({
  user: "${username}",
  pwd: "${password}",
  roles: [
    { role: "readWrite", db: "${dbName}" },
    { role: "dbAdmin", db: "${dbName}" }
  ]
})
`;

      // Write script to temporary file and execute
      await ssh.executeCommand(`cat > /tmp/create_mongo_user.js << 'EOF'\n${mongoScript}\nEOF`);
      await ssh.executeCommand(`mongo /tmp/create_mongo_user.js`);
      await ssh.executeCommand(`rm /tmp/create_mongo_user.js`);

      // Set storage limit if specified
      if (storageLimit) {
        await this.setMongoStorageLimit(ssh, dbName, storageLimit);
      }

      const config: DatabaseConfig = {
        type: 'mongodb',
        name: dbName,
        username,
        password,
        host: 'localhost',
        port: 27017
      };

      if (storageLimit !== undefined) {
        config.storageLimit = storageLimit;
      }

      return config;

    } catch (error) {
      logger.error(`Failed to create MongoDB database ${dbName}:`, error);
      throw error;
    } finally {
      await ssh.disconnect();
    }
  }

  // Set MySQL storage limit
  async setMySQLStorageLimit(ssh: SSHConnection, dbName: string, limitMB: number): Promise<void> {
    try {
      // Create a trigger to check database size
      const triggerSQL = `
CREATE TRIGGER IF NOT EXISTS ${dbName}_size_check
BEFORE INSERT ON information_schema.tables
FOR EACH ROW
BEGIN
  DECLARE db_size BIGINT;
  SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 1) INTO db_size
  FROM information_schema.tables
  WHERE table_schema = '${dbName}';
  
  IF db_size > ${limitMB} THEN
    SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Database size limit exceeded';
  END IF;
END;
`;

      await ssh.executeCommand(`mysql -u root -p${this.mysqlRootPassword} -e "${triggerSQL}"`);
    } catch (error) {
      logger.error(`Failed to set MySQL storage limit for ${dbName}:`, error);
    }
  }

  // Set PostgreSQL storage limit
  async setPostgreSQLStorageLimit(ssh: SSHConnection, dbName: string, limitMB: number): Promise<void> {
    try {
      // Set tablespace quota (requires custom tablespace)
      await ssh.executeCommand(`sudo -u postgres psql -d ${dbName} -c "CREATE TABLESPACE ${dbName}_space LOCATION '/var/lib/postgresql/tablespaces/${dbName}';"`);
      await ssh.executeCommand(`mkdir -p /var/lib/postgresql/tablespaces/${dbName}`);
      await ssh.executeCommand(`chown postgres:postgres /var/lib/postgresql/tablespaces/${dbName}`);
      
      // Set quota using filesystem quotas
      await ssh.executeCommand(`setquota -u postgres ${limitMB * 1024} ${limitMB * 1024} 0 0 /var/lib/postgresql/tablespaces/${dbName}`);
    } catch (error) {
      logger.error(`Failed to set PostgreSQL storage limit for ${dbName}:`, error);
    }
  }

  // Set MongoDB storage limit
  async setMongoStorageLimit(ssh: SSHConnection, dbName: string, limitMB: number): Promise<void> {
    try {
      // MongoDB doesn't have built-in storage limits, so we'll use filesystem quotas
      const mongoDataPath = `/var/lib/mongodb/${dbName}`;
      await ssh.executeCommand(`mkdir -p ${mongoDataPath}`);
      await ssh.executeCommand(`chown mongodb:mongodb ${mongoDataPath}`);
      
      // Set filesystem quota
      await ssh.executeCommand(`setquota -u mongodb ${limitMB * 1024} ${limitMB * 1024} 0 0 ${mongoDataPath}`);
    } catch (error) {
      logger.error(`Failed to set MongoDB storage limit for ${dbName}:`, error);
    }
  }

  // Setup database environment variables for user
  async setupDatabaseEnvironment(user: any, dbConfig: DatabaseConfig): Promise<void> {
    const ssh = new SSHConnection();
    
    try {
      await ssh.connect();

      const envVars = `
# Database Configuration
DB_TYPE=${dbConfig.type}
DB_HOST=${dbConfig.host}
DB_PORT=${dbConfig.port}
DB_NAME=${dbConfig.name}
DB_USERNAME=${dbConfig.username}
DB_PASSWORD=${dbConfig.password}
DATABASE_URL=${this.generateConnectionString(dbConfig)}
`;

      // Add to user's .bashrc
      await ssh.executeCommand(`echo '${envVars}' >> ${user.home_directory}/.bashrc`);

      // Create .env file in user's home directory
      await ssh.executeCommand(`echo '${envVars}' > ${user.home_directory}/.env`);
      await ssh.executeCommand(`chown ${user.linux_username}:${user.linux_username} ${user.home_directory}/.env`);
      await ssh.executeCommand(`chmod 600 ${user.home_directory}/.env`);

    } catch (error) {
      logger.error(`Failed to setup database environment for user ${user.username}:`, error);
      throw error;
    } finally {
      await ssh.disconnect();
    }
  }

  // Generate database connection string
  generateConnectionString(config: DatabaseConfig): string {
    switch (config.type) {
      case 'mysql':
        return `mysql://${config.username}:${config.password}@${config.host}:${config.port}/${config.name}`;
      case 'postgresql':
        return `postgresql://${config.username}:${config.password}@${config.host}:${config.port}/${config.name}`;
      case 'mongodb':
        return `mongodb://${config.username}:${config.password}@${config.host}:${config.port}/${config.name}`;
      default:
        throw new Error(`Unsupported database type: ${config.type}`);
    }
  }

  // Delete user database
  async deleteUserDatabase(userId: string, databaseName: string, type: 'mysql' | 'postgresql' | 'mongodb'): Promise<void> {
    const ssh = new SSHConnection();
    
    try {
      await ssh.connect();

      const user = await SharedHostingUser.findOne({ user_id: userId });
      if (!user) {
        throw new Error('User not found');
      }

      const dbUsername = `${user.linux_username}_db`;
      const dbName = `${user.linux_username}_${databaseName}`;

      switch (type) {
        case 'mysql':
          await ssh.executeCommand(`mysql -u root -p${this.mysqlRootPassword} -e "DROP DATABASE IF EXISTS ${dbName};"`);
          await ssh.executeCommand(`mysql -u root -p${this.mysqlRootPassword} -e "DROP USER IF EXISTS '${dbUsername}'@'localhost';"`);
          break;
        case 'postgresql':
          await ssh.executeCommand(`sudo -u postgres dropdb ${dbName}`);
          await ssh.executeCommand(`sudo -u postgres psql -c "DROP USER IF EXISTS ${dbUsername};"`);
          break;
        case 'mongodb':
          await ssh.executeCommand(`mongo ${dbName} --eval "db.dropUser('${dbUsername}')"`);
          await ssh.executeCommand(`mongo ${dbName} --eval "db.dropDatabase()"`);
          break;
      }

      logger.info(`Deleted ${type} database ${dbName} for user ${user.username}`);

    } catch (error) {
      logger.error(`Failed to delete database ${databaseName} for user ${userId}:`, error);
      throw error;
    } finally {
      await ssh.disconnect();
    }
  }

  // Get database usage statistics
  async getDatabaseUsage(userId: string): Promise<any[]> {
    const ssh = new SSHConnection();
    
    try {
      await ssh.connect();

      const user = await SharedHostingUser.findOne({ user_id: userId });
      if (!user) {
        throw new Error('User not found');
      }

      const usage = [];

      // Get MySQL databases
      const mysqlDbs = await ssh.executeCommand(`mysql -u root -p${this.mysqlRootPassword} -e "SELECT table_schema as 'database', ROUND(SUM(data_length + index_length) / 1024 / 1024, 1) AS 'size_mb' FROM information_schema.tables WHERE table_schema LIKE '${user.linux_username}_%' GROUP BY table_schema;" --skip-column-names`);
      
      if (mysqlDbs.trim()) {
        const mysqlLines = mysqlDbs.trim().split('\n');
        for (const line of mysqlLines) {
          const [dbName, sizeMB] = line.split('\t');
          usage.push({
            type: 'mysql',
            name: dbName,
            sizeMB: parseFloat(sizeMB || '0') || 0
          });
        }
      }

      // Get PostgreSQL databases
      const pgDbs = await ssh.executeCommand(`sudo -u postgres psql -c "SELECT datname, pg_size_pretty(pg_database_size(datname)) FROM pg_database WHERE datname LIKE '${user.linux_username}_%';" --tuples-only --no-align --field-separator='|'`);
      
      if (pgDbs.trim()) {
        const pgLines = pgDbs.trim().split('\n');
        for (const line of pgLines) {
          const [dbName, sizeStr] = line.split('|');
          usage.push({
            type: 'postgresql',
            name: dbName,
            size: sizeStr
          });
        }
      }

      return usage;

    } catch (error) {
      logger.error(`Failed to get database usage for user ${userId}:`, error);
      throw error;
    } finally {
      await ssh.disconnect();
    }
  }

  // Generate secure password
  private generateSecurePassword(length: number = 16): string {
    const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
    let password = '';
    for (let i = 0; i < length; i++) {
      password += charset.charAt(Math.floor(Math.random() * charset.length));
    }
    return password;
  }

  // Backup database
  async backupDatabase(userId: string, databaseName: string, type: 'mysql' | 'postgresql' | 'mongodb'): Promise<string> {
    const ssh = new SSHConnection();
    
    try {
      await ssh.connect();

      const user = await SharedHostingUser.findOne({ user_id: userId });
      if (!user) {
        throw new Error('User not found');
      }

      const dbName = `${user.linux_username}_${databaseName}`;
      const backupDir = `${user.home_directory}/backups`;
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupFile = `${backupDir}/${dbName}_${timestamp}.sql`;

      // Create backup directory
      await ssh.executeCommand(`mkdir -p ${backupDir}`);

      switch (type) {
        case 'mysql':
          await ssh.executeCommand(`mysqldump -u root -p${this.mysqlRootPassword} ${dbName} > ${backupFile}`);
          break;
        case 'postgresql':
          await ssh.executeCommand(`sudo -u postgres pg_dump ${dbName} > ${backupFile}`);
          break;
        case 'mongodb':
          await ssh.executeCommand(`mongodump --db ${dbName} --out ${backupDir}/${dbName}_${timestamp}`);
          break;
      }

      // Set ownership
      await ssh.executeCommand(`chown -R ${user.linux_username}:${user.linux_username} ${backupDir}`);

      logger.info(`Created backup for ${type} database ${dbName}: ${backupFile}`);
      return backupFile;

    } catch (error) {
      logger.error(`Failed to backup database ${databaseName} for user ${userId}:`, error);
      throw error;
    } finally {
      await ssh.disconnect();
    }
  }
}

// Export singleton instance
export const databaseServices = new DatabaseServicesManager();
