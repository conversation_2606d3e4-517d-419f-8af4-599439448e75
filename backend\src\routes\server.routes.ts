import { FastifyInstance } from 'fastify';
import { authMiddleware } from '../middleware/auth';

// Server controllers
import {
  getServers<PERSON>ontroller,
  getServerController,
  getServerMetricsController,
  provisionServerController,
  getSharedServerStatusController,
  getServerRegionsController,
  getServerPlansController
} from '../controllers/server.controller';

// Hosting controllers
import {
  getHostingPlansController,
  getRecommendedPlanController,
  getHostingPlanController,
  createSharedUserController,
  getSharedUsersController,
  getSharedUserController,
  updateSharedUserController,
  deleteSharedUserController,
  getSharedHostingAnalyticsController,
  getUserResourceUsageController,
  getServerCapacityController,
  getUserApplicationsController,
  createApplicationController as createSharedApplicationController,
  updateUserApplicationController,
  deleteUserApplicationController,
  suspendUser<PERSON>ontroller,
  reactivateUser<PERSON><PERSON>roller,
  resetUserPasswordController
} from '../controllers/hosting.controller';

// Application controllers
import {
  createApplicationController,
  getApplicationsController,
  getApplicationController,
  updateApplicationController,
  deleteApplicationController,
  deployApplicationController,
  stopApplicationController,
  getApplicationStatsController,
} from '../controllers/application.controller';

export async function serverRoutes(fastify: FastifyInstance): Promise<void> {
  // =============================================================================
  // PUBLIC ROUTES (No authentication required)
  // =============================================================================

  // Server information
  fastify.get('/regions', getServerRegionsController);
  fastify.get('/plans', getServerPlansController);

  // Hosting plans
  fastify.get('/hosting/plans', getHostingPlansController);
  fastify.get('/hosting/plans/recommend', getRecommendedPlanController);
  fastify.get('/hosting/plans/:planName', getHostingPlanController);

  // =============================================================================
  // PROTECTED ROUTES (Authentication required)
  // =============================================================================

  fastify.register(async function (fastify) {
    // Add authentication middleware
    fastify.addHook('preHandler', authMiddleware);

    // =========================================================================
    // SERVER MANAGEMENT ROUTES (Dedicated/Enterprise)
    // =========================================================================

    // Server listing and details
    fastify.get('/', getServersController);
    fastify.get('/shared/status', getSharedServerStatusController);
    fastify.get('/:serverId', getServerController);
    fastify.get('/:serverId/metrics', getServerMetricsController);

    // Server provisioning (for dedicated/enterprise tiers)
    fastify.post('/provision', provisionServerController);

    // =========================================================================
    // SHARED HOSTING MANAGEMENT ROUTES
    // =========================================================================

    // Shared hosting user CRUD operations
    fastify.get('/hosting/shared/users', getSharedUsersController); // Admin: Get all users
    fastify.post('/hosting/shared/users', createSharedUserController); // Create new user
    fastify.get('/hosting/shared/users/:userId', getSharedUserController); // Get specific user
    fastify.put('/hosting/shared/users/:userId', updateSharedUserController); // Admin: Update user
    fastify.delete('/hosting/shared/users/:userId', deleteSharedUserController); // Super Admin: Delete user

    // Shared hosting analytics and monitoring
    fastify.get('/hosting/shared/analytics', getSharedHostingAnalyticsController); // Admin: Analytics
    fastify.get('/hosting/shared/users/:userId/usage', getUserResourceUsageController); // User resource usage
    fastify.get('/hosting/shared/servers/capacity', getServerCapacityController); // Admin: Server capacity
    fastify.get('/hosting/shared/servers/:serverId/capacity', getServerCapacityController); // Admin: Specific server capacity

    // User management actions (Admin only)
    fastify.post('/hosting/shared/users/:userId/suspend', suspendUserController); // Admin: Suspend user
    fastify.post('/hosting/shared/users/:userId/reactivate', reactivateUserController); // Admin: Reactivate user
    fastify.post('/hosting/shared/users/:userId/reset-password', resetUserPasswordController); // Admin: Reset password

    // Shared hosting application management
    fastify.get('/hosting/shared/users/:userId/applications', getUserApplicationsController); // Get user apps
    fastify.post('/hosting/shared/users/:userId/applications', createSharedApplicationController); // Create app
    fastify.put('/hosting/shared/users/:userId/applications/:appId', updateUserApplicationController); // Update app
    fastify.delete('/hosting/shared/users/:userId/applications/:appId', deleteUserApplicationController); // Delete app

    // =========================================================================
    // APPLICATION MANAGEMENT ROUTES (All tiers)
    // =========================================================================

    // Application CRUD operations
    fastify.post('/applications', createApplicationController);
    fastify.get('/applications', getApplicationsController);
    fastify.get('/applications/stats', getApplicationStatsController);
    fastify.get('/applications/:id', getApplicationController);
    fastify.put('/applications/:id', updateApplicationController);
    fastify.delete('/applications/:id', deleteApplicationController);

    // Application deployment operations
    fastify.post('/applications/:id/deploy', deployApplicationController);
    fastify.post('/applications/:id/stop', stopApplicationController);
  });
}
