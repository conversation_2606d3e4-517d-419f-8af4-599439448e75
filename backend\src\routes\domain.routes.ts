import { FastifyInstance } from 'fastify';
import {
  checkDomainAvailability<PERSON><PERSON>roller,
  transferD<PERSON>in<PERSON><PERSON>roll<PERSON>,
  whoisLookupController,
  getDomainSuggestionsController,
  getPopularTldsController,
} from '../controllers/domain.controller';
import { authMiddleware } from '../middleware/auth';

export async function domainRoutes(fastify: FastifyInstance): Promise<void> {
  // Public routes (no authentication required)
  
  // Get popular TLDs - public information
  fastify.get('/tlds/popular', getPopularTldsController);
  
  // Get domain suggestions - public feature
  fastify.get('/suggestions', getDomainSuggestionsController);

  // Protected routes (authentication required)
  fastify.register(async function (fastify) {
    fastify.addHook('preHandler', authMiddleware);

    // Domain availability check
    fastify.post('/availability', {
      schema: {
        description: 'Check domain availability for multiple domains and TLDs',
        tags: ['domains'],
        body: {
          type: 'object',
          required: ['domains', 'tlds'],
          properties: {
            domains: {
              type: 'array',
              items: { type: 'string' },
              description: 'Array of domain names to check (without TLD)'
            },
            tlds: {
              type: 'array',
              items: { type: 'string' },
              description: 'Array of TLDs to check against'
            }
          }
        },
        response: {
          200: {
            type: 'object',
            properties: {
              success: { type: 'boolean' },
              data: {
                type: 'object',
                additionalProperties: {
                  type: 'object',
                  properties: {
                    status: { 
                      type: 'string',
                      enum: ['available', 'regthroughus', 'regthroughothers', 'unknown']
                    },
                    classkey: { type: 'string' },
                    ispremiumname: { type: 'boolean' },
                    premiumcost: { type: 'number' },
                    eapfee: { type: 'number' }
                  }
                }
              }
            }
          }
        }
      }
    }, checkDomainAvailabilityController);

    // Domain transfer
    fastify.post('/transfer', {
      schema: {
        description: 'Transfer a domain to your account',
        tags: ['domains'],
        body: {
          type: 'object',
          required: [
            'domainName', 'customerId', 'regContactId', 'adminContactId', 
            'techContactId', 'billingContactId', 'invoiceOption', 'autoRenew'
          ],
          properties: {
            domainName: {
              type: 'string',
              description: 'Full domain name to transfer',
              example: 'example.com'
            },
            authCode: {
              type: 'string',
              description: 'Authorization code for domain transfer'
            },
            customerId: {
              type: 'number',
              description: 'Customer ID for the domain owner'
            },
            regContactId: {
              type: 'number',
              description: 'Registrant contact ID'
            },
            adminContactId: {
              type: 'number',
              description: 'Administrative contact ID'
            },
            techContactId: {
              type: 'number',
              description: 'Technical contact ID'
            },
            billingContactId: {
              type: 'number',
              description: 'Billing contact ID'
            },
            invoiceOption: {
              type: 'string',
              enum: ['NoInvoice', 'PayInvoice', 'KeepInvoice', 'OnlyAdd'],
              description: 'Invoice handling option'
            },
            autoRenew: {
              type: 'boolean',
              description: 'Enable auto-renewal for the domain'
            },
            purchasePrivacy: {
              type: 'boolean',
              description: 'Purchase domain privacy protection'
            },
            protectPrivacy: {
              type: 'boolean',
              description: 'Protect existing privacy settings'
            },
            nameServers: {
              type: 'array',
              items: { type: 'string' },
              description: 'Custom name servers for the domain'
            },
            attributes: {
              type: 'object',
              description: 'Additional domain attributes (TLD-specific)'
            },
            purchasePremiumDns: {
              type: 'boolean',
              description: 'Purchase premium DNS service'
            }
          }
        },
        response: {
          201: {
            type: 'object',
            properties: {
              success: { type: 'boolean' },
              data: {
                type: 'object',
                properties: {
                  description: { type: 'string' },
                  entityid: { type: 'string' },
                  actiontype: { type: 'string' },
                  actiontypedesc: { type: 'string' },
                  eaqid: { type: 'string' },
                  actionstatus: { type: 'string' },
                  actionstatusdesc: { type: 'string' },
                  invoiceid: { type: 'string' },
                  sellingcurrencysymbol: { type: 'string' },
                  sellingamount: { type: 'number' },
                  customerid: { type: 'number' }
                }
              }
            }
          }
        }
      }
    }, transferDomainController);

    // WHOIS lookup
    fastify.post('/whois', {
      schema: {
        description: 'Perform WHOIS lookup for a domain',
        tags: ['domains'],
        body: {
          type: 'object',
          required: ['domainName'],
          properties: {
            domainName: {
              type: 'string',
              description: 'Domain name to lookup',
              example: 'example.com'
            }
          }
        },
        response: {
          200: {
            type: 'object',
            properties: {
              success: { type: 'boolean' },
              data: {
                type: 'object',
                properties: {
                  domain: { type: 'string' },
                  registrar: { type: 'string' },
                  registrant: {
                    type: 'object',
                    properties: {
                      name: { type: 'string' },
                      organization: { type: 'string' },
                      email: { type: 'string' },
                      phone: { type: 'string' },
                      address: { type: 'string' },
                      city: { type: 'string' },
                      state: { type: 'string' },
                      country: { type: 'string' },
                      postalCode: { type: 'string' }
                    }
                  },
                  nameServers: {
                    type: 'array',
                    items: { type: 'string' }
                  },
                  creationDate: { type: 'string' },
                  expirationDate: { type: 'string' },
                  updatedDate: { type: 'string' },
                  status: {
                    type: 'array',
                    items: { type: 'string' }
                  }
                }
              }
            }
          }
        }
      }
    }, whoisLookupController);
  });
}
