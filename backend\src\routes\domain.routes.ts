import { FastifyInstance } from 'fastify';
import {
  checkDomainAvailabilityController,
  transferDomainController,
  whoisLookupController,
  getDomainSuggestionsController,
  getPopularTldsController,
} from '../controllers/domain.controller';
import { authMiddleware } from '../middleware/auth';

export async function domainRoutes(fastify: FastifyInstance): Promise<void> {
  // Public routes (no authentication required)
  fastify.get('/tlds/popular', getPopularTldsController);
  fastify.get('/suggestions', getDomainSuggestionsController);
  fastify.post('/availability', checkDomainAvailabilityController);

  // Protected routes (authentication required)
  fastify.register(async function (fastify) {
    // Add auth middleware to all routes in this context
    fastify.addHook('preHandler', authMiddleware);

    fastify.post('/transfer', transferDomainController);
    fastify.post('/whois', whoisLookupController);
  });
}
