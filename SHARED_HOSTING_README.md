# 🏠 SHARED HOSTING USER CREATION GUIDE

## 📋 **OVERVIEW**
This guide shows you how to create shared hosting users on the Achidas platform. Shared hosting allows multiple users to share server resources with Linux user isolation for security.

## 🔧 **PREREQUISITES**

### **1. Server Setup**
Before creating shared users, ensure your shared server is properly configured:

```bash
# Run the setup script on your shared server (*************)
sudo bash scripts/setup_native_linux_hosting.sh
```

### **2. Authentication**
You need a valid JWT token to create shared users. Get one by logging in:

```bash
curl -X POST "http://localhost:3000/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "your-password"
  }'
```

## 🚀 **CREATING SHARED USERS**

### **API Endpoint**
```
POST /api/v1/services/hosting/shared/users
```

### **Request Headers**
```
Authorization: Bearer your-jwt-token
Content-Type: application/json
```

### **Request Body Schema**
```json
{
  "user_id": "string (required)",     // Unique user identifier
  "username": "string (required)",    // Username for the hosting account
  "plan": "string (required)",        // Hosting plan: free, starter, basic, standard, pro
  "server_id": "string (optional)"    // Specific server ID (auto-assigned if not provided)
}
```

## 📊 **AVAILABLE PLANS**

| Plan | CPU | RAM | Storage | Bandwidth | Price/Month | African Pricing |
|------|-----|-----|---------|-----------|-------------|-----------------|
| **free** | 2% | 128MB | 1GB | 5GB | $0 | Free |
| **starter** | 5% | 256MB | 5GB | 25GB | $2.16 | ₦3,500 / R40 / KSh280 / GH₵26 |
| **basic** | 10% | 512MB | 10GB | 50GB | $5.00 | ₦8,100 / R92 / KSh650 / GH₵60 |
| **standard** | 15% | 1GB | 20GB | 100GB | $10.00 | ₦16,200 / R185 / KSh1,300 / GH₵120 |
| **pro** | 25% | 2GB | 50GB | 250GB | $15.00 | ₦24,300 / R277 / KSh1,950 / GH₵180 |

## 🎯 **EXAMPLES**

### **Example 1: Create Free Plan User**
```bash
curl -X POST "http://localhost:3000/api/v1/services/hosting/shared/users" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "user_12345",
    "username": "johndoe",
    "plan": "free"
  }'
```

### **Example 2: Create Starter Plan User**
```bash
curl -X POST "http://localhost:3000/api/v1/services/hosting/shared/users" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "user_67890",
    "username": "janesmithbiz",
    "plan": "starter"
  }'
```

### **Example 3: Create Pro Plan User**
```bash
curl -X POST "http://localhost:3000/api/v1/services/hosting/shared/users" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "user_11111",
    "username": "enterprise-app",
    "plan": "pro"
  }'
```

## ✅ **SUCCESS RESPONSE**
```json
{
  "success": true,
  "data": {
    "id": "shared_1703123456789_abc123def",
    "user_id": "user_12345",
    "username": "johndoe",
    "linux_username": "user_johndoe",
    "home_directory": "/var/www/user_johndoe",
    "plan": "free",
    "status": "active",
    "server_id": "vultr-jnb-shared-01",
    "server_ip": "*************",
    "port": 3001,
    "ssh_port": 2201,
    "ftp_port": 2101,
    "resource_limits": {
      "cpu_quota": 2,
      "memory_max": 128,
      "bandwidth_limit": 5,
      "storage_limit": 1
    },
    "usage": {
      "cpu_usage": 0,
      "memory_usage": 0,
      "bandwidth_used": 0,
      "storage_used": 0
    },
    "created_at": "2024-12-21T10:30:45.789Z",
    "applications": []
  },
  "meta": {
    "timestamp": "2024-12-21T10:30:45.789Z",
    "request_id": "req_abc123def456",
    "trace_id": "trace_789xyz123",
    "version": "1.0.0",
    "status_code": 201
  }
}
```

## ❌ **ERROR RESPONSES**

### **400 - Validation Error**
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Missing required fields: user_id, username, plan"
  }
}
```

### **401 - Unauthorized**
```json
{
  "success": false,
  "error": {
    "code": "UNAUTHORIZED",
    "message": "Authentication required"
  }
}
```

### **409 - User Already Exists**
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "User already exists"
  }
}
```

### **500 - Internal Server Error**
```json
{
  "success": false,
  "error": {
    "code": "INTERNAL_ERROR",
    "message": "Failed to create shared hosting user"
  }
}
```

## 🔧 **TROUBLESHOOTING**

### **Issue: 500 Internal Server Error**

**Possible Causes:**
1. **Server not configured**: Shared server (*************) not set up properly
2. **SSH access issues**: Backend can't connect to shared server
3. **Permission issues**: Backend doesn't have sudo privileges
4. **Missing dependencies**: Required packages not installed

**Solutions:**

#### **1. Check Server Configuration**
```bash
# SSH into your shared server
ssh root@*************

# Run the setup script
sudo bash scripts/setup_native_linux_hosting.sh

# Verify systemd is working
sudo systemctl status systemd-logind
```

#### **2. Test SSH Connection**
```bash
# From your backend server, test SSH connection
ssh root@************* "echo 'SSH connection working'"
```

#### **3. Check Backend Logs**
```bash
# Check backend logs for detailed error
tail -f backend/logs/achidas.log

# Or check console output
npm start
```

#### **4. Manual User Creation Test**
```bash
# SSH to shared server and test manual user creation
ssh root@*************
sudo adduser --disabled-password --gecos "" --home /var/www/user_test user_test
sudo chmod 700 /var/www/user_test
sudo chown user_test:user_test /var/www/user_test
```

### **Issue: Authentication Required**
Make sure you're including the JWT token in the Authorization header:
```bash
# Get token first
TOKEN=$(curl -s -X POST "http://localhost:3000/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email":"your-email","password":"your-password"}' | \
  jq -r '.data.token')

# Use token in request
curl -X POST "http://localhost:3000/api/v1/services/hosting/shared/users" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"user_id":"test123","username":"testuser","plan":"free"}'
```

### **Issue: Invalid Plan**
Make sure you're using one of the valid plan names:
- `free`
- `starter` 
- `basic`
- `standard`
- `pro`

## 🏗️ **WHAT HAPPENS WHEN YOU CREATE A USER**

1. **Linux User Creation**: Creates isolated Linux user with `user_` prefix
2. **Directory Structure**: Sets up web hosting directories:
   ```
   /var/www/user_username/
   ├── public_html/     # Web root
   ├── logs/           # Application logs
   ├── tmp/            # Temporary files
   ├── backups/        # User backups
   ├── ssl/            # SSL certificates
   └── apps/           # Application directories
       ├── static/     # Static websites
       ├── nodejs/     # Node.js apps
       └── php/        # PHP apps
   ```
3. **Resource Limits**: Applies CPU, memory, and bandwidth limits based on plan
4. **Security**: Sets chmod 700 permissions for user isolation
5. **Port Assignment**: Assigns unique ports for web, SSH, and FTP access

## 🔐 **SECURITY FEATURES**

- **Linux User Isolation**: Each user gets their own Linux user account
- **Directory Permissions**: chmod 700 ensures users can't access each other's files
- **Resource Limits**: systemd slices prevent resource abuse
- **Bandwidth Throttling**: Traffic control (tc) limits bandwidth per user
- **Cron Restrictions**: Users can't run background cron jobs
- **Process Limits**: Maximum 50 processes per user

## 📞 **SUPPORT**

If you continue to have issues:
1. Check the backend logs: `tail -f backend/logs/achidas.log`
2. Verify server setup: Run `scripts/setup_native_linux_hosting.sh`
3. Test manual commands on the shared server
4. Ensure proper SSH key setup between backend and shared server

**The shared hosting system provides secure, isolated hosting for multiple users on a single server with African market pricing!**
