import { FastifyInstance } from 'fastify';
import {
  getServers<PERSON><PERSON><PERSON><PERSON>,
  getServerController,
  createServerController,
  deleteServerController,
  startServerController,
  stopServerController,
  rebootServerController,
  getPlansController,
  getRegionsController,
  getBillingController,
  getAccountInfoController,
  getOperatingSystemsController,
  getBlockStorageController,
  createBlockStorageController,
  deleteBlockStorageController,
  attachBlockStorageController,
  detachBlockStorageController,
  getSSHKeysController,
  getSnapshotsController,
  getLoadBalancersController,
} from '../controllers/vultr.controller';
import { authMiddleware, adminMiddleware } from '../middleware/auth';

export async function vultrRoutes(fastify: FastifyInstance): Promise<void> {
  // All Vultr routes require authentication
  fastify.addHook('preHandler', authMiddleware);

  // Server management routes
  fastify.get('/fetch-servers', getServersController);
  fastify.get('/servers/:id', getServerController);
  
  // Admin-only server operations
  fastify.register(async function (fastify) {
    fastify.addHook('preHandler', adminMiddleware);
    
    fastify.post('/servers', createServerController);
    fastify.delete('/servers/:id', deleteServerController);
    fastify.post('/servers/:id/start', startServerController);
    fastify.post('/servers/:id/stop', stopServerController);
    fastify.post('/servers/:id/reboot', rebootServerController);
  });

  // Plans and regions (public info)
  fastify.get('/plans', getPlansController);
  fastify.get('/regions', getRegionsController);
  fastify.get('/os', getOperatingSystemsController);

  // Account and billing info (admin only)
  fastify.register(async function (fastify) {
    fastify.addHook('preHandler', adminMiddleware);
    
    fastify.get('/account', getAccountInfoController);
    fastify.get('/billing', getBillingController);
  });

  // Block storage routes
  fastify.get('/block-storage', getBlockStorageController);
  
  fastify.register(async function (fastify) {
    fastify.addHook('preHandler', adminMiddleware);
    
    fastify.post('/block-storage', createBlockStorageController);
    fastify.delete('/block-storage/:id', deleteBlockStorageController);
    fastify.post('/block-storage/:id/attach', attachBlockStorageController);
    fastify.post('/block-storage/:id/detach', detachBlockStorageController);
  });

  // Other resources
  fastify.get('/ssh-keys', getSSHKeysController);
  fastify.get('/snapshots', getSnapshotsController);
  fastify.get('/load-balancers', getLoadBalancersController);
}
