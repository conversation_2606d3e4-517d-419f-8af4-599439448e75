import Fastify, { FastifyInstance } from 'fastify';
import { appConfig } from './config';
import { logger } from './utils/logger';
import { databaseConnection } from './database/connection';
import { metricsCollector } from './services/metrics-collector';
import { registerCors } from './middleware/cors';
import { registerRateLimit } from './middleware/rate-limit';
import { registerErrorHandler } from './middleware/error';
import { registerRoutes } from './routes';

// Extend Fastify request interface to include user
declare module 'fastify' {
  interface FastifyRequest {
    user?: {
      sub: string;
      email: string;
      username: string;
      role: string;
      iat: number;
      exp: number;
    };
  }
}

export async function createApp(): Promise<FastifyInstance> {
  // Create Fastify instance
  const fastify = Fastify({
    logger: logger,
    requestIdLogLabel: 'request_id',
    requestIdHeader: 'x-request-id',
    genReqId: () => {
      return Math.random().toString(36).substring(2, 15) + 
             Math.random().toString(36).substring(2, 15);
    },
  });

  try {
    // Connect to database
    logger.info('Connecting to database...');
    await databaseConnection.connect();
    logger.info('Database connected successfully');

    // Register middleware
    logger.info('Registering CORS middleware...');
    await registerCors(fastify as any);

    logger.info('Registering rate limit middleware...');
    await registerRateLimit(fastify as any);

    logger.info('Registering error handler...');
    registerErrorHandler(fastify as any);

    // Add request logging
    logger.info('Adding request logging hooks...');
    fastify.addHook('onRequest', async (request, _reply) => {
      logger.info({
        method: request.method,
        url: request.url,
        ip: request.ip,
        userAgent: request.headers['user-agent'],
        requestId: request.id,
      }, 'Incoming request');
    });

    // Add response logging
    fastify.addHook('onResponse', async (request, reply) => {
      logger.info({
        method: request.method,
        url: request.url,
        statusCode: reply.statusCode,
        responseTime: reply.elapsedTime,
        requestId: request.id,
      }, 'Request completed');
    });

    // Register routes
    logger.info('Registering routes...');
    await registerRoutes(fastify as any);

    logger.info('Fastify application created successfully');
    return fastify as any;
  } catch (error) {
    logger.error('Failed to create Fastify application:', error);
    logger.error('Error details:', {
      name: error.name,
      message: error.message,
      stack: error.stack,
      code: error.code,
    });
    throw error;
  }
}

export async function startServer(): Promise<void> {
  try {
    const app = await createApp();

    // Start server
    await app.listen({
      port: appConfig.server.port,
      host: appConfig.server.host,
    });

    logger.info(`Server started on ${appConfig.server.host}:${appConfig.server.port}`);
    logger.info(`Environment: ${appConfig.server.env}`);
    logger.info(`API Documentation: http://${appConfig.server.host}:${appConfig.server.port}/health`);

    // Initialize metrics collection if monitoring is enabled (hourly)
    if (appConfig.monitoring.enabled) {
      logger.info('Hourly metrics collection enabled');
    }

    logger.info('🚀 Server successfully started and ready to accept connections!');

    // Graceful shutdown
    const gracefulShutdown = async (signal: string) => {
      logger.info(`Received ${signal}, shutting down gracefully`);
      
      try {
        // Stop metrics collection
        if (appConfig.monitoring.enabled) {
          metricsCollector.stopCollection();
        }

        await app.close();
        await databaseConnection.disconnect();
        logger.info('Server shut down successfully');
        process.exit(0);
      } catch (error) {
        logger.error('Error during shutdown:', error);
        process.exit(1);
      }
    };

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
}

// Start server if this file is run directly
if (require.main === module) {
  startServer();
}
