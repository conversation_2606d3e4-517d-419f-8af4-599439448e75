import { config } from 'dotenv';
import { z } from 'zod';

// Load environment variables
config();

// Environment validation schema
const envSchema = z.object({
  NODE_ENV: z.enum(['development', 'staging', 'production']).default('development'),
  PORT: z.string().transform(Number).default('3000'),
  HOST: z.string().default('0.0.0.0'),
  
  // Database
  DATABASE_URL: z.string().min(1, 'DATABASE_URL is required'),
  
  // Authentication
  JWT_SECRET: z.string().min(32, 'JWT_SECRET must be at least 32 characters'),
  JWT_EXPIRES_IN: z.string().default('7d'),
  
  // Vultr API
  VULTR_API_KEY: z.string().min(1, 'VULTR_API_KEY is required'),
  VULTR_API_BASE_URL: z.string().url().default('https://api.vultr.com/v2'),
  
  // Logging
  LOG_LEVEL: z.enum(['fatal', 'error', 'warn', 'info', 'debug', 'trace']).default('info'),
  LOG_PRETTY: z.string().transform(val => val === 'true').default('false'),

  // Rate Limiting
  RATE_LIMIT_MAX: z.string().transform(Number).default('100'),
  RATE_LIMIT_WINDOW: z.string().transform(Number).default('60000'),

  // CORS
  CORS_ORIGIN: z.string().default('*'),
  CORS_CREDENTIALS: z.string().transform(val => val === 'true').default('true'),

  // Monitoring
  JAEGER_ENDPOINT: z.string().url().optional(),
  METRICS_PORT: z.string().transform(Number).default('9090'),

  // Circuit Breaker
  CIRCUIT_BREAKER_FAILURE_THRESHOLD: z.string().transform(Number).default('5'),
  CIRCUIT_BREAKER_TIMEOUT_SECONDS: z.string().transform(Number).default('60'),

  // Shared Hosting Configuration
  SHARED_HOSTING_SERVERS: z.string().optional(),
  SHARED_HOSTING_MAX_USERS_PER_SERVER: z.string().transform(Number).default('50'),
  SHARED_HOSTING_BASE_PORT: z.string().transform(Number).default('8000'),
  SHARED_HOSTING_SSH_BASE_PORT: z.string().transform(Number).default('2200'),
  SHARED_HOSTING_FTP_BASE_PORT: z.string().transform(Number).default('2100'),

  // SSH Configuration
  SSH_HOST: z.string().optional(),
  SSH_PORT: z.string().transform(Number).default('22'),
  SSH_USERNAME: z.string().default('root'),
  SSH_PASSWORD: z.string().optional(),
  SSH_PRIVATE_KEY_PATH: z.string().optional(),
  SSH_TIMEOUT: z.string().transform(Number).default('30000'),

  // Payment and Lifecycle Configuration
  GRACE_PERIOD_DAYS_DEFAULT: z.string().transform(Number).default('30'),
  GRACE_PERIOD_DAYS_PREMIUM: z.string().transform(Number).default('60'),
  DELETION_DELAY_DAYS: z.string().transform(Number).default('7'),
  PAYMENT_CHECK_INTERVAL_HOURS: z.string().transform(Number).default('24'),

  // Server Synchronization
  SYNC_INTERVAL_MINUTES: z.string().transform(Number).default('5'),
  SYNC_ENABLED: z.string().transform(val => val === 'true').default('true'),

  // Resource Monitoring
  MONITORING_ENABLED: z.string().transform(val => val === 'true').default('true'),
  MONITORING_INTERVAL_SECONDS: z.string().transform(Number).default('60'),
  METRICS_RETENTION_DAYS: z.string().transform(Number).default('30'),

  // Security
  SECURITY_MONITORING_ENABLED: z.string().transform(val => val === 'true').default('true'),
  MAX_LOGIN_ATTEMPTS: z.string().transform(Number).default('5'),
  LOCKOUT_DURATION_MINUTES: z.string().transform(Number).default('15'),
  ABUSE_CHECK_INTERVAL_MINUTES: z.string().transform(Number).default('5'),

  // Domain Configuration
  PRIMARY_DOMAIN: z.string().default('poolot.com'),

  // Cloudflare Integration
  CLOUDFLARE_API_TOKEN: z.string().optional(),
  CLOUDFLARE_ZONE_ID: z.string().optional(),
  CLOUDFLARE_DNS_ENABLED: z.string().transform(val => val === 'true').default('false'),

  // Backup Configuration
  BACKUP_ENABLED: z.string().transform(val => val === 'true').default('true'),
  BACKUP_INTERVAL_HOURS: z.string().transform(Number).default('24'),
  BACKUP_RETENTION_DAYS: z.string().transform(Number).default('30'),
  BACKUP_STORAGE_PATH: z.string().default('/var/backups/achidas'),

  // Application Deployment
  DEPLOYMENT_TIMEOUT_MINUTES: z.string().transform(Number).default('30'),
  BUILD_TIMEOUT_MINUTES: z.string().transform(Number).default('15'),
  MAX_CONCURRENT_DEPLOYMENTS: z.string().transform(Number).default('5'),
});

// Validate environment variables
const env = envSchema.parse(process.env);

// Export configuration object
export const appConfig = {
  server: {
    port: env.PORT,
    host: env.HOST,
    environment: env.NODE_ENV,
    env: env.NODE_ENV,
  },
  database: {
    url: env.DATABASE_URL,
  },
  auth: {
    jwtSecret: env.JWT_SECRET,
    jwtExpiresIn: env.JWT_EXPIRES_IN,
  },
  vultr: {
    apiKey: env.VULTR_API_KEY,
    baseUrl: env.VULTR_API_BASE_URL,
  },
  logging: {
    level: env.LOG_LEVEL,
    pretty: env.LOG_PRETTY,
  },
  rateLimit: {
    max: env.RATE_LIMIT_MAX,
    window: env.RATE_LIMIT_WINDOW,
    timeWindow: env.RATE_LIMIT_WINDOW,
  },
  cors: {
    origin: env.CORS_ORIGIN === '*' ? true : env.CORS_ORIGIN.split(',').map(origin => origin.trim()),
    credentials: env.CORS_CREDENTIALS,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'X-Request-ID'],
  },
  monitoring: {
    jaegerEndpoint: env.JAEGER_ENDPOINT,
    metricsPort: env.METRICS_PORT,
    enabled: env.MONITORING_ENABLED,
    intervalSeconds: env.MONITORING_INTERVAL_SECONDS,
    retentionDays: env.METRICS_RETENTION_DAYS,
  },
  circuitBreaker: {
    failureThreshold: env.CIRCUIT_BREAKER_FAILURE_THRESHOLD,
    timeoutSeconds: env.CIRCUIT_BREAKER_TIMEOUT_SECONDS,
  },
  sharedHosting: {
    servers: env.SHARED_HOSTING_SERVERS ? JSON.parse(env.SHARED_HOSTING_SERVERS) : [],
    maxUsersPerServer: env.SHARED_HOSTING_MAX_USERS_PER_SERVER,
    basePort: env.SHARED_HOSTING_BASE_PORT,
    sshBasePort: env.SHARED_HOSTING_SSH_BASE_PORT,
    ftpBasePort: env.SHARED_HOSTING_FTP_BASE_PORT,
  },
  ssh: {
    host: env.SSH_HOST,
    port: env.SSH_PORT,
    username: env.SSH_USERNAME,
    password: env.SSH_PASSWORD,
    privateKeyPath: env.SSH_PRIVATE_KEY_PATH,
    timeout: env.SSH_TIMEOUT,
  },
  payment: {
    gracePeriodDaysDefault: env.GRACE_PERIOD_DAYS_DEFAULT,
    gracePeriodDaysPremium: env.GRACE_PERIOD_DAYS_PREMIUM,
    deletionDelayDays: env.DELETION_DELAY_DAYS,
    checkIntervalHours: env.PAYMENT_CHECK_INTERVAL_HOURS,
  },
  sync: {
    intervalMinutes: env.SYNC_INTERVAL_MINUTES,
    enabled: env.SYNC_ENABLED,
  },
  security: {
    monitoringEnabled: env.SECURITY_MONITORING_ENABLED,
    maxLoginAttempts: env.MAX_LOGIN_ATTEMPTS,
    lockoutDurationMinutes: env.LOCKOUT_DURATION_MINUTES,
    abuseCheckIntervalMinutes: env.ABUSE_CHECK_INTERVAL_MINUTES,
  },
  domain: {
    primary: env.PRIMARY_DOMAIN,
  },
  cloudflare: {
    apiToken: env.CLOUDFLARE_API_TOKEN,
    zoneId: env.CLOUDFLARE_ZONE_ID,
    dnsEnabled: env.CLOUDFLARE_DNS_ENABLED,
  },
  backup: {
    enabled: env.BACKUP_ENABLED,
    intervalHours: env.BACKUP_INTERVAL_HOURS,
    retentionDays: env.BACKUP_RETENTION_DAYS,
    storagePath: env.BACKUP_STORAGE_PATH,
  },
  deployment: {
    timeoutMinutes: env.DEPLOYMENT_TIMEOUT_MINUTES,
    buildTimeoutMinutes: env.BUILD_TIMEOUT_MINUTES,
    maxConcurrentDeployments: env.MAX_CONCURRENT_DEPLOYMENTS,
  },
} as const;

export type AppConfig = typeof appConfig;
