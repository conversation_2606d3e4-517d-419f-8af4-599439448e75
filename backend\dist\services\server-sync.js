"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.serverSync = exports.ServerSyncService = void 0;
const logger_1 = require("../utils/logger");
const config_1 = require("../config");
const shared_hosting_1 = require("../models/shared-hosting");
const shared_hosting_2 = require("./shared-hosting");
class ServerSyncService {
    syncInterval = null;
    isSyncing = false;
    constructor() {
        this.startSyncSchedule();
    }
    startSyncSchedule() {
        if (this.syncInterval) {
            clearInterval(this.syncInterval);
        }
        const intervalMs = config_1.appConfig.sync.intervalMinutes * 60 * 1000;
        this.syncInterval = setInterval(async () => {
            if (!this.isSyncing) {
                this.isSyncing = true;
                try {
                    await this.syncAllServers();
                }
                catch (error) {
                    logger_1.logger.error('Server sync failed:', error);
                }
                finally {
                    this.isSyncing = false;
                }
            }
        }, intervalMs);
        logger_1.logger.info(`Server sync started with ${config_1.appConfig.sync.intervalMinutes}min interval`);
    }
    stopSync() {
        if (this.syncInterval) {
            clearInterval(this.syncInterval);
            this.syncInterval = null;
        }
        logger_1.logger.info('Server sync stopped');
    }
    async syncAllServers() {
        try {
            const servers = config_1.appConfig.sharedHosting.servers;
            if (servers.length < 2) {
                logger_1.logger.debug('Only one server configured, skipping sync');
                return;
            }
            const primaryServer = servers[0];
            const secondaryServers = servers.slice(1);
            logger_1.logger.info(`Starting sync from primary server ${primaryServer.ip_address} to ${secondaryServers.length} secondary servers`);
            for (const secondaryServer of secondaryServers) {
                try {
                    await this.syncServerToServer(primaryServer, secondaryServer);
                }
                catch (error) {
                    logger_1.logger.error(`Failed to sync to server ${secondaryServer.ip_address}:`, error);
                }
            }
            logger_1.logger.info('Server sync completed');
        }
        catch (error) {
            logger_1.logger.error('Failed to sync servers:', error);
            throw error;
        }
    }
    async syncServerToServer(primaryServer, secondaryServer) {
        const primarySSH = new shared_hosting_2.SSHConnection();
        const secondarySSH = new shared_hosting_2.SSHConnection();
        try {
            await primarySSH.connect();
            await secondarySSH.connect();
            const activeUsers = await shared_hosting_1.SharedHostingUser.find({
                server_id: primaryServer.ip_address,
                status: 'active'
            });
            logger_1.logger.info(`Syncing ${activeUsers.length} users from ${primaryServer.ip_address} to ${secondaryServer.ip_address}`);
            for (const user of activeUsers) {
                try {
                    await this.syncUserData(primarySSH, secondarySSH, user, secondaryServer);
                }
                catch (error) {
                    logger_1.logger.error(`Failed to sync user ${user.username}:`, error);
                }
            }
            await this.syncNginxConfigs(primarySSH, secondarySSH);
            await this.syncSSLCertificates(primarySSH, secondarySSH);
            logger_1.logger.info(`Successfully synced ${primaryServer.ip_address} to ${secondaryServer.ip_address}`);
        }
        catch (error) {
            logger_1.logger.error(`Server sync failed between ${primaryServer.ip_address} and ${secondaryServer.ip_address}:`, error);
            throw error;
        }
        finally {
            await primarySSH.disconnect();
            await secondarySSH.disconnect();
        }
    }
    async syncUserData(primarySSH, secondarySSH, user, secondaryServer) {
        try {
            const homeDir = user.home_directory;
            const userExists = await secondarySSH.executeCommand(`id ${user.linux_username} 2>/dev/null || echo "not_found"`);
            if (userExists.includes('not_found')) {
                await this.createUserOnSecondary(secondarySSH, user);
            }
            const rsyncCommand = `rsync -avz --delete -e "ssh -o StrictHostKeyChecking=no -i ${config_1.appConfig.ssh.privateKeyPath}" ${homeDir}/ ${config_1.appConfig.ssh.username}@${secondaryServer.ip_address}:${homeDir}/`;
            await primarySSH.executeCommand(rsyncCommand);
            await secondarySSH.executeCommand(`chown -R ${user.linux_username}:${user.linux_username} ${homeDir}`);
            await this.syncUserServices(primarySSH, secondarySSH, user);
            logger_1.logger.debug(`Synced user data for ${user.username}`);
        }
        catch (error) {
            logger_1.logger.error(`Failed to sync user data for ${user.username}:`, error);
            throw error;
        }
    }
    async createUserOnSecondary(secondarySSH, user) {
        try {
            const createUserCmd = `useradd -m -u ${user.uid || 1000} -s /bin/bash ${user.linux_username}`;
            await secondarySSH.executeCommand(createUserCmd);
            const limitsConfig = `
${user.linux_username} soft cpu ${user.resource_limits.cpu_quota}
${user.linux_username} hard cpu ${user.resource_limits.cpu_quota}
${user.linux_username} soft memlock ${user.resource_limits.memory_max * 1024}
${user.linux_username} hard memlock ${user.resource_limits.memory_max * 1024}
`;
            await secondarySSH.executeCommand(`echo "${limitsConfig}" >> /etc/security/limits.conf`);
            logger_1.logger.debug(`Created user ${user.username} on secondary server`);
        }
        catch (error) {
            logger_1.logger.error(`Failed to create user ${user.username} on secondary server:`, error);
            throw error;
        }
    }
    async syncUserServices(primarySSH, secondarySSH, user) {
        try {
            const serviceDir = `/etc/systemd/system`;
            const userServicePattern = `*${user.linux_username}*`;
            const rsyncServicesCmd = `rsync -avz -e "ssh -o StrictHostKeyChecking=no -i ${config_1.appConfig.ssh.privateKeyPath}" ${serviceDir}/${userServicePattern} ${config_1.appConfig.ssh.username}@${config_1.appConfig.ssh.host}:${serviceDir}/`;
            await primarySSH.executeCommand(rsyncServicesCmd);
            await secondarySSH.executeCommand('systemctl daemon-reload');
            logger_1.logger.debug(`Synced services for user ${user.username}`);
        }
        catch (error) {
            logger_1.logger.error(`Failed to sync services for user ${user.username}:`, error);
        }
    }
    async syncNginxConfigs(primarySSH, secondarySSH) {
        try {
            const nginxSitesDir = '/etc/nginx/sites-available';
            const nginxEnabledDir = '/etc/nginx/sites-enabled';
            const rsyncSitesCmd = `rsync -avz --delete -e "ssh -o StrictHostKeyChecking=no -i ${config_1.appConfig.ssh.privateKeyPath}" ${nginxSitesDir}/ ${config_1.appConfig.ssh.username}@${config_1.appConfig.ssh.host}:${nginxSitesDir}/`;
            await primarySSH.executeCommand(rsyncSitesCmd);
            const rsyncEnabledCmd = `rsync -avz --delete -e "ssh -o StrictHostKeyChecking=no -i ${config_1.appConfig.ssh.privateKeyPath}" ${nginxEnabledDir}/ ${config_1.appConfig.ssh.username}@${config_1.appConfig.ssh.host}:${nginxEnabledDir}/`;
            await primarySSH.executeCommand(rsyncEnabledCmd);
            await secondarySSH.executeCommand('nginx -t && systemctl reload nginx');
            logger_1.logger.debug('Synced NGINX configurations');
        }
        catch (error) {
            logger_1.logger.error('Failed to sync NGINX configurations:', error);
        }
    }
    async syncSSLCertificates(primarySSH, secondarySSH) {
        try {
            const sslDir = '/etc/ssl/certs/achidas';
            await secondarySSH.executeCommand(`mkdir -p ${sslDir}`);
            const rsyncSSLCmd = `rsync -avz --delete -e "ssh -o StrictHostKeyChecking=no -i ${config_1.appConfig.ssh.privateKeyPath}" ${sslDir}/ ${config_1.appConfig.ssh.username}@${config_1.appConfig.ssh.host}:${sslDir}/`;
            await primarySSH.executeCommand(rsyncSSLCmd);
            logger_1.logger.debug('Synced SSL certificates');
        }
        catch (error) {
            logger_1.logger.error('Failed to sync SSL certificates:', error);
        }
    }
    async triggerManualSync() {
        if (this.isSyncing) {
            throw new Error('Sync already in progress');
        }
        this.isSyncing = true;
        try {
            await this.syncAllServers();
        }
        finally {
            this.isSyncing = false;
        }
    }
    getSyncStatus() {
        const nextSync = this.syncInterval ?
            new Date(Date.now() + config_1.appConfig.sync.intervalMinutes * 60 * 1000) : null;
        return {
            isActive: this.syncInterval !== null,
            lastSync: null,
            nextSync
        };
    }
}
exports.ServerSyncService = ServerSyncService;
exports.serverSync = new ServerSyncService();
//# sourceMappingURL=server-sync.js.map