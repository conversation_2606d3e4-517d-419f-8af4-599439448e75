export * from './user';
export * from './application';
export * from './server';
export * from './shared-hosting';
export interface PaginationOptions {
    page?: number;
    limit?: number;
    sort?: string;
    order?: 'asc' | 'desc';
}
export interface PaginatedResponse<T> {
    data: T[];
    pagination: {
        page: number;
        limit: number;
        total: number;
        pages: number;
        has_next: boolean;
        has_prev: boolean;
    };
}
export interface JwtPayload {
    sub: string;
    email: string;
    username: string;
    role: string;
    iat: number;
    exp: number;
}
export interface RequestContext {
    user?: JwtPayload;
    requestId: string;
    traceId: string;
}
export declare enum ErrorCode {
    VALIDATION_ERROR = "VALIDATION_ERROR",
    AUTHENTICATION_ERROR = "AUTHENTICATION_ERROR",
    AUTHORIZATION_ERROR = "AUTHORIZATION_ERROR",
    NOT_FOUND = "NOT_FOUND",
    CONFLICT = "CONFLICT",
    RATE_LIMIT_EXCEEDED = "RATE_LIMIT_EXCEEDED",
    INTERNAL_ERROR = "INTERNAL_ERROR",
    SERVICE_UNAVAILABLE = "SERVICE_UNAVAILABLE",
    BAD_REQUEST = "BAD_REQUEST"
}
export declare class AppError extends Error {
    code: ErrorCode;
    message: string;
    statusCode: number;
    details?: any | undefined;
    constructor(code: ErrorCode, message: string, statusCode?: number, details?: any | undefined);
}
//# sourceMappingURL=index.d.ts.map