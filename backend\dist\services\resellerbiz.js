"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResellerBizService = void 0;
exports.getResellerBizService = getResellerBizService;
const axios_1 = __importDefault(require("axios"));
const config_1 = require("../config");
const logger_1 = require("../utils/logger");
class ResellerBizService {
    client;
    authUserId;
    apiKey;
    constructor() {
        this.authUserId = config_1.appConfig.resellerBiz.authUserId;
        this.apiKey = config_1.appConfig.resellerBiz.apiKey;
        this.client = axios_1.default.create({
            baseURL: 'https://domaincheck.httpapi.com/api',
            timeout: 30000,
            headers: {
                'Content-Type': 'application/json',
            },
        });
        this.client.interceptors.request.use((config) => {
            logger_1.logger.debug(`ResellerBiz API Request: ${config.method?.toUpperCase()} ${config.url}`);
            return config;
        }, (error) => {
            logger_1.logger.error('ResellerBiz API Request Error:', error);
            return Promise.reject(error);
        });
        this.client.interceptors.response.use((response) => {
            logger_1.logger.debug(`ResellerBiz API Response: ${response.status} ${response.config.url}`);
            return response;
        }, (error) => {
            logger_1.logger.error('ResellerBiz API Response Error:', {
                status: error.response?.status,
                data: error.response?.data,
                url: error.config?.url,
            });
            return Promise.reject(error);
        });
    }
    async checkDomainAvailability(request) {
        try {
            logger_1.logger.info('Starting domain availability check', {
                domains: request.domains,
                tlds: request.tlds,
                authUserId: this.authUserId ? 'SET' : 'NOT_SET',
                apiKey: this.apiKey ? 'SET' : 'NOT_SET'
            });
            const params = new URLSearchParams();
            params.append('auth-userid', this.authUserId);
            params.append('api-key', this.apiKey);
            request.domains.forEach(domain => {
                params.append('domain-name', domain);
            });
            request.tlds.forEach(tld => {
                params.append('tlds', tld);
            });
            const url = '/domains/available.json?' + params.toString();
            logger_1.logger.info('Making ResellerBiz API request', {
                fullUrl: this.client.defaults.baseURL + url,
                params: params.toString()
            });
            const response = await this.client.get(url);
            logger_1.logger.info(`Domain availability check successful`, {
                status: response.status,
                domains: request.domains.length,
                tlds: request.tlds.length,
                responseKeys: Object.keys(response.data || {})
            });
            return response.data;
        }
        catch (error) {
            logger_1.logger.error('Failed to check domain availability:', {
                message: error.message,
                status: error.response?.status,
                statusText: error.response?.statusText,
                responseData: error.response?.data,
                requestUrl: error.config?.url,
                fullUrl: this.client.defaults.baseURL + (error.config?.url || ''),
                requestParams: error.config?.params
            });
            throw new Error(`Failed to check domain availability: ${error.message}`);
        }
    }
    async transferDomain(request) {
        try {
            const params = new URLSearchParams();
            params.append('auth-userid', this.authUserId);
            params.append('api-key', this.apiKey);
            params.append('domain-name', request.domainName);
            params.append('customer-id', request.customerId.toString());
            params.append('reg-contact-id', request.regContactId.toString());
            params.append('admin-contact-id', request.adminContactId.toString());
            params.append('tech-contact-id', request.techContactId.toString());
            params.append('billing-contact-id', request.billingContactId.toString());
            params.append('invoice-option', request.invoiceOption);
            params.append('auto-renew', request.autoRenew.toString());
            if (request.authCode) {
                params.append('auth-code', request.authCode);
            }
            if (request.purchasePrivacy !== undefined) {
                params.append('purchase-privacy', request.purchasePrivacy.toString());
            }
            if (request.protectPrivacy !== undefined) {
                params.append('protect-privacy', request.protectPrivacy.toString());
            }
            if (request.nameServers && request.nameServers.length > 0) {
                request.nameServers.forEach(ns => {
                    params.append('ns', ns);
                });
            }
            if (request.attributes) {
                let attrIndex = 1;
                Object.entries(request.attributes).forEach(([key, value]) => {
                    params.append(`attr-name${attrIndex}`, key);
                    params.append(`attr-value${attrIndex}`, value);
                    attrIndex++;
                });
            }
            if (request.purchasePremiumDns !== undefined) {
                params.append('purchase-premium-dns', request.purchasePremiumDns.toString());
            }
            const response = await this.client.post('/domains/transfer.json', params.toString(), {
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
            });
            logger_1.logger.info(`Initiated domain transfer for ${request.domainName}`);
            return response.data;
        }
        catch (error) {
            logger_1.logger.error(`Failed to transfer domain ${request.domainName}:`, error);
            throw new Error('Failed to transfer domain');
        }
    }
    async whoisLookup(request) {
        try {
            logger_1.logger.info(`Performing WHOIS lookup for ${request.domainName}`);
            const result = {
                domain: request.domainName,
                registrar: 'Unknown',
                registrant: {
                    name: 'WHOIS data not available',
                },
                nameServers: [],
                status: ['WHOIS lookup not implemented'],
            };
            logger_1.logger.warn('WHOIS lookup is not fully implemented - returning placeholder data');
            return result;
        }
        catch (error) {
            logger_1.logger.error(`Failed to perform WHOIS lookup for ${request.domainName}:`, error);
            throw new Error('Failed to perform WHOIS lookup');
        }
    }
}
exports.ResellerBizService = ResellerBizService;
let resellerBizServiceInstance = null;
function getResellerBizService() {
    if (!resellerBizServiceInstance) {
        resellerBizServiceInstance = new ResellerBizService();
    }
    return resellerBizServiceInstance;
}
//# sourceMappingURL=resellerbiz.js.map