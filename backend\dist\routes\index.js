"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.registerRoutes = registerRoutes;
const auth_routes_1 = require("./auth.routes");
const vultr_routes_1 = require("./vultr.routes");
const server_routes_1 = require("./server.routes");
const domain_routes_1 = require("./domain.routes");
const response_1 = require("../utils/response");
async function registerRoutes(fastify) {
    try {
        console.log('Registering health check route...');
        fastify.get('/health', async (_request, reply) => {
            return response_1.ResponseHelper.success(reply, {
                status: 'healthy',
                timestamp: new Date().toISOString(),
                uptime: process.uptime(),
                version: process.env['npm_package_version'] || '1.0.0',
            });
        });
        console.log('Registering API v1 routes...');
        fastify.register(async function (fastify) {
            console.log('Registering auth routes...');
            await fastify.register(auth_routes_1.authRoutes, { prefix: '/auth' });
            console.log('Registering vultr routes...');
            await fastify.register(vultr_routes_1.vultrRoutes, { prefix: '/compute' });
            console.log('Registering server routes...');
            await fastify.register(server_routes_1.serverRoutes, { prefix: '/services' });
            console.log('Registering domain routes...');
            await fastify.register(domain_routes_1.domainRoutes, { prefix: '/domains' });
            console.log('Domain routes registered successfully');
        }, { prefix: '/api/v1' });
        console.log('Setting up 404 handler...');
        fastify.setNotFoundHandler(async (request, reply) => {
            return response_1.ResponseHelper.notFound(reply, `Route ${request.method} ${request.url} not found`);
        });
        console.log('All routes registered successfully');
    }
    catch (error) {
        console.error('Error registering routes:', error);
        throw error;
    }
}
//# sourceMappingURL=index.js.map