"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.registerRoutes = registerRoutes;
const auth_routes_1 = require("./auth.routes");
const vultr_routes_1 = require("./vultr.routes");
const server_routes_1 = require("./server.routes");
const response_1 = require("../utils/response");
async function registerRoutes(fastify) {
    fastify.get('/health', async (_request, reply) => {
        return response_1.ResponseHelper.success(reply, {
            status: 'healthy',
            timestamp: new Date().toISOString(),
            uptime: process.uptime(),
            version: process.env['npm_package_version'] || '1.0.0',
        });
    });
    fastify.register(async function (fastify) {
        await fastify.register(auth_routes_1.authRoutes, { prefix: '/auth' });
        await fastify.register(vultr_routes_1.vultrRoutes, { prefix: '/compute' });
        await fastify.register(server_routes_1.serverRoutes, { prefix: '/services' });
    }, { prefix: '/api/v1' });
    fastify.setNotFoundHandler(async (request, reply) => {
        return response_1.ResponseHelper.notFound(reply, `Route ${request.method} ${request.url} not found`);
    });
}
//# sourceMappingURL=index.js.map