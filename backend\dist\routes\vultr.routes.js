"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.vultrRoutes = vultrRoutes;
const vultr_controller_1 = require("../controllers/vultr.controller");
const auth_1 = require("../middleware/auth");
async function vultrRoutes(fastify) {
    fastify.addHook('preHandler', auth_1.authMiddleware);
    fastify.get('/fetch-servers', vultr_controller_1.getServersController);
    fastify.get('/servers/:id', vultr_controller_1.getServerController);
    fastify.register(async function (fastify) {
        fastify.addHook('preHandler', auth_1.adminMiddleware);
        fastify.post('/servers', vultr_controller_1.createServerController);
        fastify.delete('/servers/:id', vultr_controller_1.deleteServerController);
        fastify.post('/servers/:id/start', vultr_controller_1.startServerController);
        fastify.post('/servers/:id/stop', vultr_controller_1.stopServerController);
        fastify.post('/servers/:id/reboot', vultr_controller_1.rebootServerController);
    });
    fastify.get('/plans', vultr_controller_1.getPlansController);
    fastify.get('/regions', vultr_controller_1.getRegionsController);
    fastify.get('/os', vultr_controller_1.getOperatingSystemsController);
    fastify.register(async function (fastify) {
        fastify.addHook('preHandler', auth_1.adminMiddleware);
        fastify.get('/account', vultr_controller_1.getAccountInfoController);
        fastify.get('/billing', vultr_controller_1.getBillingController);
    });
    fastify.get('/block-storage', vultr_controller_1.getBlockStorageController);
    fastify.register(async function (fastify) {
        fastify.addHook('preHandler', auth_1.adminMiddleware);
        fastify.post('/block-storage', vultr_controller_1.createBlockStorageController);
        fastify.delete('/block-storage/:id', vultr_controller_1.deleteBlockStorageController);
        fastify.post('/block-storage/:id/attach', vultr_controller_1.attachBlockStorageController);
        fastify.post('/block-storage/:id/detach', vultr_controller_1.detachBlockStorageController);
    });
    fastify.get('/ssh-keys', vultr_controller_1.getSSHKeysController);
    fastify.get('/snapshots', vultr_controller_1.getSnapshotsController);
    fastify.get('/load-balancers', vultr_controller_1.getLoadBalancersController);
}
//# sourceMappingURL=vultr.routes.js.map