"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.execAsync = void 0;
exports.execCommand = execCommand;
exports.execCommandSafe = execCommandSafe;
const child_process_1 = require("child_process");
const util_1 = require("util");
exports.execAsync = (0, util_1.promisify)(child_process_1.exec);
async function execCommand(command) {
    try {
        const result = await (0, exports.execAsync)(command);
        return {
            stdout: result.stdout,
            stderr: result.stderr
        };
    }
    catch (error) {
        throw new Error(`Command failed: ${command}\nError: ${error.message}`);
    }
}
async function execCommandSafe(command) {
    try {
        return await execCommand(command);
    }
    catch (error) {
        return null;
    }
}
//# sourceMappingURL=exec.js.map