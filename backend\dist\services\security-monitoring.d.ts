export interface SecurityAlert {
    id: string;
    type: 'unauthorized_access' | 'resource_abuse' | 'file_permission' | 'process_violation' | 'network_anomaly';
    severity: 'low' | 'medium' | 'high' | 'critical';
    user_id: string;
    username: string;
    description: string;
    details: Record<string, any>;
    timestamp: string;
    resolved: boolean;
    actions_taken: string[];
}
export interface UserIsolationStatus {
    user_id: string;
    username: string;
    linux_username: string;
    isolation_status: 'secure' | 'warning' | 'compromised';
    file_permissions_ok: boolean;
    process_limits_ok: boolean;
    network_restrictions_ok: boolean;
    resource_usage: {
        cpu_percent: number;
        memory_percent: number;
        disk_usage_mb: number;
        network_usage_mb: number;
    };
    last_checked: string;
    violations: string[];
}
export interface SecurityMetrics {
    total_users: number;
    secure_users: number;
    warning_users: number;
    compromised_users: number;
    active_alerts: number;
    resolved_alerts_24h: number;
    system_health_score: number;
}
declare class SecurityMonitoringService {
    private readonly SHARED_SERVER_IP;
    private readonly alerts;
    monitorUserIsolation(): Promise<UserIsolationStatus[]>;
    private checkUserIsolation;
    private checkFilePermissions;
    private checkProcessLimits;
    private checkNetworkRestrictions;
    private getUserResourceUsage;
    private generateSecurityAlert;
    private autoRemediate;
    private getSharedHostingUsers;
    getSecurityMetrics(): Promise<SecurityMetrics>;
    getAlerts(resolved?: boolean): SecurityAlert[];
    resolveAlert(alertId: string, resolution: string): Promise<void>;
}
export declare function getSecurityMonitoringService(): SecurityMonitoringService;
export {};
//# sourceMappingURL=security-monitoring.d.ts.map