import { logger } from '../utils/logger';
import { appConfig } from '../config';
import { SharedHostingUser, SharedHostingApplication } from '../models/shared-hosting';
import { SSHConnection } from './shared-hosting';
import { databaseServices } from './database-services';

interface BackupConfig {
  type: 'full' | 'incremental' | 'database' | 'files';
  schedule: string; // cron format
  retention: number; // days
  compression: boolean;
  encryption: boolean;
  destination: 'local' | 's3' | 'vultr-object-storage';
}

interface BackupJob {
  id: string;
  userId: string;
  type: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  startTime: Date;
  endTime?: Date;
  size?: number;
  location: string;
  error?: string;
}

export class BackupRecoveryService {
  private backupJobs: Map<string, BackupJob> = new Map();
  private scheduledBackups: Map<string, NodeJS.Timeout> = new Map();

  constructor() {
    this.initializeScheduledBackups();
  }

  // Initialize scheduled backups
  async initializeScheduledBackups(): Promise<void> {
    try {
      // Schedule nightly full server backup
      this.scheduleBackup('server-full', {
        type: 'full',
        schedule: '0 2 * * *', // 2 AM daily
        retention: 7,
        compression: true,
        encryption: true,
        destination: 'vultr-object-storage'
      });

      // Schedule user backups for premium plans
      const premiumUsers = await SharedHostingUser.find({ 
        plan_type: { $in: ['premium', 'enterprise'] },
        status: 'active'
      });

      for (const user of premiumUsers) {
        this.scheduleUserBackup(user.user_id, {
          type: 'full',
          schedule: '0 3 * * *', // 3 AM daily
          retention: 30,
          compression: true,
          encryption: true,
          destination: 'vultr-object-storage'
        });
      }

      logger.info('Initialized scheduled backups');
    } catch (error) {
      logger.error('Failed to initialize scheduled backups:', error);
    }
  }

  // Schedule backup job
  scheduleBackup(jobId: string, config: BackupConfig): void {
    // Clear existing schedule if any
    if (this.scheduledBackups.has(jobId)) {
      clearInterval(this.scheduledBackups.get(jobId)!);
    }

    // Parse cron schedule (simplified - in production use a proper cron library)
    const interval = this.parseCronToInterval(config.schedule);
    
    const timer = setInterval(async () => {
      try {
        if (jobId.startsWith('server-')) {
          await this.performServerBackup(config);
        } else {
          await this.performUserBackup(jobId, config);
        }
      } catch (error) {
        logger.error(`Scheduled backup failed for ${jobId}:`, error);
      }
    }, interval);

    this.scheduledBackups.set(jobId, timer);
    logger.info(`Scheduled backup job ${jobId} with interval ${interval}ms`);
  }

  // Schedule user-specific backup
  scheduleUserBackup(userId: string, config: BackupConfig): void {
    this.scheduleBackup(`user-${userId}`, config);
  }

  // Perform server-wide backup
  async performServerBackup(config: BackupConfig): Promise<string> {
    const jobId = this.generateJobId();
    const job: BackupJob = {
      id: jobId,
      userId: 'system',
      type: config.type,
      status: 'running',
      startTime: new Date(),
      location: ''
    };

    this.backupJobs.set(jobId, job);

    try {
      logger.info(`Starting server backup job ${jobId}`);

      const ssh = new SSHConnection();
      await ssh.connect();

      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupDir = `/tmp/server-backup-${timestamp}`;
      const backupFile = `server-backup-${timestamp}.tar.gz`;

      // Create backup directory
      await ssh.exec(`mkdir -p ${backupDir}`);

      // Backup system configurations
      await ssh.exec(`cp -r /etc/nginx ${backupDir}/`);
      await ssh.exec(`cp -r /etc/systemd/system ${backupDir}/`);
      await ssh.exec(`cp /etc/security/limits.conf ${backupDir}/`);

      // Backup user data
      const users = await SharedHostingUser.find({ status: 'active' });
      for (const user of users) {
        const userBackupDir = `${backupDir}/users/${user.linux_username}`;
        await ssh.exec(`mkdir -p ${userBackupDir}`);
        
        // Backup user files (excluding large files)
        await ssh.exec(`rsync -av --exclude='*.log' --exclude='node_modules' --exclude='.git' ${user.home_directory}/ ${userBackupDir}/`);
        
        // Backup user databases
        await this.backupUserDatabases(ssh, user, userBackupDir);
      }

      // Create compressed archive
      const archivePath = `/tmp/${backupFile}`;
      if (config.compression) {
        await ssh.exec(`tar -czf ${archivePath} -C ${backupDir} .`);
      } else {
        await ssh.exec(`tar -cf ${archivePath} -C ${backupDir} .`);
      }

      // Encrypt if required
      if (config.encryption) {
        await this.encryptBackup(ssh, archivePath);
      }

      // Upload to storage
      const uploadLocation = await this.uploadBackup(ssh, archivePath, config.destination);

      // Cleanup local files
      await ssh.exec(`rm -rf ${backupDir} ${archivePath}`);
      await ssh.disconnect();

      // Update job status
      job.status = 'completed';
      job.endTime = new Date();
      job.location = uploadLocation;

      // Get backup size
      const sizeResult = await ssh.exec(`ls -la ${archivePath} | awk '{print $5}'`);
      job.size = parseInt(sizeResult.trim()) || 0;

      logger.info(`Server backup completed: ${uploadLocation}`);
      return uploadLocation;

    } catch (error) {
      job.status = 'failed';
      job.endTime = new Date();
      job.error = error instanceof Error ? error.message : 'Unknown error';
      
      logger.error(`Server backup failed for job ${jobId}:`, error);
      throw error;
    }
  }

  // Perform user-specific backup
  async performUserBackup(userId: string, config: BackupConfig): Promise<string> {
    const jobId = this.generateJobId();
    const job: BackupJob = {
      id: jobId,
      userId,
      type: config.type,
      status: 'running',
      startTime: new Date(),
      location: ''
    };

    this.backupJobs.set(jobId, job);

    try {
      const user = await SharedHostingUser.findOne({ user_id: userId });
      if (!user) {
        throw new Error('User not found');
      }

      logger.info(`Starting user backup job ${jobId} for ${user.username}`);

      const ssh = new SSHConnection();
      await ssh.connect();

      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupDir = `/tmp/user-backup-${user.linux_username}-${timestamp}`;
      const backupFile = `user-backup-${user.linux_username}-${timestamp}.tar.gz`;

      // Create backup directory
      await ssh.exec(`mkdir -p ${backupDir}`);

      // Backup user files
      await ssh.exec(`rsync -av --exclude='*.log' --exclude='node_modules' --exclude='.git' ${user.home_directory}/ ${backupDir}/files/`);

      // Backup user databases
      await this.backupUserDatabases(ssh, user, `${backupDir}/databases`);

      // Backup user configurations
      await ssh.exec(`mkdir -p ${backupDir}/config`);
      await ssh.exec(`cp /etc/systemd/system/*${user.linux_username}* ${backupDir}/config/ 2>/dev/null || true`);

      // Create compressed archive
      const archivePath = `/tmp/${backupFile}`;
      if (config.compression) {
        await ssh.exec(`tar -czf ${archivePath} -C ${backupDir} .`);
      } else {
        await ssh.exec(`tar -cf ${archivePath} -C ${backupDir} .`);
      }

      // Encrypt if required
      if (config.encryption) {
        await this.encryptBackup(ssh, archivePath);
      }

      // Upload to storage
      const uploadLocation = await this.uploadBackup(ssh, archivePath, config.destination, `users/${user.linux_username}`);

      // Cleanup local files
      await ssh.exec(`rm -rf ${backupDir} ${archivePath}`);
      await ssh.disconnect();

      // Update job status
      job.status = 'completed';
      job.endTime = new Date();
      job.location = uploadLocation;

      logger.info(`User backup completed for ${user.username}: ${uploadLocation}`);
      return uploadLocation;

    } catch (error) {
      job.status = 'failed';
      job.endTime = new Date();
      job.error = error instanceof Error ? error.message : 'Unknown error';
      
      logger.error(`User backup failed for job ${jobId}:`, error);
      throw error;
    }
  }

  // Backup user databases
  async backupUserDatabases(ssh: SSHConnection, user: any, backupDir: string): Promise<void> {
    try {
      await ssh.exec(`mkdir -p ${backupDir}`);

      // Get user's databases
      const mysqlDbs = await ssh.exec(`mysql -u root -p${appConfig.database.mysqlRootPassword} -e "SHOW DATABASES LIKE '${user.linux_username}_%';" --skip-column-names`);
      
      if (mysqlDbs.trim()) {
        const dbNames = mysqlDbs.trim().split('\n');
        for (const dbName of dbNames) {
          await ssh.exec(`mysqldump -u root -p${appConfig.database.mysqlRootPassword} ${dbName} > ${backupDir}/${dbName}.sql`);
        }
      }

      // Backup PostgreSQL databases
      const pgDbs = await ssh.exec(`sudo -u postgres psql -c "SELECT datname FROM pg_database WHERE datname LIKE '${user.linux_username}_%';" --tuples-only --no-align`);
      
      if (pgDbs.trim()) {
        const dbNames = pgDbs.trim().split('\n');
        for (const dbName of dbNames) {
          await ssh.exec(`sudo -u postgres pg_dump ${dbName} > ${backupDir}/${dbName}.sql`);
        }
      }

    } catch (error) {
      logger.error(`Failed to backup databases for user ${user.username}:`, error);
    }
  }

  // Encrypt backup file
  async encryptBackup(ssh: SSHConnection, filePath: string): Promise<void> {
    try {
      const encryptedPath = `${filePath}.enc`;
      await ssh.exec(`openssl enc -aes-256-cbc -salt -in ${filePath} -out ${encryptedPath} -k ${appConfig.backup.encryptionKey}`);
      await ssh.exec(`mv ${encryptedPath} ${filePath}`);
    } catch (error) {
      logger.error(`Failed to encrypt backup ${filePath}:`, error);
      throw error;
    }
  }

  // Upload backup to storage
  async uploadBackup(ssh: SSHConnection, filePath: string, destination: string, prefix?: string): Promise<string> {
    try {
      const fileName = filePath.split('/').pop()!;
      const remotePath = prefix ? `${prefix}/${fileName}` : fileName;

      switch (destination) {
        case 'vultr-object-storage':
          // Upload to Vultr Object Storage using s3cmd or similar
          await ssh.exec(`s3cmd put ${filePath} s3://${appConfig.backup.bucketName}/${remotePath}`);
          return `s3://${appConfig.backup.bucketName}/${remotePath}`;
          
        case 's3':
          // Upload to AWS S3
          await ssh.exec(`aws s3 cp ${filePath} s3://${appConfig.backup.bucketName}/${remotePath}`);
          return `s3://${appConfig.backup.bucketName}/${remotePath}`;
          
        case 'local':
          // Move to local backup directory
          const localPath = `/var/backups/achidas/${remotePath}`;
          await ssh.exec(`mkdir -p $(dirname ${localPath})`);
          await ssh.exec(`mv ${filePath} ${localPath}`);
          return localPath;
          
        default:
          throw new Error(`Unsupported backup destination: ${destination}`);
      }
    } catch (error) {
      logger.error(`Failed to upload backup to ${destination}:`, error);
      throw error;
    }
  }

  // Restore from backup
  async restoreFromBackup(backupLocation: string, userId?: string): Promise<void> {
    const ssh = new SSHConnection();
    
    try {
      await ssh.connect();
      
      logger.info(`Starting restore from backup: ${backupLocation}`);

      // Download backup file
      const localPath = `/tmp/restore-${Date.now()}.tar.gz`;
      await this.downloadBackup(ssh, backupLocation, localPath);

      // Decrypt if needed
      if (backupLocation.endsWith('.enc')) {
        await this.decryptBackup(ssh, localPath);
      }

      // Extract backup
      const extractDir = `/tmp/restore-${Date.now()}`;
      await ssh.exec(`mkdir -p ${extractDir}`);
      await ssh.exec(`tar -xzf ${localPath} -C ${extractDir}`);

      if (userId) {
        // User-specific restore
        await this.restoreUserData(ssh, extractDir, userId);
      } else {
        // Full server restore
        await this.restoreServerData(ssh, extractDir);
      }

      // Cleanup
      await ssh.exec(`rm -rf ${extractDir} ${localPath}`);
      await ssh.disconnect();

      logger.info('Restore completed successfully');

    } catch (error) {
      logger.error('Restore failed:', error);
      throw error;
    }
  }

  // Download backup from storage
  async downloadBackup(ssh: SSHConnection, backupLocation: string, localPath: string): Promise<void> {
    if (backupLocation.startsWith('s3://')) {
      await ssh.exec(`s3cmd get ${backupLocation} ${localPath}`);
    } else if (backupLocation.startsWith('/')) {
      await ssh.exec(`cp ${backupLocation} ${localPath}`);
    } else {
      throw new Error(`Unsupported backup location: ${backupLocation}`);
    }
  }

  // Decrypt backup file
  async decryptBackup(ssh: SSHConnection, filePath: string): Promise<void> {
    const decryptedPath = filePath.replace('.enc', '');
    await ssh.exec(`openssl enc -aes-256-cbc -d -in ${filePath} -out ${decryptedPath} -k ${appConfig.backup.encryptionKey}`);
    await ssh.exec(`mv ${decryptedPath} ${filePath}`);
  }

  // Restore user data
  async restoreUserData(ssh: SSHConnection, extractDir: string, userId: string): Promise<void> {
    const user = await SharedHostingUser.findOne({ user_id: userId });
    if (!user) {
      throw new Error('User not found');
    }

    // Restore files
    await ssh.exec(`rsync -av ${extractDir}/files/ ${user.home_directory}/`);
    await ssh.exec(`chown -R ${user.linux_username}:${user.linux_username} ${user.home_directory}`);

    // Restore databases
    const dbDir = `${extractDir}/databases`;
    const sqlFiles = await ssh.exec(`find ${dbDir} -name "*.sql" 2>/dev/null || echo ""`);
    
    if (sqlFiles.trim()) {
      const files = sqlFiles.trim().split('\n');
      for (const file of files) {
        const dbName = file.split('/').pop()!.replace('.sql', '');
        if (dbName.startsWith(user.linux_username)) {
          await ssh.exec(`mysql -u root -p${appConfig.database.mysqlRootPassword} ${dbName} < ${file}`);
        }
      }
    }
  }

  // Restore server data
  async restoreServerData(ssh: SSHConnection, extractDir: string): Promise<void> {
    // Restore system configurations
    await ssh.exec(`cp -r ${extractDir}/nginx/* /etc/nginx/`);
    await ssh.exec(`cp -r ${extractDir}/system/* /etc/systemd/system/`);
    
    // Reload services
    await ssh.exec(`systemctl daemon-reload`);
    await ssh.exec(`nginx -t && systemctl reload nginx`);
  }

  // Parse cron schedule to interval (simplified)
  private parseCronToInterval(cronSchedule: string): number {
    // This is a simplified parser - in production use a proper cron library
    if (cronSchedule === '0 2 * * *') return 24 * 60 * 60 * 1000; // Daily at 2 AM
    if (cronSchedule === '0 3 * * *') return 24 * 60 * 60 * 1000; // Daily at 3 AM
    return 24 * 60 * 60 * 1000; // Default to daily
  }

  // Generate unique job ID
  private generateJobId(): string {
    return `backup_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Get backup job status
  getBackupJob(jobId: string): BackupJob | undefined {
    return this.backupJobs.get(jobId);
  }

  // List backup jobs
  listBackupJobs(userId?: string): BackupJob[] {
    const jobs = Array.from(this.backupJobs.values());
    return userId ? jobs.filter(job => job.userId === userId) : jobs;
  }

  // Cleanup old backups
  async cleanupOldBackups(retentionDays: number): Promise<void> {
    const ssh = new SSHConnection();
    
    try {
      await ssh.connect();
      
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - retentionDays);
      
      // Cleanup local backups
      await ssh.exec(`find /var/backups/achidas -type f -mtime +${retentionDays} -delete`);
      
      // Cleanup remote backups (would need specific implementation for each storage provider)
      
      logger.info(`Cleaned up backups older than ${retentionDays} days`);
      
    } catch (error) {
      logger.error('Failed to cleanup old backups:', error);
    } finally {
      await ssh.disconnect();
    }
  }
}

// Export singleton instance
export const backupRecovery = new BackupRecoveryService();
