export declare class NginxManagerService {
    private nginxSitesAvailable;
    private nginxSitesEnabled;
    private nginxMainConfig;
    private sslCertDir;
    createVirtualHost(userId: string, applicationId: string, config: {
        subdomain: string;
        domain?: string;
        port: number;
        type: 'static-website' | 'web-service';
        sslEnabled?: boolean;
        customConfig?: string;
    }): Promise<void>;
    removeVirtualHost(userId: string, applicationId: string): Promise<void>;
    private generateNginxConfig;
    private generateStaticSiteConfig;
    private generateWebServiceConfig;
    private generateSSLConfig;
    private generateHTTPSRedirectConfig;
    updateVirtualHost(userId: string, applicationId: string, updates: any): Promise<void>;
    getNginxStatus(): Promise<any>;
    reloadNginx(): Promise<void>;
}
export declare const nginxManager: NginxManagerService;
//# sourceMappingURL=nginx-manager.d.ts.map