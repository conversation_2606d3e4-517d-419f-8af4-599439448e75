"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createSharedUserController = createSharedUserController;
exports.getSharedUserController = getSharedUserController;
exports.createApplicationController = createApplicationController;
exports.deployApplicationController = deployApplicationController;
exports.getServerStatusController = getServerStatusController;
exports.listServerUsersController = listServerUsersController;
exports.toggleUserStatusController = toggleUserStatusController;
exports.getUserFileStructureController = getUserFileStructureController;
const response_1 = require("../utils/response");
const logger_1 = require("../utils/logger");
const shared_hosting_1 = require("../services/shared-hosting");
async function createSharedUserController(request, reply) {
    try {
        if (!request.user) {
            return response_1.ResponseHelper.unauthorized(reply, 'Authentication required');
        }
        const userData = request.body;
        if (!userData.user_id || !userData.username || !userData.plan) {
            return response_1.ResponseHelper.validationError(reply, 'Missing required fields: user_id, username, plan');
        }
        const sharedUser = await (0, shared_hosting_1.getSharedHostingService)().createUser(userData);
        logger_1.logger.info(`Created shared hosting user: ${userData.username} on server: ${sharedUser.server_id}`);
        return response_1.ResponseHelper.success(reply, sharedUser, 201);
    }
    catch (error) {
        logger_1.logger.error('Create shared user controller error:', error);
        if (error instanceof Error) {
            if (error.message.includes('already exists')) {
                return response_1.ResponseHelper.validationError(reply, error.message);
            }
            if (error.message.includes('server capacity')) {
                return response_1.ResponseHelper.validationError(reply, 'No available server capacity for new users');
            }
        }
        return response_1.ResponseHelper.internalError(reply, 'Failed to create shared hosting user');
    }
}
async function getSharedUserController(request, reply) {
    try {
        if (!request.user) {
            return response_1.ResponseHelper.unauthorized(reply, 'Authentication required');
        }
        const { userId } = request.params;
        const sharedUser = await (0, shared_hosting_1.getSharedHostingService)().getUser(userId);
        if (!sharedUser) {
            return response_1.ResponseHelper.notFound(reply, 'Shared hosting user not found');
        }
        logger_1.logger.info(`Retrieved shared hosting user: ${userId}`);
        return response_1.ResponseHelper.success(reply, sharedUser);
    }
    catch (error) {
        logger_1.logger.error('Get shared user controller error:', error);
        return response_1.ResponseHelper.internalError(reply, 'Failed to fetch shared hosting user');
    }
}
async function createApplicationController(request, reply) {
    try {
        if (!request.user) {
            return response_1.ResponseHelper.unauthorized(reply, 'Authentication required');
        }
        const { userId } = request.params;
        const appData = request.body;
        if (!appData.name || !appData.type) {
            return response_1.ResponseHelper.validationError(reply, 'Missing required fields: name, type');
        }
        const application = await (0, shared_hosting_1.getSharedHostingService)().createApplication(userId, appData);
        logger_1.logger.info(`Created application: ${appData.name} for user: ${userId}`);
        return response_1.ResponseHelper.success(reply, application, 201);
    }
    catch (error) {
        logger_1.logger.error('Create application controller error:', error);
        if (error instanceof Error) {
            if (error.message.includes('not found')) {
                return response_1.ResponseHelper.notFound(reply, 'Shared hosting user not found');
            }
            if (error.message.includes('already exists')) {
                return response_1.ResponseHelper.validationError(reply, error.message);
            }
        }
        return response_1.ResponseHelper.internalError(reply, 'Failed to create application');
    }
}
async function deployApplicationController(request, reply) {
    try {
        if (!request.user) {
            return response_1.ResponseHelper.unauthorized(reply, 'Authentication required');
        }
        const { userId, appId } = request.params;
        const result = await (0, shared_hosting_1.getSharedHostingService)().deployApplication(userId, appId);
        logger_1.logger.info(`Deployed application: ${appId} for user: ${userId}`);
        return response_1.ResponseHelper.success(reply, result);
    }
    catch (error) {
        logger_1.logger.error('Deploy application controller error:', error);
        if (error instanceof Error) {
            if (error.message.includes('not found')) {
                return response_1.ResponseHelper.notFound(reply, 'Application or user not found');
            }
        }
        return response_1.ResponseHelper.internalError(reply, 'Failed to deploy application');
    }
}
async function getServerStatusController(request, reply) {
    try {
        if (!request.user) {
            return response_1.ResponseHelper.unauthorized(reply, 'Authentication required');
        }
        const { serverId } = request.params;
        const status = await (0, shared_hosting_1.getSharedHostingService)().getServerStatus(serverId);
        logger_1.logger.info(`Retrieved server status: ${serverId}`);
        return response_1.ResponseHelper.success(reply, status);
    }
    catch (error) {
        logger_1.logger.error('Get server status controller error:', error);
        return response_1.ResponseHelper.internalError(reply, 'Failed to fetch server status');
    }
}
async function listServerUsersController(request, reply) {
    try {
        if (!request.user) {
            return response_1.ResponseHelper.unauthorized(reply, 'Authentication required');
        }
        const { serverId } = request.params;
        const { status, limit = '50', offset = '0' } = request.query;
        const users = await (0, shared_hosting_1.getSharedHostingService)().listServerUsers(serverId, {
            status,
            limit: parseInt(limit),
            offset: parseInt(offset)
        });
        logger_1.logger.info(`Retrieved ${users.length} users for server: ${serverId}`);
        return response_1.ResponseHelper.success(reply, users);
    }
    catch (error) {
        logger_1.logger.error('List server users controller error:', error);
        return response_1.ResponseHelper.internalError(reply, 'Failed to fetch server users');
    }
}
async function toggleUserStatusController(request, reply) {
    try {
        if (!request.user) {
            return response_1.ResponseHelper.unauthorized(reply, 'Authentication required');
        }
        const { userId } = request.params;
        const { action, reason } = request.body;
        if (!action || !['suspend', 'unsuspend', 'disable'].includes(action)) {
            return response_1.ResponseHelper.validationError(reply, 'Invalid action. Must be: suspend, unsuspend, or disable');
        }
        const result = await (0, shared_hosting_1.getSharedHostingService)().toggleUserStatus(userId, action, reason);
        logger_1.logger.info(`${action} user: ${userId}${reason ? ` - ${reason}` : ''}`);
        return response_1.ResponseHelper.success(reply, result);
    }
    catch (error) {
        logger_1.logger.error('Toggle user status controller error:', error);
        if (error instanceof Error && error.message.includes('not found')) {
            return response_1.ResponseHelper.notFound(reply, 'Shared hosting user not found');
        }
        return response_1.ResponseHelper.internalError(reply, 'Failed to update user status');
    }
}
async function getUserFileStructureController(request, reply) {
    try {
        if (!request.user) {
            return response_1.ResponseHelper.unauthorized(reply, 'Authentication required');
        }
        const { userId } = request.params;
        const { path = '/' } = request.query;
        const fileStructure = await (0, shared_hosting_1.getSharedHostingService)().getUserFileStructure(userId, path);
        logger_1.logger.info(`Retrieved file structure for user: ${userId}, path: ${path}`);
        return response_1.ResponseHelper.success(reply, fileStructure);
    }
    catch (error) {
        logger_1.logger.error('Get user file structure controller error:', error);
        if (error instanceof Error && error.message.includes('not found')) {
            return response_1.ResponseHelper.notFound(reply, 'User or path not found');
        }
        return response_1.ResponseHelper.internalError(reply, 'Failed to fetch file structure');
    }
}
//# sourceMappingURL=shared-hosting.controller.js.map