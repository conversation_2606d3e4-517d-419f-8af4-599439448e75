import {
  __commonJS
} from "./chunk-DC5AMYBS.js";

// node_modules/@xterm/addon-search/lib/addon-search.js
var require_addon_search = __commonJS({
  "node_modules/@xterm/addon-search/lib/addon-search.js"(exports, module) {
    !function(e, t) {
      "object" == typeof exports && "object" == typeof module ? module.exports = t() : "function" == typeof define && define.amd ? define([], t) : "object" == typeof exports ? exports.SearchAddon = t() : e.SearchAddon = t();
    }(self, () => (() => {
      "use strict";
      var e = { 345: (e2, t2) => {
        Object.defineProperty(t2, "__esModule", { value: true }), t2.runAndSubscribe = t2.forwardEvent = t2.EventEmitter = void 0, t2.EventEmitter = class {
          constructor() {
            this._listeners = [], this._disposed = false;
          }
          get event() {
            return this._event || (this._event = (e3) => (this._listeners.push(e3), { dispose: () => {
              if (!this._disposed) {
                for (let t3 = 0; t3 < this._listeners.length; t3++) if (this._listeners[t3] === e3) return void this._listeners.splice(t3, 1);
              }
            } })), this._event;
          }
          fire(e3, t3) {
            const i2 = [];
            for (let e4 = 0; e4 < this._listeners.length; e4++) i2.push(this._listeners[e4]);
            for (let s2 = 0; s2 < i2.length; s2++) i2[s2].call(void 0, e3, t3);
          }
          dispose() {
            this.clearListeners(), this._disposed = true;
          }
          clearListeners() {
            this._listeners && (this._listeners.length = 0);
          }
        }, t2.forwardEvent = function(e3, t3) {
          return e3((e4) => t3.fire(e4));
        }, t2.runAndSubscribe = function(e3, t3) {
          return t3(void 0), e3((e4) => t3(e4));
        };
      }, 859: (e2, t2) => {
        function i2(e3) {
          for (const t3 of e3) t3.dispose();
          e3.length = 0;
        }
        Object.defineProperty(t2, "__esModule", { value: true }), t2.getDisposeArrayDisposable = t2.disposeArray = t2.toDisposable = t2.MutableDisposable = t2.Disposable = void 0, t2.Disposable = class {
          constructor() {
            this._disposables = [], this._isDisposed = false;
          }
          dispose() {
            this._isDisposed = true;
            for (const e3 of this._disposables) e3.dispose();
            this._disposables.length = 0;
          }
          register(e3) {
            return this._disposables.push(e3), e3;
          }
          unregister(e3) {
            const t3 = this._disposables.indexOf(e3);
            -1 !== t3 && this._disposables.splice(t3, 1);
          }
        }, t2.MutableDisposable = class {
          constructor() {
            this._isDisposed = false;
          }
          get value() {
            return this._isDisposed ? void 0 : this._value;
          }
          set value(e3) {
            var _a;
            this._isDisposed || e3 === this._value || ((_a = this._value) == null ? void 0 : _a.dispose(), this._value = e3);
          }
          clear() {
            this.value = void 0;
          }
          dispose() {
            var _a;
            this._isDisposed = true, (_a = this._value) == null ? void 0 : _a.dispose(), this._value = void 0;
          }
        }, t2.toDisposable = function(e3) {
          return { dispose: e3 };
        }, t2.disposeArray = i2, t2.getDisposeArrayDisposable = function(e3) {
          return { dispose: () => i2(e3) };
        };
      } }, t = {};
      function i(s2) {
        var r = t[s2];
        if (void 0 !== r) return r.exports;
        var o = t[s2] = { exports: {} };
        return e[s2](o, o.exports, i), o.exports;
      }
      var s = {};
      return (() => {
        var e2 = s;
        Object.defineProperty(e2, "__esModule", { value: true }), e2.SearchAddon = void 0;
        const t2 = i(345), r = i(859), o = " ~!@#$%^&*()+`-=[]{}|\\;:\"',./<>?";
        class n extends r.Disposable {
          constructor(e3) {
            super(), this._highlightedLines = /* @__PURE__ */ new Set(), this._highlightDecorations = [], this._selectedDecoration = this.register(new r.MutableDisposable()), this._linesCacheTimeoutId = 0, this._linesCacheDisposables = new r.MutableDisposable(), this._onDidChangeResults = this.register(new t2.EventEmitter()), this.onDidChangeResults = this._onDidChangeResults.event, this._highlightLimit = (e3 == null ? void 0 : e3.highlightLimit) ?? 1e3;
          }
          activate(e3) {
            this._terminal = e3, this.register(this._terminal.onWriteParsed(() => this._updateMatches())), this.register(this._terminal.onResize(() => this._updateMatches())), this.register((0, r.toDisposable)(() => this.clearDecorations()));
          }
          _updateMatches() {
            var _a;
            this._highlightTimeout && window.clearTimeout(this._highlightTimeout), this._cachedSearchTerm && ((_a = this._lastSearchOptions) == null ? void 0 : _a.decorations) && (this._highlightTimeout = setTimeout(() => {
              const e3 = this._cachedSearchTerm;
              this._cachedSearchTerm = void 0, this.findPrevious(e3, { ...this._lastSearchOptions, incremental: true, noScroll: true });
            }, 200));
          }
          clearDecorations(e3) {
            this._selectedDecoration.clear(), (0, r.disposeArray)(this._highlightDecorations), this._highlightDecorations = [], this._highlightedLines.clear(), e3 || (this._cachedSearchTerm = void 0);
          }
          clearActiveDecoration() {
            this._selectedDecoration.clear();
          }
          findNext(e3, t3) {
            if (!this._terminal) throw new Error("Cannot use addon until it has been loaded");
            const i2 = !this._lastSearchOptions || this._didOptionsChange(this._lastSearchOptions, t3);
            this._lastSearchOptions = t3, (t3 == null ? void 0 : t3.decorations) && (void 0 === this._cachedSearchTerm || e3 !== this._cachedSearchTerm || i2) && this._highlightAllMatches(e3, t3);
            const s2 = this._findNextAndSelect(e3, t3);
            return this._fireResults(t3), this._cachedSearchTerm = e3, s2;
          }
          _highlightAllMatches(e3, t3) {
            if (!this._terminal) throw new Error("Cannot use addon until it has been loaded");
            if (!e3 || 0 === e3.length) return void this.clearDecorations();
            t3 = t3 || {}, this.clearDecorations(true);
            const i2 = [];
            let s2, r2 = this._find(e3, 0, 0, t3);
            for (; r2 && ((s2 == null ? void 0 : s2.row) !== r2.row || (s2 == null ? void 0 : s2.col) !== r2.col) && !(i2.length >= this._highlightLimit); ) s2 = r2, i2.push(s2), r2 = this._find(e3, s2.col + s2.term.length >= this._terminal.cols ? s2.row + 1 : s2.row, s2.col + s2.term.length >= this._terminal.cols ? 0 : s2.col + 1, t3);
            for (const e4 of i2) {
              const i3 = this._createResultDecoration(e4, t3.decorations);
              i3 && (this._highlightedLines.add(i3.marker.line), this._highlightDecorations.push({ decoration: i3, match: e4, dispose() {
                i3.dispose();
              } }));
            }
          }
          _find(e3, t3, i2, s2) {
            var _a;
            if (!this._terminal || !e3 || 0 === e3.length) return (_a = this._terminal) == null ? void 0 : _a.clearSelection(), void this.clearDecorations();
            if (i2 > this._terminal.cols) throw new Error(`Invalid col: ${i2} to search in terminal of ${this._terminal.cols} cols`);
            let r2;
            this._initLinesCache();
            const o2 = { startRow: t3, startCol: i2 };
            if (r2 = this._findInLine(e3, o2, s2), !r2) for (let i3 = t3 + 1; i3 < this._terminal.buffer.active.baseY + this._terminal.rows && (o2.startRow = i3, o2.startCol = 0, r2 = this._findInLine(e3, o2, s2), !r2); i3++) ;
            return r2;
          }
          _findNextAndSelect(e3, t3) {
            var _a;
            if (!this._terminal || !e3 || 0 === e3.length) return (_a = this._terminal) == null ? void 0 : _a.clearSelection(), this.clearDecorations(), false;
            const i2 = this._terminal.getSelectionPosition();
            this._terminal.clearSelection();
            let s2 = 0, r2 = 0;
            i2 && (this._cachedSearchTerm === e3 ? (s2 = i2.end.x, r2 = i2.end.y) : (s2 = i2.start.x, r2 = i2.start.y)), this._initLinesCache();
            const o2 = { startRow: r2, startCol: s2 };
            let n2 = this._findInLine(e3, o2, t3);
            if (!n2) for (let i3 = r2 + 1; i3 < this._terminal.buffer.active.baseY + this._terminal.rows && (o2.startRow = i3, o2.startCol = 0, n2 = this._findInLine(e3, o2, t3), !n2); i3++) ;
            if (!n2 && 0 !== r2) for (let i3 = 0; i3 < r2 && (o2.startRow = i3, o2.startCol = 0, n2 = this._findInLine(e3, o2, t3), !n2); i3++) ;
            return !n2 && i2 && (o2.startRow = i2.start.y, o2.startCol = 0, n2 = this._findInLine(e3, o2, t3)), this._selectResult(n2, t3 == null ? void 0 : t3.decorations, t3 == null ? void 0 : t3.noScroll);
          }
          findPrevious(e3, t3) {
            if (!this._terminal) throw new Error("Cannot use addon until it has been loaded");
            const i2 = !this._lastSearchOptions || this._didOptionsChange(this._lastSearchOptions, t3);
            this._lastSearchOptions = t3, (t3 == null ? void 0 : t3.decorations) && (void 0 === this._cachedSearchTerm || e3 !== this._cachedSearchTerm || i2) && this._highlightAllMatches(e3, t3);
            const s2 = this._findPreviousAndSelect(e3, t3);
            return this._fireResults(t3), this._cachedSearchTerm = e3, s2;
          }
          _didOptionsChange(e3, t3) {
            return !!t3 && (e3.caseSensitive !== t3.caseSensitive || e3.regex !== t3.regex || e3.wholeWord !== t3.wholeWord);
          }
          _fireResults(e3) {
            if (e3 == null ? void 0 : e3.decorations) {
              let e4 = -1;
              if (this._selectedDecoration.value) {
                const t3 = this._selectedDecoration.value.match;
                for (let i2 = 0; i2 < this._highlightDecorations.length; i2++) {
                  const s2 = this._highlightDecorations[i2].match;
                  if (s2.row === t3.row && s2.col === t3.col && s2.size === t3.size) {
                    e4 = i2;
                    break;
                  }
                }
              }
              this._onDidChangeResults.fire({ resultIndex: e4, resultCount: this._highlightDecorations.length });
            }
          }
          _findPreviousAndSelect(e3, t3) {
            var _a;
            if (!this._terminal) throw new Error("Cannot use addon until it has been loaded");
            if (!this._terminal || !e3 || 0 === e3.length) return (_a = this._terminal) == null ? void 0 : _a.clearSelection(), this.clearDecorations(), false;
            const i2 = this._terminal.getSelectionPosition();
            this._terminal.clearSelection();
            let s2 = this._terminal.buffer.active.baseY + this._terminal.rows - 1, r2 = this._terminal.cols;
            const o2 = true;
            this._initLinesCache();
            const n2 = { startRow: s2, startCol: r2 };
            let h;
            if (i2 && (n2.startRow = s2 = i2.start.y, n2.startCol = r2 = i2.start.x, this._cachedSearchTerm !== e3 && (h = this._findInLine(e3, n2, t3, false), h || (n2.startRow = s2 = i2.end.y, n2.startCol = r2 = i2.end.x))), h || (h = this._findInLine(e3, n2, t3, o2)), !h) {
              n2.startCol = Math.max(n2.startCol, this._terminal.cols);
              for (let i3 = s2 - 1; i3 >= 0 && (n2.startRow = i3, h = this._findInLine(e3, n2, t3, o2), !h); i3--) ;
            }
            if (!h && s2 !== this._terminal.buffer.active.baseY + this._terminal.rows - 1) for (let i3 = this._terminal.buffer.active.baseY + this._terminal.rows - 1; i3 >= s2 && (n2.startRow = i3, h = this._findInLine(e3, n2, t3, o2), !h); i3--) ;
            return this._selectResult(h, t3 == null ? void 0 : t3.decorations, t3 == null ? void 0 : t3.noScroll);
          }
          _initLinesCache() {
            const e3 = this._terminal;
            this._linesCache || (this._linesCache = new Array(e3.buffer.active.length), this._linesCacheDisposables.value = (0, r.getDisposeArrayDisposable)([e3.onLineFeed(() => this._destroyLinesCache()), e3.onCursorMove(() => this._destroyLinesCache()), e3.onResize(() => this._destroyLinesCache())])), window.clearTimeout(this._linesCacheTimeoutId), this._linesCacheTimeoutId = window.setTimeout(() => this._destroyLinesCache(), 15e3);
          }
          _destroyLinesCache() {
            this._linesCache = void 0, this._linesCacheDisposables.clear(), this._linesCacheTimeoutId && (window.clearTimeout(this._linesCacheTimeoutId), this._linesCacheTimeoutId = 0);
          }
          _isWholeWord(e3, t3, i2) {
            return (0 === e3 || o.includes(t3[e3 - 1])) && (e3 + i2.length === t3.length || o.includes(t3[e3 + i2.length]));
          }
          _findInLine(e3, t3, i2 = {}, s2 = false) {
            var _a;
            const r2 = this._terminal, o2 = t3.startRow, n2 = t3.startCol, h = r2.buffer.active.getLine(o2);
            if (h == null ? void 0 : h.isWrapped) return s2 ? void (t3.startCol += r2.cols) : (t3.startRow--, t3.startCol += r2.cols, this._findInLine(e3, t3, i2));
            let a = (_a = this._linesCache) == null ? void 0 : _a[o2];
            a || (a = this._translateBufferLineToStringWithWrap(o2, true), this._linesCache && (this._linesCache[o2] = a));
            const [l, c] = a, d = this._bufferColsToStringOffset(o2, n2), _ = i2.caseSensitive ? e3 : e3.toLowerCase(), u = i2.caseSensitive ? l : l.toLowerCase();
            let f = -1;
            if (i2.regex) {
              const t4 = RegExp(_, "g");
              let i3;
              if (s2) for (; i3 = t4.exec(u.slice(0, d)); ) f = t4.lastIndex - i3[0].length, e3 = i3[0], t4.lastIndex -= e3.length - 1;
              else i3 = t4.exec(u.slice(d)), i3 && i3[0].length > 0 && (f = d + (t4.lastIndex - i3[0].length), e3 = i3[0]);
            } else s2 ? d - _.length >= 0 && (f = u.lastIndexOf(_, d - _.length)) : f = u.indexOf(_, d);
            if (f >= 0) {
              if (i2.wholeWord && !this._isWholeWord(f, u, e3)) return;
              let t4 = 0;
              for (; t4 < c.length - 1 && f >= c[t4 + 1]; ) t4++;
              let s3 = t4;
              for (; s3 < c.length - 1 && f + e3.length >= c[s3 + 1]; ) s3++;
              const n3 = f - c[t4], h2 = f + e3.length - c[s3], a2 = this._stringLengthToBufferSize(o2 + t4, n3);
              return { term: e3, col: a2, row: o2 + t4, size: this._stringLengthToBufferSize(o2 + s3, h2) - a2 + r2.cols * (s3 - t4) };
            }
          }
          _stringLengthToBufferSize(e3, t3) {
            const i2 = this._terminal.buffer.active.getLine(e3);
            if (!i2) return 0;
            for (let e4 = 0; e4 < t3; e4++) {
              const s2 = i2.getCell(e4);
              if (!s2) break;
              const r2 = s2.getChars();
              r2.length > 1 && (t3 -= r2.length - 1);
              const o2 = i2.getCell(e4 + 1);
              o2 && 0 === o2.getWidth() && t3++;
            }
            return t3;
          }
          _bufferColsToStringOffset(e3, t3) {
            const i2 = this._terminal;
            let s2 = e3, r2 = 0, o2 = i2.buffer.active.getLine(s2);
            for (; t3 > 0 && o2; ) {
              for (let e4 = 0; e4 < t3 && e4 < i2.cols; e4++) {
                const t4 = o2.getCell(e4);
                if (!t4) break;
                t4.getWidth() && (r2 += 0 === t4.getCode() ? 1 : t4.getChars().length);
              }
              if (s2++, o2 = i2.buffer.active.getLine(s2), o2 && !o2.isWrapped) break;
              t3 -= i2.cols;
            }
            return r2;
          }
          _translateBufferLineToStringWithWrap(e3, t3) {
            var _a;
            const i2 = this._terminal, s2 = [], r2 = [0];
            let o2 = i2.buffer.active.getLine(e3);
            for (; o2; ) {
              const n2 = i2.buffer.active.getLine(e3 + 1), h = !!n2 && n2.isWrapped;
              let a = o2.translateToString(!h && t3);
              if (h && n2) {
                const e4 = o2.getCell(o2.length - 1);
                e4 && 0 === e4.getCode() && 1 === e4.getWidth() && 2 === ((_a = n2.getCell(0)) == null ? void 0 : _a.getWidth()) && (a = a.slice(0, -1));
              }
              if (s2.push(a), !h) break;
              r2.push(r2[r2.length - 1] + a.length), e3++, o2 = n2;
            }
            return [s2.join(""), r2];
          }
          _selectResult(e3, t3, i2) {
            const s2 = this._terminal;
            if (this._selectedDecoration.clear(), !e3) return s2.clearSelection(), false;
            if (s2.select(e3.col, e3.row, e3.size), t3) {
              const i3 = s2.registerMarker(-s2.buffer.active.baseY - s2.buffer.active.cursorY + e3.row);
              if (i3) {
                const o2 = s2.registerDecoration({ marker: i3, x: e3.col, width: e3.size, backgroundColor: t3.activeMatchBackground, layer: "top", overviewRulerOptions: { color: t3.activeMatchColorOverviewRuler } });
                if (o2) {
                  const s3 = [];
                  s3.push(i3), s3.push(o2.onRender((e4) => this._applyStyles(e4, t3.activeMatchBorder, true))), s3.push(o2.onDispose(() => (0, r.disposeArray)(s3))), this._selectedDecoration.value = { decoration: o2, match: e3, dispose() {
                    o2.dispose();
                  } };
                }
              }
            }
            if (!i2 && (e3.row >= s2.buffer.active.viewportY + s2.rows || e3.row < s2.buffer.active.viewportY)) {
              let t4 = e3.row - s2.buffer.active.viewportY;
              t4 -= Math.floor(s2.rows / 2), s2.scrollLines(t4);
            }
            return true;
          }
          _applyStyles(e3, t3, i2) {
            e3.classList.contains("xterm-find-result-decoration") || (e3.classList.add("xterm-find-result-decoration"), t3 && (e3.style.outline = `1px solid ${t3}`)), i2 && e3.classList.add("xterm-find-active-result-decoration");
          }
          _createResultDecoration(e3, t3) {
            const i2 = this._terminal, s2 = i2.registerMarker(-i2.buffer.active.baseY - i2.buffer.active.cursorY + e3.row);
            if (!s2) return;
            const o2 = i2.registerDecoration({ marker: s2, x: e3.col, width: e3.size, backgroundColor: t3.matchBackground, overviewRulerOptions: this._highlightedLines.has(s2.line) ? void 0 : { color: t3.matchOverviewRuler, position: "center" } });
            if (o2) {
              const e4 = [];
              e4.push(s2), e4.push(o2.onRender((e5) => this._applyStyles(e5, t3.matchBorder, false))), e4.push(o2.onDispose(() => (0, r.disposeArray)(e4)));
            }
            return o2;
          }
        }
        e2.SearchAddon = n;
      })(), s;
    })());
  }
});
export default require_addon_search();
//# sourceMappingURL=@xterm_addon-search.js.map
