const crypto = require('crypto');
const axios = require('axios');

/**
 * WhoGoHost Domain Lookup API Client
 * JavaScript equivalent of the PHP script for domain availability checking
 */
class WhoGoHostDomainLookup {
  constructor(username, secretKey) {
    this.endpoint = "https://www.whogohost.com/host/modules/addons/DomainsReseller/api/index.php";
    this.username = username;
    this.secretKey = secretKey;
  }

  /**
   * Generate authentication token using HMAC SHA256
   * Equivalent to PHP: base64_encode(hash_hmac("sha256", secretKey, "username:" . gmdate("y-m-d H")))
   */
  generateAuthToken() {
    // Get current date in GMT format (y-m-d H)
    const now = new Date();
    const year = now.getUTCFullYear().toString().slice(-2); // Last 2 digits of year
    const month = String(now.getUTCMonth() + 1).padStart(2, '0');
    const day = String(now.getUTCDate()).padStart(2, '0');
    const hour = String(now.getUTCHours()).padStart(2, '0');
    
    const dateString = `${year}-${month}-${day} ${hour}`;
    const message = `${this.username}:${dateString}`;
    
    // Create HMAC SHA256 hash
    const hmac = crypto.createHmac('sha256', this.secretKey);
    hmac.update(message);
    const hash = hmac.digest('hex');
    
    // Base64 encode the hash
    return Buffer.from(hash).toString('base64');
  }

  /**
   * Perform domain lookup
   * @param {Object} params - Domain lookup parameters
   * @param {string} params.searchTerm - Domain name to search
   * @param {string} params.punnyCodeSearchTerm - Punycode version of domain
   * @param {Array<string>} params.tldsToInclude - Array of TLDs to check
   * @param {boolean} params.isIdnDomain - Whether it's an IDN domain
   * @param {boolean} params.premiumEnabled - Whether to include premium domains
   * @returns {Promise<Object>} API response
   */
  async lookupDomain(params = {}) {
    try {
      // Default parameters
      const defaultParams = {
        searchTerm: "example",
        punnyCodeSearchTerm: "example",
        tldsToInclude: [".com", ".com.ng"],
        isIdnDomain: true,
        premiumEnabled: true
      };

      // Merge with provided parameters
      const requestParams = { ...defaultParams, ...params };

      // Generate authentication token
      const authToken = this.generateAuthToken();

      // Prepare headers
      const headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
        'username': this.username,
        'token': authToken
      };

      // Convert parameters to URL-encoded format (equivalent to http_build_query)
      const formData = new URLSearchParams();
      Object.keys(requestParams).forEach(key => {
        if (Array.isArray(requestParams[key])) {
          requestParams[key].forEach(value => {
            formData.append(`${key}[]`, value);
          });
        } else {
          formData.append(key, requestParams[key]);
        }
      });

      console.log('🔍 Making domain lookup request...');
      console.log('Endpoint:', `${this.endpoint}/domains/lookup`);
      console.log('Parameters:', requestParams);
      console.log('Auth Token:', authToken);

      // Make the API request
      const response = await axios.post(
        `${this.endpoint}/domains/lookup`,
        formData.toString(),
        {
          headers,
          timeout: 30000, // 30 second timeout
          validateStatus: function (status) {
            return status < 500; // Accept all status codes below 500
          }
        }
      );

      console.log('✅ Response received:', {
        status: response.status,
        statusText: response.statusText,
        data: response.data
      });

      return {
        success: response.status === 200,
        status: response.status,
        data: response.data,
        headers: response.headers
      };

    } catch (error) {
      console.error('❌ Domain lookup failed:', error.message);
      
      if (error.response) {
        console.error('Response status:', error.response.status);
        console.error('Response data:', error.response.data);
        return {
          success: false,
          status: error.response.status,
          error: error.response.data,
          message: error.message
        };
      }

      return {
        success: false,
        error: error.message,
        message: 'Network or request error occurred'
      };
    }
  }

  /**
   * Lookup multiple domains with different TLDs
   * @param {string} domainName - Base domain name
   * @param {Array<string>} tlds - Array of TLDs to check
   * @returns {Promise<Object>} Combined results
   */
  async lookupMultipleTlds(domainName, tlds = ['.com', '.net', '.org', '.com.ng']) {
    try {
      const results = [];
      
      for (const tld of tlds) {
        console.log(`🔍 Checking ${domainName}${tld}...`);
        
        const result = await this.lookupDomain({
          searchTerm: domainName,
          punnyCodeSearchTerm: domainName,
          tldsToInclude: [tld],
          isIdnDomain: false,
          premiumEnabled: true
        });
        
        results.push({
          domain: `${domainName}${tld}`,
          tld: tld,
          result: result
        });
        
        // Add small delay between requests to be respectful
        await new Promise(resolve => setTimeout(resolve, 500));
      }
      
      return {
        success: true,
        domainName,
        results
      };
      
    } catch (error) {
      console.error('❌ Multiple TLD lookup failed:', error.message);
      return {
        success: false,
        error: error.message
      };
    }
  }
}

// Example usage
async function example() {
  // Initialize the client with your credentials
  const client = new WhoGoHostDomainLookup(
    '<EMAIL>', // Replace with your username
    '1234567890QWERTYUIOPASDFGHJKLZXCVBNM' // Replace with your secret key
  );

  try {
    // Single domain lookup
    console.log('=== Single Domain Lookup ===');
    const singleResult = await client.lookupDomain({
      searchTerm: 'myawesomesite',
      punnyCodeSearchTerm: 'myawesomesite',
      tldsToInclude: ['.com', '.net', '.org'],
      isIdnDomain: false,
      premiumEnabled: true
    });
    
    console.log('Single lookup result:', JSON.stringify(singleResult, null, 2));

    // Multiple TLD lookup
    console.log('\n=== Multiple TLD Lookup ===');
    const multipleResult = await client.lookupMultipleTlds('testdomain', ['.com', '.net', '.org', '.com.ng']);
    console.log('Multiple TLD result:', JSON.stringify(multipleResult, null, 2));

  } catch (error) {
    console.error('Example failed:', error);
  }
}

// Export for use as module
module.exports = WhoGoHostDomainLookup;

// Run example if this file is executed directly
if (require.main === module) {
  example();
}
