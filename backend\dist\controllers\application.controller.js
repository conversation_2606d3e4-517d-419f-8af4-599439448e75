"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createApplicationController = createApplicationController;
exports.getApplicationsController = getApplicationsController;
exports.getApplicationController = getApplicationController;
exports.updateApplicationController = updateApplicationController;
exports.deleteApplicationController = deleteApplicationController;
exports.getApplicationStatsController = getApplicationStatsController;
exports.deployApplicationController = deployApplicationController;
exports.stopApplicationController = stopApplicationController;
const models_1 = require("../models");
const application_1 = require("../services/application");
const response_1 = require("../utils/response");
const logger_1 = require("../utils/logger");
async function createApplicationController(request, reply) {
    try {
        if (!request.user) {
            return response_1.ResponseHelper.unauthorized(reply, 'Authentication required');
        }
        const validationResult = models_1.createApplicationSchema.safeParse(request.body);
        if (!validationResult.success) {
            const errors = validationResult.error.errors.map(err => ({
                field: err.path.join('.'),
                message: err.message,
            }));
            return response_1.ResponseHelper.validationError(reply, 'Validation failed', { errors });
        }
        const applicationData = validationResult.data;
        const application = await (0, application_1.getApplicationService)().createApplication(request.user.sub, applicationData);
        logger_1.logger.info(`Application created: ${application.name} by user: ${request.user.email}`);
        return response_1.ResponseHelper.success(reply, application, 201);
    }
    catch (error) {
        logger_1.logger.error('Create application controller error:', error);
        if (error instanceof Error) {
            if (error.message.includes('already exists')) {
                return response_1.ResponseHelper.error(reply, 'CONFLICT', error.message, 409);
            }
        }
        return response_1.ResponseHelper.internalError(reply, 'Failed to create application');
    }
}
async function getApplicationsController(request, reply) {
    try {
        if (!request.user) {
            return response_1.ResponseHelper.unauthorized(reply, 'Authentication required');
        }
        const options = {
            page: request.query.page ? parseInt(request.query.page, 10) : 1,
            limit: request.query.limit ? parseInt(request.query.limit, 10) : 10,
            sort: request.query.sort || 'created_at',
            order: request.query.order || 'desc',
        };
        if (options.page < 1)
            options.page = 1;
        if (options.limit < 1 || options.limit > 100)
            options.limit = 10;
        const applications = await (0, application_1.getApplicationService)().getApplicationsByUser(request.user.sub, options);
        return response_1.ResponseHelper.success(reply, applications);
    }
    catch (error) {
        logger_1.logger.error('Get applications controller error:', error);
        return response_1.ResponseHelper.internalError(reply, 'Failed to get applications');
    }
}
async function getApplicationController(request, reply) {
    try {
        if (!request.user) {
            return response_1.ResponseHelper.unauthorized(reply, 'Authentication required');
        }
        const { id } = request.params;
        if (!id.match(/^[0-9a-fA-F]{24}$/)) {
            return response_1.ResponseHelper.validationError(reply, 'Invalid application ID format');
        }
        const application = await (0, application_1.getApplicationService)().getApplicationById(id, request.user.sub);
        return response_1.ResponseHelper.success(reply, application);
    }
    catch (error) {
        logger_1.logger.error('Get application controller error:', error);
        if (error instanceof Error && error.message.includes('not found')) {
            return response_1.ResponseHelper.notFound(reply, 'Application not found');
        }
        return response_1.ResponseHelper.internalError(reply, 'Failed to get application');
    }
}
async function updateApplicationController(request, reply) {
    try {
        if (!request.user) {
            return response_1.ResponseHelper.unauthorized(reply, 'Authentication required');
        }
        const { id } = request.params;
        if (!id.match(/^[0-9a-fA-F]{24}$/)) {
            return response_1.ResponseHelper.validationError(reply, 'Invalid application ID format');
        }
        const validationResult = models_1.updateApplicationSchema.safeParse(request.body);
        if (!validationResult.success) {
            const errors = validationResult.error.errors.map(err => ({
                field: err.path.join('.'),
                message: err.message,
            }));
            return response_1.ResponseHelper.validationError(reply, 'Validation failed', { errors });
        }
        const updateData = validationResult.data;
        const application = await (0, application_1.getApplicationService)().updateApplication(id, request.user.sub, updateData);
        logger_1.logger.info(`Application updated: ${id} by user: ${request.user.email}`);
        return response_1.ResponseHelper.success(reply, application);
    }
    catch (error) {
        logger_1.logger.error('Update application controller error:', error);
        if (error instanceof Error) {
            if (error.message.includes('not found')) {
                return response_1.ResponseHelper.notFound(reply, 'Application not found');
            }
            if (error.message.includes('already exists')) {
                return response_1.ResponseHelper.error(reply, 'CONFLICT', error.message, 409);
            }
        }
        return response_1.ResponseHelper.internalError(reply, 'Failed to update application');
    }
}
async function deleteApplicationController(request, reply) {
    try {
        if (!request.user) {
            return response_1.ResponseHelper.unauthorized(reply, 'Authentication required');
        }
        const { id } = request.params;
        if (!id.match(/^[0-9a-fA-F]{24}$/)) {
            return response_1.ResponseHelper.validationError(reply, 'Invalid application ID format');
        }
        await (0, application_1.getApplicationService)().deleteApplication(id, request.user.sub);
        logger_1.logger.info(`Application deleted: ${id} by user: ${request.user.email}`);
        return response_1.ResponseHelper.success(reply, { message: 'Application deleted successfully' });
    }
    catch (error) {
        logger_1.logger.error('Delete application controller error:', error);
        if (error instanceof Error && error.message.includes('not found')) {
            return response_1.ResponseHelper.notFound(reply, 'Application not found');
        }
        return response_1.ResponseHelper.internalError(reply, 'Failed to delete application');
    }
}
async function getApplicationStatsController(request, reply) {
    try {
        if (!request.user) {
            return response_1.ResponseHelper.unauthorized(reply, 'Authentication required');
        }
        const stats = await (0, application_1.getApplicationService)().getApplicationStats(request.user.sub);
        return response_1.ResponseHelper.success(reply, stats);
    }
    catch (error) {
        logger_1.logger.error('Get application stats controller error:', error);
        return response_1.ResponseHelper.internalError(reply, 'Failed to get application statistics');
    }
}
async function deployApplicationController(request, reply) {
    try {
        if (!request.user) {
            return response_1.ResponseHelper.unauthorized(reply, 'Authentication required');
        }
        const { id } = request.params;
        if (!id.match(/^[0-9a-fA-F]{24}$/)) {
            return response_1.ResponseHelper.validationError(reply, 'Invalid application ID format');
        }
        const application = await (0, application_1.getApplicationService)().deployApplication(id, request.user.sub);
        logger_1.logger.info(`Application deployment started: ${id} by user: ${request.user.email}`);
        return response_1.ResponseHelper.success(reply, {
            message: 'Deployment started successfully',
            application
        });
    }
    catch (error) {
        logger_1.logger.error('Deploy application controller error:', error);
        if (error instanceof Error && error.message.includes('not found')) {
            return response_1.ResponseHelper.notFound(reply, 'Application not found');
        }
        return response_1.ResponseHelper.internalError(reply, 'Failed to start deployment');
    }
}
async function stopApplicationController(request, reply) {
    try {
        if (!request.user) {
            return response_1.ResponseHelper.unauthorized(reply, 'Authentication required');
        }
        const { id } = request.params;
        if (!id.match(/^[0-9a-fA-F]{24}$/)) {
            return response_1.ResponseHelper.validationError(reply, 'Invalid application ID format');
        }
        const application = await (0, application_1.getApplicationService)().updateApplicationStatus(id, 'stopped', request.user.sub);
        logger_1.logger.info(`Application stopped: ${id} by user: ${request.user.email}`);
        return response_1.ResponseHelper.success(reply, {
            message: 'Application stopped',
            application
        });
    }
    catch (error) {
        logger_1.logger.error('Stop application controller error:', error);
        if (error instanceof Error && error.message.includes('not found')) {
            return response_1.ResponseHelper.notFound(reply, 'Application not found');
        }
        return response_1.ResponseHelper.internalError(reply, 'Failed to stop application');
    }
}
//# sourceMappingURL=application.controller.js.map