import { Document, Types } from 'mongoose';
export interface ISharedHostingUser extends Document {
    _id: Types.ObjectId;
    user_id: string;
    username: string;
    linux_username: string;
    email: string;
    home_directory: string;
    plan: string;
    status: 'active' | 'suspended' | 'deleted' | 'pending';
    server_id: string;
    server_ip: string;
    port: number;
    ssh_port: number;
    ftp_port: number;
    resource_limits: {
        cpu_quota: number;
        memory_max: number;
        bandwidth_limit: number;
        storage_limit: number;
    };
    usage: {
        cpu_usage: number;
        memory_usage: number;
        bandwidth_used: number;
        storage_used: number;
    };
    created_at: Date;
    updated_at: Date;
    last_login?: Date;
    applications: Types.ObjectId[];
}
export interface ISharedHostingApplication extends Document {
    _id: Types.ObjectId;
    user_id: string;
    name: string;
    type: 'static-website' | 'web-service' | 'api' | 'database';
    framework: string;
    language: string;
    status: 'running' | 'stopped' | 'building' | 'failed' | 'pending';
    subdomain: string;
    directory: string;
    ssl_enabled: boolean;
    domain?: string;
    environment_variables: Record<string, string>;
    build_command?: string;
    start_command?: string;
    port?: number;
    created_at: Date;
    updated_at: Date;
    last_deployed?: Date;
    deployment_logs: Types.ObjectId[];
}
export interface IServerMetrics extends Document {
    _id: Types.ObjectId;
    server_id: string;
    server_ip: string;
    timestamp: Date;
    cpu_usage: number;
    memory_usage: number;
    disk_usage: number;
    load_average: number;
    network_in: number;
    network_out: number;
    active_users: number;
    active_applications: number;
}
export interface IPaymentStatus extends Document {
    _id: Types.ObjectId;
    user_id: string;
    status: 'active' | 'grace_period' | 'suspended' | 'deleted';
    last_payment_date?: Date;
    next_payment_due?: Date;
    grace_period_start?: Date;
    grace_period_end?: Date;
    deletion_scheduled?: Date;
    payment_method?: string;
    subscription_id?: string;
    created_at: Date;
    updated_at: Date;
}
export interface IUserUsageAnalytics extends Document {
    _id: Types.ObjectId;
    user_id: string;
    date: Date;
    cpu_usage_avg: number;
    cpu_usage_max: number;
    memory_usage_avg: number;
    memory_usage_max: number;
    bandwidth_used: number;
    storage_used: number;
    requests_count: number;
    uptime_percentage: number;
}
export declare const SharedHostingUser: import("mongoose").Model<ISharedHostingUser, {}, {}, {}, Document<unknown, {}, ISharedHostingUser, {}> & ISharedHostingUser & Required<{
    _id: Types.ObjectId;
}> & {
    __v: number;
}, any>;
export declare const SharedHostingApplication: import("mongoose").Model<ISharedHostingApplication, {}, {}, {}, Document<unknown, {}, ISharedHostingApplication, {}> & ISharedHostingApplication & Required<{
    _id: Types.ObjectId;
}> & {
    __v: number;
}, any>;
export declare const ServerMetrics: import("mongoose").Model<IServerMetrics, {}, {}, {}, Document<unknown, {}, IServerMetrics, {}> & IServerMetrics & Required<{
    _id: Types.ObjectId;
}> & {
    __v: number;
}, any>;
export declare const PaymentStatus: import("mongoose").Model<IPaymentStatus, {}, {}, {}, Document<unknown, {}, IPaymentStatus, {}> & IPaymentStatus & Required<{
    _id: Types.ObjectId;
}> & {
    __v: number;
}, any>;
export declare const UserUsageAnalytics: import("mongoose").Model<IUserUsageAnalytics, {}, {}, {}, Document<unknown, {}, IUserUsageAnalytics, {}> & IUserUsageAnalytics & Required<{
    _id: Types.ObjectId;
}> & {
    __v: number;
}, any>;
//# sourceMappingURL=shared-hosting.d.ts.map