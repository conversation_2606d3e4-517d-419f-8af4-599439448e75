import { logger } from '../utils/logger';
import { appConfig } from '../config';

interface CloudflareRecord {
  id?: string;
  type: 'A' | 'AAAA' | 'CNAME' | 'MX' | 'TXT';
  name: string;
  content: string;
  ttl?: number;
  priority?: number;
  proxied?: boolean;
}

interface LoadBalancerPool {
  id?: string;
  name: string;
  origins: Array<{
    name: string;
    address: string;
    enabled: boolean;
    weight?: number;
  }>;
  monitor?: string;
  enabled: boolean;
}

export class CloudflareDNSService {
  private apiToken: string;
  private zoneId: string;
  private baseUrl = 'https://api.cloudflare.com/client/v4';

  constructor() {
    this.apiToken = appConfig.cloudflare.apiToken;
    this.zoneId = appConfig.cloudflare.zoneId;
  }

  // Make API request to Cloudflare
  private async makeRequest(endpoint: string, method: string = 'GET', data?: any): Promise<any> {
    const url = `${this.baseUrl}${endpoint}`;
    
    const options: RequestInit = {
      method,
      headers: {
        'Authorization': `Bearer ${this.apiToken}`,
        'Content-Type': 'application/json',
      },
    };

    if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
      options.body = JSON.stringify(data);
    }

    try {
      const response = await fetch(url, options);
      const result = await response.json();

      if (!result.success) {
        throw new Error(`Cloudflare API error: ${result.errors?.map((e: any) => e.message).join(', ')}`);
      }

      return result.result;
    } catch (error) {
      logger.error(`Cloudflare API request failed: ${endpoint}`, error);
      throw error;
    }
  }

  // Create DNS record
  async createDNSRecord(record: CloudflareRecord): Promise<string> {
    try {
      const result = await this.makeRequest(`/zones/${this.zoneId}/dns_records`, 'POST', {
        type: record.type,
        name: record.name,
        content: record.content,
        ttl: record.ttl || 300,
        priority: record.priority,
        proxied: record.proxied || false
      });

      logger.info(`Created DNS record: ${record.name} -> ${record.content}`);
      return result.id;
    } catch (error) {
      logger.error(`Failed to create DNS record for ${record.name}:`, error);
      throw error;
    }
  }

  // Update DNS record
  async updateDNSRecord(recordId: string, updates: Partial<CloudflareRecord>): Promise<void> {
    try {
      await this.makeRequest(`/zones/${this.zoneId}/dns_records/${recordId}`, 'PATCH', updates);
      logger.info(`Updated DNS record ${recordId}`);
    } catch (error) {
      logger.error(`Failed to update DNS record ${recordId}:`, error);
      throw error;
    }
  }

  // Delete DNS record
  async deleteDNSRecord(recordId: string): Promise<void> {
    try {
      await this.makeRequest(`/zones/${this.zoneId}/dns_records/${recordId}`, 'DELETE');
      logger.info(`Deleted DNS record ${recordId}`);
    } catch (error) {
      logger.error(`Failed to delete DNS record ${recordId}:`, error);
      throw error;
    }
  }

  // Get DNS records
  async getDNSRecords(name?: string, type?: string): Promise<CloudflareRecord[]> {
    try {
      let endpoint = `/zones/${this.zoneId}/dns_records`;
      const params = new URLSearchParams();
      
      if (name) params.append('name', name);
      if (type) params.append('type', type);
      
      if (params.toString()) {
        endpoint += `?${params.toString()}`;
      }

      const records = await this.makeRequest(endpoint);
      return records;
    } catch (error) {
      logger.error('Failed to get DNS records:', error);
      throw error;
    }
  }

  // Setup round-robin DNS for multiple servers
  async setupRoundRobinDNS(subdomain: string, serverIPs: string[]): Promise<string[]> {
    try {
      const recordIds: string[] = [];
      const fullDomain = `${subdomain}.${appConfig.domain.primary}`;

      // Delete existing records for this subdomain
      const existingRecords = await this.getDNSRecords(fullDomain, 'A');
      for (const record of existingRecords) {
        if (record.id) {
          await this.deleteDNSRecord(record.id);
        }
      }

      // Create A records for each server IP
      for (const ip of serverIPs) {
        const recordId = await this.createDNSRecord({
          type: 'A',
          name: fullDomain,
          content: ip,
          ttl: 300, // 5 minutes for quick failover
          proxied: false // Direct IP routing for round-robin
        });
        recordIds.push(recordId);
      }

      logger.info(`Setup round-robin DNS for ${fullDomain} with ${serverIPs.length} servers`);
      return recordIds;
    } catch (error) {
      logger.error(`Failed to setup round-robin DNS for ${subdomain}:`, error);
      throw error;
    }
  }

  // Create load balancer pool
  async createLoadBalancerPool(pool: LoadBalancerPool): Promise<string> {
    try {
      const result = await this.makeRequest('/load_balancers/pools', 'POST', {
        name: pool.name,
        origins: pool.origins,
        monitor: pool.monitor,
        enabled: pool.enabled,
        minimum_origins: 1,
        check_regions: ['WNAM', 'ENAM', 'WEU', 'EEU', 'SEAS', 'NEAS']
      });

      logger.info(`Created load balancer pool: ${pool.name}`);
      return result.id;
    } catch (error) {
      logger.error(`Failed to create load balancer pool ${pool.name}:`, error);
      throw error;
    }
  }

  // Create load balancer
  async createLoadBalancer(name: string, hostname: string, poolIds: string[]): Promise<string> {
    try {
      const result = await this.makeRequest('/load_balancers', 'POST', {
        name,
        fallback_pool: poolIds[0],
        default_pools: poolIds,
        description: `Load balancer for ${hostname}`,
        ttl: 30,
        steering_policy: 'dynamic_latency',
        proxied: true,
        enabled: true,
        zone_id: this.zoneId,
        hostname
      });

      logger.info(`Created load balancer: ${name} for ${hostname}`);
      return result.id;
    } catch (error) {
      logger.error(`Failed to create load balancer ${name}:`, error);
      throw error;
    }
  }

  // Setup advanced load balancing for application
  async setupLoadBalancing(subdomain: string, servers: Array<{ ip: string; weight?: number }>): Promise<{
    poolId: string;
    loadBalancerId: string;
  }> {
    try {
      const fullDomain = `${subdomain}.${appConfig.domain.primary}`;
      
      // Create origins for the pool
      const origins = servers.map((server, index) => ({
        name: `server-${index + 1}`,
        address: server.ip,
        enabled: true,
        weight: server.weight || 1
      }));

      // Create load balancer pool
      const poolId = await this.createLoadBalancerPool({
        name: `${subdomain}-pool`,
        origins,
        enabled: true
      });

      // Create load balancer
      const loadBalancerId = await this.createLoadBalancer(
        `${subdomain}-lb`,
        fullDomain,
        [poolId]
      );

      logger.info(`Setup load balancing for ${fullDomain}`);
      return { poolId, loadBalancerId };
    } catch (error) {
      logger.error(`Failed to setup load balancing for ${subdomain}:`, error);
      throw error;
    }
  }

  // Update server health in load balancer pool
  async updateServerHealth(poolId: string, serverAddress: string, enabled: boolean): Promise<void> {
    try {
      // Get current pool configuration
      const pool = await this.makeRequest(`/load_balancers/pools/${poolId}`);
      
      // Update the specific origin
      const updatedOrigins = pool.origins.map((origin: any) => {
        if (origin.address === serverAddress) {
          return { ...origin, enabled };
        }
        return origin;
      });

      // Update the pool
      await this.makeRequest(`/load_balancers/pools/${poolId}`, 'PATCH', {
        origins: updatedOrigins
      });

      logger.info(`Updated server ${serverAddress} health to ${enabled ? 'enabled' : 'disabled'} in pool ${poolId}`);
    } catch (error) {
      logger.error(`Failed to update server health for ${serverAddress}:`, error);
      throw error;
    }
  }

  // Create health monitor
  async createHealthMonitor(name: string, path: string = '/health'): Promise<string> {
    try {
      const result = await this.makeRequest('/load_balancers/monitors', 'POST', {
        type: 'http',
        description: `Health monitor for ${name}`,
        method: 'GET',
        path,
        header: {
          'Host': [appConfig.domain.primary],
          'User-Agent': ['Cloudflare-Health-Check']
        },
        timeout: 5,
        retries: 2,
        interval: 60,
        expected_codes: '200',
        follow_redirects: false,
        allow_insecure: false
      });

      logger.info(`Created health monitor: ${name}`);
      return result.id;
    } catch (error) {
      logger.error(`Failed to create health monitor ${name}:`, error);
      throw error;
    }
  }

  // Get load balancer analytics
  async getLoadBalancerAnalytics(loadBalancerId: string, since?: Date): Promise<any> {
    try {
      const sinceParam = since ? since.toISOString() : new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();
      
      const analytics = await this.makeRequest(
        `/load_balancers/${loadBalancerId}/analytics/events?since=${sinceParam}`
      );

      return analytics;
    } catch (error) {
      logger.error(`Failed to get load balancer analytics for ${loadBalancerId}:`, error);
      throw error;
    }
  }

  // Failover to backup server
  async failoverToBackup(loadBalancerId: string, backupPoolId: string): Promise<void> {
    try {
      await this.makeRequest(`/load_balancers/${loadBalancerId}`, 'PATCH', {
        fallback_pool: backupPoolId,
        enabled: true
      });

      logger.info(`Initiated failover for load balancer ${loadBalancerId} to backup pool ${backupPoolId}`);
    } catch (error) {
      logger.error(`Failed to failover load balancer ${loadBalancerId}:`, error);
      throw error;
    }
  }

  // Get zone analytics
  async getZoneAnalytics(since?: Date): Promise<any> {
    try {
      const sinceParam = since ? since.toISOString() : new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();
      
      const analytics = await this.makeRequest(
        `/zones/${this.zoneId}/analytics/dashboard?since=${sinceParam}`
      );

      return analytics;
    } catch (error) {
      logger.error('Failed to get zone analytics:', error);
      throw error;
    }
  }

  // Purge cache for specific URLs
  async purgeCache(urls: string[]): Promise<void> {
    try {
      await this.makeRequest(`/zones/${this.zoneId}/purge_cache`, 'POST', {
        files: urls
      });

      logger.info(`Purged cache for ${urls.length} URLs`);
    } catch (error) {
      logger.error('Failed to purge cache:', error);
      throw error;
    }
  }

  // Get DNS management status
  async getDNSStatus(): Promise<any> {
    try {
      const zone = await this.makeRequest(`/zones/${this.zoneId}`);
      const records = await this.getDNSRecords();
      
      return {
        zone: {
          name: zone.name,
          status: zone.status,
          nameServers: zone.name_servers
        },
        recordCount: records.length,
        lastChecked: new Date().toISOString()
      };
    } catch (error) {
      logger.error('Failed to get DNS status:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const cloudflareDNS = new CloudflareDNSService();
