import { Schema, model, Document, Types } from 'mongoose';

// Shared Hosting User Interface
export interface ISharedHostingUser extends Document {
  _id: Types.ObjectId;
  user_id: string;
  username: string;
  linux_username: string;
  email: string;
  home_directory: string;
  plan: string;
  status: 'active' | 'suspended' | 'deleted' | 'pending';
  server_id: string;
  server_ip: string;
  port: number;
  ssh_port: number;
  ftp_port: number;
  resource_limits: {
    cpu_quota: number;
    memory_max: number;
    bandwidth_limit: number;
    storage_limit: number;
  };
  usage: {
    cpu_usage: number;
    memory_usage: number;
    bandwidth_used: number;
    storage_used: number;
  };
  created_at: Date;
  updated_at: Date;
  last_login?: Date;
  applications: Types.ObjectId[];
}

// Shared Hosting User Schema
const SharedHostingUserSchema = new Schema<ISharedHostingUser>({
  user_id: { type: String, required: true, unique: true, index: true },
  username: { type: String, required: true, unique: true, index: true },
  linux_username: { type: String, required: true, unique: true, index: true },
  email: { type: String, required: true, index: true },
  home_directory: { type: String, required: true },
  plan: { type: String, required: true, index: true },
  status: { 
    type: String, 
    enum: ['active', 'suspended', 'deleted', 'pending'], 
    default: 'pending',
    index: true 
  },
  server_id: { type: String, required: true, index: true },
  server_ip: { type: String, required: true },
  port: { type: Number, required: true },
  ssh_port: { type: Number, required: true },
  ftp_port: { type: Number, required: true },
  resource_limits: {
    cpu_quota: { type: Number, required: true },
    memory_max: { type: Number, required: true },
    bandwidth_limit: { type: Number, required: true },
    storage_limit: { type: Number, required: true }
  },
  usage: {
    cpu_usage: { type: Number, default: 0 },
    memory_usage: { type: Number, default: 0 },
    bandwidth_used: { type: Number, default: 0 },
    storage_used: { type: Number, default: 0 }
  },
  created_at: { type: Date, default: Date.now, index: true },
  updated_at: { type: Date, default: Date.now },
  last_login: { type: Date },
  applications: [{ type: Schema.Types.ObjectId, ref: 'SharedHostingApplication' }]
}, {
  timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
  collection: 'shared_hosting_users'
});

// Shared Hosting Application Interface
export interface ISharedHostingApplication extends Document {
  _id: Types.ObjectId;
  user_id: string;
  name: string;
  type: 'static-website' | 'web-service' | 'api' | 'database';
  framework: string;
  language: string;
  status: 'running' | 'stopped' | 'building' | 'failed' | 'pending';
  subdomain: string;
  directory: string;
  ssl_enabled: boolean;
  domain?: string;
  environment_variables: Record<string, string>;
  build_command?: string;
  start_command?: string;
  port?: number;
  created_at: Date;
  updated_at: Date;
  last_deployed?: Date;
  deployment_logs: Types.ObjectId[];
}

// Shared Hosting Application Schema
const SharedHostingApplicationSchema = new Schema<ISharedHostingApplication>({
  user_id: { type: String, required: true, index: true },
  name: { type: String, required: true },
  type: { 
    type: String, 
    enum: ['static-website', 'web-service', 'api', 'database'], 
    required: true,
    index: true 
  },
  framework: { type: String, required: true },
  language: { type: String, required: true },
  status: { 
    type: String, 
    enum: ['running', 'stopped', 'building', 'failed', 'pending'], 
    default: 'pending',
    index: true 
  },
  subdomain: { type: String, required: true, unique: true, index: true },
  directory: { type: String, required: true },
  ssl_enabled: { type: Boolean, default: false },
  domain: { type: String, index: true },
  environment_variables: { type: Map, of: String, default: {} },
  build_command: { type: String },
  start_command: { type: String },
  port: { type: Number },
  created_at: { type: Date, default: Date.now, index: true },
  updated_at: { type: Date, default: Date.now },
  last_deployed: { type: Date },
  deployment_logs: [{ type: Schema.Types.ObjectId, ref: 'DeploymentLog' }]
}, {
  timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
  collection: 'shared_hosting_applications'
});

// Server Metrics Interface
export interface IServerMetrics extends Document {
  _id: Types.ObjectId;
  server_id: string;
  server_ip: string;
  timestamp: Date;
  cpu_usage: number;
  memory_usage: number;
  disk_usage: number;
  load_average: number;
  network_in: number;
  network_out: number;
  active_users: number;
  active_applications: number;
}

// Server Metrics Schema
const ServerMetricsSchema = new Schema<IServerMetrics>({
  server_id: { type: String, required: true, index: true },
  server_ip: { type: String, required: true },
  timestamp: { type: Date, default: Date.now, index: true },
  cpu_usage: { type: Number, required: true },
  memory_usage: { type: Number, required: true },
  disk_usage: { type: Number, required: true },
  load_average: { type: Number, required: true },
  network_in: { type: Number, default: 0 },
  network_out: { type: Number, default: 0 },
  active_users: { type: Number, default: 0 },
  active_applications: { type: Number, default: 0 }
}, {
  collection: 'server_metrics'
});

// Payment Status Interface
export interface IPaymentStatus extends Document {
  _id: Types.ObjectId;
  user_id: string;
  status: 'active' | 'grace_period' | 'suspended' | 'deleted';
  last_payment_date?: Date;
  next_payment_due?: Date;
  grace_period_start?: Date;
  grace_period_end?: Date;
  deletion_scheduled?: Date;
  payment_method?: string;
  subscription_id?: string;
  created_at: Date;
  updated_at: Date;
}

// Payment Status Schema
const PaymentStatusSchema = new Schema<IPaymentStatus>({
  user_id: { type: String, required: true, unique: true, index: true },
  status: { 
    type: String, 
    enum: ['active', 'grace_period', 'suspended', 'deleted'], 
    default: 'active',
    index: true 
  },
  last_payment_date: { type: Date },
  next_payment_due: { type: Date, index: true },
  grace_period_start: { type: Date },
  grace_period_end: { type: Date, index: true },
  deletion_scheduled: { type: Date, index: true },
  payment_method: { type: String },
  subscription_id: { type: String },
  created_at: { type: Date, default: Date.now },
  updated_at: { type: Date, default: Date.now }
}, {
  timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
  collection: 'payment_status'
});

// User Usage Analytics Interface
export interface IUserUsageAnalytics extends Document {
  _id: Types.ObjectId;
  user_id: string;
  date: Date;
  cpu_usage_avg: number;
  cpu_usage_max: number;
  memory_usage_avg: number;
  memory_usage_max: number;
  bandwidth_used: number;
  storage_used: number;
  requests_count: number;
  uptime_percentage: number;
}

// User Usage Analytics Schema
const UserUsageAnalyticsSchema = new Schema<IUserUsageAnalytics>({
  user_id: { type: String, required: true, index: true },
  date: { type: Date, required: true, index: true },
  cpu_usage_avg: { type: Number, default: 0 },
  cpu_usage_max: { type: Number, default: 0 },
  memory_usage_avg: { type: Number, default: 0 },
  memory_usage_max: { type: Number, default: 0 },
  bandwidth_used: { type: Number, default: 0 },
  storage_used: { type: Number, default: 0 },
  requests_count: { type: Number, default: 0 },
  uptime_percentage: { type: Number, default: 100 }
}, {
  collection: 'user_usage_analytics'
});

// Create compound indexes for better query performance
SharedHostingUserSchema.index({ server_id: 1, status: 1 });
SharedHostingUserSchema.index({ plan: 1, status: 1 });
SharedHostingApplicationSchema.index({ user_id: 1, status: 1 });
ServerMetricsSchema.index({ server_id: 1, timestamp: -1 });
UserUsageAnalyticsSchema.index({ user_id: 1, date: -1 });
PaymentStatusSchema.index({ status: 1, grace_period_end: 1 });
PaymentStatusSchema.index({ status: 1, deletion_scheduled: 1 });

// Export models
export const SharedHostingUser = model<ISharedHostingUser>('SharedHostingUser', SharedHostingUserSchema);
export const SharedHostingApplication = model<ISharedHostingApplication>('SharedHostingApplication', SharedHostingApplicationSchema);
export const ServerMetrics = model<IServerMetrics>('ServerMetrics', ServerMetricsSchema);
export const PaymentStatus = model<IPaymentStatus>('PaymentStatus', PaymentStatusSchema);
export const UserUsageAnalytics = model<IUserUsageAnalytics>('UserUsageAnalytics', UserUsageAnalyticsSchema);
