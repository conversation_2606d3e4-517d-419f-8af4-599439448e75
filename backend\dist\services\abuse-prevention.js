"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AbusePreventionService = void 0;
const logger_1 = require("../utils/logger");
const config_1 = require("../config");
const shared_hosting_1 = require("../models/shared-hosting");
const shared_hosting_2 = require("./shared-hosting");
class AbusePreventionService {
    monitoringInterval = null;
    isMonitoring = false;
    thresholds = {
        cpu: { warning: 70, critical: 85, suspend: 95 },
        memory: { warning: 80, critical: 90, suspend: 95 },
        bandwidth: { warning: 80, critical: 90, suspend: 95 },
        storage: { warning: 85, critical: 95, suspend: 98 },
        requests: { warning: 1000, critical: 2000, suspend: 5000 }
    };
    constructor() {
        this.startMonitoring();
    }
    startMonitoring() {
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
        }
        const intervalMs = config_1.appConfig.security.abuseCheckIntervalMinutes * 60 * 1000;
        this.monitoringInterval = setInterval(async () => {
            if (!this.isMonitoring) {
                this.isMonitoring = true;
                try {
                    await this.checkForAbuse();
                }
                catch (error) {
                    logger_1.logger.error('Abuse monitoring failed:', error);
                }
                finally {
                    this.isMonitoring = false;
                }
            }
        }, intervalMs);
        logger_1.logger.info(`Abuse prevention monitoring started with ${config_1.appConfig.security.abuseCheckIntervalMinutes}min interval`);
    }
    stopMonitoring() {
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            this.monitoringInterval = null;
        }
        logger_1.logger.info('Abuse prevention monitoring stopped');
    }
    async checkForAbuse() {
        try {
            const activeUsers = await shared_hosting_1.SharedHostingUser.find({
                status: 'active',
                server_id: config_1.appConfig.ssh.host
            });
            logger_1.logger.debug(`Checking ${activeUsers.length} users for abuse`);
            for (const user of activeUsers) {
                try {
                    await this.checkUserAbuse(user);
                }
                catch (error) {
                    logger_1.logger.error(`Failed to check abuse for user ${user.username}:`, error);
                }
            }
            await this.checkServerAbuse();
        }
        catch (error) {
            logger_1.logger.error('Failed to check for abuse:', error);
            throw error;
        }
    }
    async checkUserAbuse(user) {
        const alerts = [];
        const usage = user.usage;
        const limits = user.resource_limits;
        const cpuPercent = (usage.cpu_usage / limits.cpu_quota) * 100;
        if (cpuPercent >= this.thresholds.cpu.suspend) {
            alerts.push(this.createAlert(user.user_id, 'cpu', 'suspend', cpuPercent, this.thresholds.cpu.suspend));
        }
        else if (cpuPercent >= this.thresholds.cpu.critical) {
            alerts.push(this.createAlert(user.user_id, 'cpu', 'critical', cpuPercent, this.thresholds.cpu.critical));
        }
        else if (cpuPercent >= this.thresholds.cpu.warning) {
            alerts.push(this.createAlert(user.user_id, 'cpu', 'warning', cpuPercent, this.thresholds.cpu.warning));
        }
        const memoryPercent = (usage.memory_usage / limits.memory_max) * 100;
        if (memoryPercent >= this.thresholds.memory.suspend) {
            alerts.push(this.createAlert(user.user_id, 'memory', 'suspend', memoryPercent, this.thresholds.memory.suspend));
        }
        else if (memoryPercent >= this.thresholds.memory.critical) {
            alerts.push(this.createAlert(user.user_id, 'memory', 'critical', memoryPercent, this.thresholds.memory.critical));
        }
        else if (memoryPercent >= this.thresholds.memory.warning) {
            alerts.push(this.createAlert(user.user_id, 'memory', 'warning', memoryPercent, this.thresholds.memory.warning));
        }
        const bandwidthPercent = (usage.bandwidth_used / limits.bandwidth_limit) * 100;
        if (bandwidthPercent >= this.thresholds.bandwidth.suspend) {
            alerts.push(this.createAlert(user.user_id, 'bandwidth', 'suspend', bandwidthPercent, this.thresholds.bandwidth.suspend));
        }
        else if (bandwidthPercent >= this.thresholds.bandwidth.critical) {
            alerts.push(this.createAlert(user.user_id, 'bandwidth', 'critical', bandwidthPercent, this.thresholds.bandwidth.critical));
        }
        else if (bandwidthPercent >= this.thresholds.bandwidth.warning) {
            alerts.push(this.createAlert(user.user_id, 'bandwidth', 'warning', bandwidthPercent, this.thresholds.bandwidth.warning));
        }
        const storagePercent = (usage.storage_used / limits.storage_limit) * 100;
        if (storagePercent >= this.thresholds.storage.suspend) {
            alerts.push(this.createAlert(user.user_id, 'storage', 'suspend', storagePercent, this.thresholds.storage.suspend));
        }
        else if (storagePercent >= this.thresholds.storage.critical) {
            alerts.push(this.createAlert(user.user_id, 'storage', 'critical', storagePercent, this.thresholds.storage.critical));
        }
        else if (storagePercent >= this.thresholds.storage.warning) {
            alerts.push(this.createAlert(user.user_id, 'storage', 'warning', storagePercent, this.thresholds.storage.warning));
        }
        const recentAnalytics = await shared_hosting_1.UserUsageAnalytics.findOne({
            user_id: user.user_id,
            date: { $gte: new Date(Date.now() - 60 * 60 * 1000) }
        });
        if (recentAnalytics) {
            const requestsPerMinute = recentAnalytics.requests_count / 60;
            if (requestsPerMinute >= this.thresholds.requests.suspend) {
                alerts.push(this.createAlert(user.user_id, 'requests', 'suspend', requestsPerMinute, this.thresholds.requests.suspend));
            }
            else if (requestsPerMinute >= this.thresholds.requests.critical) {
                alerts.push(this.createAlert(user.user_id, 'requests', 'critical', requestsPerMinute, this.thresholds.requests.critical));
            }
            else if (requestsPerMinute >= this.thresholds.requests.warning) {
                alerts.push(this.createAlert(user.user_id, 'requests', 'warning', requestsPerMinute, this.thresholds.requests.warning));
            }
        }
        for (const alert of alerts) {
            await this.processAlert(alert);
        }
    }
    async checkServerAbuse() {
        try {
            const latestMetrics = await shared_hosting_1.ServerMetrics.findOne({
                server_id: config_1.appConfig.ssh.host
            }).sort({ timestamp: -1 });
            if (!latestMetrics) {
                return;
            }
            if (latestMetrics.cpu_usage > 90) {
                await this.handleServerOverload('cpu', latestMetrics.cpu_usage);
            }
            if (latestMetrics.memory_usage > 90) {
                await this.handleServerOverload('memory', latestMetrics.memory_usage);
            }
            if (latestMetrics.disk_usage > 95) {
                await this.handleServerOverload('disk', latestMetrics.disk_usage);
            }
        }
        catch (error) {
            logger_1.logger.error('Failed to check server abuse:', error);
        }
    }
    createAlert(userId, type, severity, value, threshold) {
        return {
            userId,
            type,
            severity,
            value,
            threshold,
            timestamp: new Date(),
            action: this.getActionForSeverity(severity)
        };
    }
    getActionForSeverity(severity) {
        switch (severity) {
            case 'warning':
                return 'log_and_notify';
            case 'critical':
                return 'throttle_resources';
            case 'suspend':
                return 'suspend_user';
            default:
                return 'log_only';
        }
    }
    async processAlert(alert) {
        try {
            logger_1.logger.warn(`Abuse detected for user ${alert.userId}: ${alert.type} usage ${alert.value.toFixed(2)}% exceeds ${alert.threshold}% threshold`);
            switch (alert.action) {
                case 'log_and_notify':
                    await this.logAndNotify(alert);
                    break;
                case 'throttle_resources':
                    await this.throttleUserResources(alert);
                    break;
                case 'suspend_user':
                    await this.suspendUser(alert);
                    break;
                default:
                    logger_1.logger.info(`Alert logged for user ${alert.userId}`);
            }
        }
        catch (error) {
            logger_1.logger.error(`Failed to process alert for user ${alert.userId}:`, error);
        }
    }
    async logAndNotify(alert) {
        logger_1.logger.warn(`WARNING: User ${alert.userId} ${alert.type} usage at ${alert.value.toFixed(2)}%`);
    }
    async throttleUserResources(alert) {
        const ssh = new shared_hosting_2.SSHConnection();
        try {
            await ssh.connect();
            const user = await shared_hosting_1.SharedHostingUser.findOne({ user_id: alert.userId });
            if (!user) {
                return;
            }
            logger_1.logger.warn(`THROTTLING: User ${alert.userId} ${alert.type} usage at ${alert.value.toFixed(2)}%`);
            switch (alert.type) {
                case 'cpu':
                    await this.throttleCPU(ssh, user, 50);
                    break;
                case 'memory':
                    await this.throttleMemory(ssh, user, 80);
                    break;
                case 'bandwidth':
                    await this.throttleBandwidth(ssh, user, 50);
                    break;
                case 'requests':
                    await this.throttleRequests(ssh, user);
                    break;
            }
        }
        catch (error) {
            logger_1.logger.error(`Failed to throttle user ${alert.userId}:`, error);
        }
        finally {
            await ssh.disconnect();
        }
    }
    async suspendUser(alert) {
        try {
            logger_1.logger.error(`SUSPENDING: User ${alert.userId} ${alert.type} usage at ${alert.value.toFixed(2)}%`);
            await shared_hosting_1.SharedHostingUser.findOneAndUpdate({ user_id: alert.userId }, {
                status: 'suspended',
                suspension_reason: `Abuse detected: ${alert.type} usage exceeded ${alert.threshold}%`,
                suspended_at: new Date()
            });
            await this.stopUserServices(alert.userId);
        }
        catch (error) {
            logger_1.logger.error(`Failed to suspend user ${alert.userId}:`, error);
        }
    }
    async throttleCPU(ssh, user, percentage) {
        const newQuota = Math.floor(user.resource_limits.cpu_quota * (percentage / 100));
        await ssh.executeCommand(`systemctl set-property user-${user.linux_username}.slice CPUQuota=${newQuota}%`);
    }
    async throttleMemory(ssh, user, percentage) {
        const newLimit = Math.floor(user.resource_limits.memory_max * (percentage / 100));
        await ssh.executeCommand(`systemctl set-property user-${user.linux_username}.slice MemoryMax=${newLimit}M`);
    }
    async throttleBandwidth(ssh, user, percentage) {
        const newLimit = Math.floor(user.resource_limits.bandwidth_limit * (percentage / 100));
        await ssh.executeCommand(`tc class change dev eth0 parent 1: classid 1:${user.port} htb rate ${newLimit}mbit`);
    }
    async throttleRequests(ssh, user) {
        await ssh.executeCommand(`iptables -A INPUT -p tcp --dport ${user.port} -m limit --limit 10/min -j ACCEPT`);
    }
    async stopUserServices(userId) {
        const ssh = new shared_hosting_2.SSHConnection();
        try {
            await ssh.connect();
            const user = await shared_hosting_1.SharedHostingUser.findOne({ user_id: userId });
            if (!user) {
                return;
            }
            await ssh.executeCommand(`systemctl stop user-${user.linux_username}.slice`);
            const applications = await shared_hosting_1.SharedHostingApplication.find({ user_id: userId });
            for (const app of applications) {
                if (app.status === 'running') {
                    await ssh.executeCommand(`systemctl stop ${app.name}-${user.linux_username}`);
                    await shared_hosting_1.SharedHostingApplication.findOneAndUpdate({ _id: app._id }, { status: 'stopped' });
                }
            }
        }
        catch (error) {
            logger_1.logger.error(`Failed to stop services for user ${userId}:`, error);
        }
        finally {
            await ssh.disconnect();
        }
    }
    async handleServerOverload(type, usage) {
        logger_1.logger.error(`SERVER OVERLOAD: ${type} usage at ${usage.toFixed(2)}%`);
        const topUsers = await this.getTopResourceConsumers(type, 5);
        for (const user of topUsers) {
            const alert = this.createAlert(user.user_id, type, 'critical', usage, 90);
            await this.throttleUserResources(alert);
        }
    }
    async getTopResourceConsumers(type, limit) {
        const sortField = type === 'cpu' ? 'usage.cpu_usage' :
            type === 'memory' ? 'usage.memory_usage' :
                'usage.storage_used';
        return await shared_hosting_1.SharedHostingUser.find({
            status: 'active',
            server_id: config_1.appConfig.ssh.host
        })
            .sort({ [sortField]: -1 })
            .limit(limit);
    }
    async getAbuseStats() {
        return {
            totalAlerts: 0,
            suspendedUsers: await shared_hosting_1.SharedHostingUser.countDocuments({ status: 'suspended' }),
            throttledUsers: 0,
            serverOverloads: 0
        };
    }
}
exports.AbusePreventionService = AbusePreventionService;
//# sourceMappingURL=abuse-prevention.js.map