import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import Card from '../components/ui/Card';
import Button from '../components/ui/Button';
import {
  CheckCircleIcon,
  XCircleIcon,
  GlobeAltIcon,
  MagnifyingGlassIcon,
  SparklesIcon,
  ShieldCheckIcon,
  BoltIcon,
  ArrowRightIcon,
  ExclamationTriangleIcon,
} from '@heroicons/react/24/outline';
import Layout from '../components/layout/Layout';
import { DomainService, type PopularTld, type DomainAvailabilityResult } from '../services/domainService';

// Popular domain extensions with pricing
const domainExtensions = [
  { extension: '.com', price: '$12.99', popular: true },
  { extension: '.io', price: '$39.99', popular: true },
  { extension: '.dev', price: '$15.99', popular: true },
  { extension: '.net', price: '$11.99', popular: false },
  { extension: '.org', price: '$13.99', popular: false },
  { extension: '.app', price: '$18.99', popular: false },
  { extension: '.co', price: '$32.99', popular: false },
  { extension: '.me', price: '$19.99', popular: false },
];

// Features for domain management
const domainFeatures = [
  {
    name: 'Free SSL Certificates',
    description: 'Secure your domain with automatic SSL certificate provisioning and renewal.',
    icon: ShieldCheckIcon,
  },
  {
    name: 'DNS Management',
    description: 'Full control over your DNS records with our intuitive management interface.',
    icon: GlobeAltIcon,
  },
  {
    name: 'Domain Protection',
    description: 'Protect your domain with privacy protection and security features.',
    icon: SparklesIcon,
  },
  {
    name: 'Fast Propagation',
    description: 'Lightning-fast DNS propagation ensures your changes take effect quickly.',
    icon: BoltIcon,
  },
];

export default function Domains() {
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState<Array<{
    domain: string;
    available: boolean;
    price: string;
    extension: string;
    status: string;
    isPremium?: boolean;
    premiumCost?: number;
    eapFee?: number;
  }>>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);
  const [searchError, setSearchError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'search' | 'transfer' | 'whois'>('search');
  const [transferDomain, setTransferDomain] = useState('');
  const [whoisDomain, setWhoisDomain] = useState('');
  const [whoisResult, setWhoisResult] = useState<any>(null);
  const [isWhoisLoading, setIsWhoisLoading] = useState(false);
  const [whoisError, setWhoisError] = useState<string | null>(null);
  const [popularTlds, setPopularTlds] = useState<PopularTld[]>([]);
  const [loadingTlds, setLoadingTlds] = useState(true);

  // Load popular TLDs on component mount
  useEffect(() => {
    const loadPopularTlds = async () => {
      try {
        const tlds = await DomainService.getPopularTlds();
        setPopularTlds(tlds);
      } catch (error) {
        console.error('Failed to load popular TLDs:', error);
        // Fallback to static data if API fails
        setPopularTlds([]);
      } finally {
        setLoadingTlds(false);
      }
    };

    loadPopularTlds();
  }, []);

  const handleDomainSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!searchTerm.trim()) return;

    setIsSearching(true);
    setHasSearched(true);
    setSearchError(null);

    try {
      // Clean the search term and extract domain name
      const cleanedTerm = DomainService.cleanDomainName(searchTerm);
      const baseDomain = cleanedTerm.replace(/\.(com|io|dev|net|org|app|co|me)$/, '');

      // Use popular TLDs or fallback to static extensions
      const tlds = popularTlds.length > 0
        ? popularTlds.map(tld => tld.tld.replace('.', ''))
        : domainExtensions.map(ext => ext.extension.replace('.', ''));

      // Call the real API
      const availabilityResult: DomainAvailabilityResult = await DomainService.checkAvailability({
        domains: [baseDomain],
        tlds: tlds
      });

      // Transform API results to component format
      const results = Object.entries(availabilityResult).map(([domain, info]) => {
        const extension = '.' + domain.split('.').pop();
        const fallbackPrice = domainExtensions.find(ext => ext.extension === extension)?.price || '$15.99';
        const statusInfo = DomainService.formatAvailabilityStatus(info.status);

        return {
          domain,
          available: statusInfo.available,
          price: info.premiumcost ? DomainService.formatPrice(info.premiumcost) : fallbackPrice,
          extension,
          status: info.status,
          isPremium: info.ispremiumname,
          premiumCost: info.premiumcost,
          eapFee: info.eapfee,
        };
      });

      setSearchResults(results);
    } catch (error) {
      console.error('Domain search failed:', error);
      setSearchError('Failed to search domains. Please try again.');

      // Fallback to simulated results if API fails
      const baseDomain = searchTerm.toLowerCase().replace(/\.(com|io|dev|net|org|app|co|me)$/, '');
      const fallbackResults = domainExtensions.map(ext => ({
        domain: `${baseDomain}${ext.extension}`,
        available: Math.random() > 0.3,
        price: ext.price,
        extension: ext.extension,
        status: 'unknown',
      }));
      setSearchResults(fallbackResults);
    } finally {
      setIsSearching(false);
    }
  };

  const handleWhoisLookup = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!whoisDomain.trim()) return;

    setIsWhoisLoading(true);
    setWhoisError(null);

    try {
      const cleanedDomain = DomainService.cleanDomainName(whoisDomain);

      if (!DomainService.validateDomainName(cleanedDomain)) {
        setWhoisError('Please enter a valid domain name (e.g., example.com)');
        return;
      }

      const result = await DomainService.whoisLookup({ domainName: cleanedDomain });
      setWhoisResult(result);
    } catch (error) {
      console.error('WHOIS lookup failed:', error);
      setWhoisError('Failed to perform WHOIS lookup. Please try again.');
    } finally {
      setIsWhoisLoading(false);
    }
  };

  return (
    <Layout>
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">
        {/* Hero Section */}
        <div className="relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-secondary-600/20 to-primary-600/20"></div>
          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
            <div className="text-center mb-12">
              <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
                Find Your Perfect
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-secondary-400 to-primary-400 block">
                  Domain Name
                </span>
              </h1>
              <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
                Search for available domains and register them instantly. Get started with your online presence today.
              </p>
            </div>

            {/* Main Content with Sidebar */}
            <div className="flex gap-8 max-w-7xl mx-auto">
              {/* Sidebar Navigation */}
              <aside className="w-64 hidden md:block bg-gray-900/80 rounded-xl p-6 h-fit">
                <nav className="flex flex-col gap-2">
                  <button
                    className={`text-left px-4 py-2 rounded-lg font-medium transition-colors duration-200 ${
                      activeTab === 'search' ? 'bg-secondary-600/20 text-secondary-300' : 'text-gray-300 hover:bg-white/5'
                    }`}
                    onClick={() => setActiveTab('search')}
                  >
                    <MagnifyingGlassIcon className="h-5 w-5 inline mr-2" />
                    Domain Search
                  </button>
                  <button
                    className={`text-left px-4 py-2 rounded-lg font-medium transition-colors duration-200 ${
                      activeTab === 'transfer' ? 'bg-secondary-600/20 text-secondary-300' : 'text-gray-300 hover:bg-white/5'
                    }`}
                    onClick={() => setActiveTab('transfer')}
                  >
                    <GlobeAltIcon className="h-5 w-5 inline mr-2" />
                    Transfer Domain
                  </button>
                  <button
                    className={`text-left px-4 py-2 rounded-lg font-medium transition-colors duration-200 ${
                      activeTab === 'whois' ? 'bg-secondary-600/20 text-secondary-300' : 'text-gray-300 hover:bg-white/5'
                    }`}
                    onClick={() => setActiveTab('whois')}
                  >
                    <SparklesIcon className="h-5 w-5 inline mr-2" />
                    WHOIS Lookup
                  </button>
                </nav>
              </aside>

              {/* Main Content Area */}
              <div className="flex-1 space-y-8">
                {/* Domain Search Tab */}
                {activeTab === 'search' && (
                  <Card className="p-8 bg-white/5 backdrop-blur-sm border border-white/10">
                    <div className="max-w-3xl mx-auto">
                      <div className="text-center mb-8">
                        <GlobeAltIcon className="h-12 w-12 text-secondary-500 mx-auto mb-4" />
                        <h2 className="text-2xl font-bold text-white mb-2">Search Available Domains</h2>
                        <p className="text-gray-400">Find the perfect domain for your project</p>
                      </div>

                      <form onSubmit={handleDomainSearch} className="space-y-6">
                        <div className="flex flex-col sm:flex-row gap-4">
                          <div className="relative flex-grow">
                            <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-4">
                              <MagnifyingGlassIcon className="h-6 w-6 text-gray-400" />
                            </div>
                            <input
                              type="text"
                              className="block w-full rounded-xl border-0 py-4 pl-12 pr-4 text-white text-lg
                                ring-1 ring-inset ring-white/20 placeholder:text-gray-400
                                focus:ring-2 focus:ring-inset focus:ring-secondary-500
                                bg-white/10 backdrop-blur-sm transition-all duration-300
                                hover:bg-white/15 focus:bg-white/15"
                              placeholder="Enter your domain name (e.g., myawesome-site)"
                              value={searchTerm}
                              onChange={(e) => setSearchTerm(e.target.value)}
                              disabled={isSearching}
                            />
                          </div>
                          <Button
                            type="submit"
                            variant="glass"
                            glow={true}
                            size="lg"
                            disabled={isSearching || !searchTerm.trim()}
                            className="px-8"
                          >
                            {isSearching ? (
                              <>
                                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                                Searching...
                              </>
                            ) : (
                              <>
                                Search Domains
                                <ArrowRightIcon className="ml-2 h-5 w-5" />
                              </>
                            )}
                          </Button>
                        </div>

                        {/* Popular Extensions */}
                        <div className="text-center">
                          <p className="text-sm text-gray-400 mb-3">Popular extensions:</p>
                          <div className="flex flex-wrap justify-center gap-2">
                            {domainExtensions.filter(ext => ext.popular).map((ext) => (
                              <button
                                key={ext.extension}
                                type="button"
                                onClick={() => setSearchTerm(searchTerm.replace(/\.(com|io|dev|net|org|app|co|me)$/, '') + ext.extension)}
                                className="px-3 py-1 text-sm bg-white/10 hover:bg-white/20 text-white rounded-lg
                                  border border-white/20 transition-all duration-200"
                              >
                                {ext.extension} - {ext.price}/year
                              </button>
                            ))}
                          </div>
                        </div>
                      </form>

                      {/* Error Display */}
                      {searchError && (
                        <div className="mt-6 p-4 bg-red-500/10 border border-red-500/20 rounded-xl">
                          <div className="flex items-center">
                            <ExclamationTriangleIcon className="h-5 w-5 text-red-400 mr-2" />
                            <p className="text-red-300 text-sm">{searchError}</p>
                          </div>
                        </div>
                      )}

                      {/* Search Results */}
                      {hasSearched && searchResults.length > 0 && (
                        <div className="mt-8 space-y-4">
                          <h3 className="text-lg font-medium text-white mb-4">Availability Results</h3>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {searchResults.map((result, index) => (
                              <div
                                key={result.domain}
                                className="glass-card p-4 rounded-xl flex justify-between items-center bg-white/5 border border-white/10"
                                style={{ animationDelay: `${index * 100}ms` }}
                              >
                                <div className="flex items-center">
                                  {result.available ? (
                                    <CheckCircleIcon className="h-5 w-5 text-green-400 mr-3" />
                                  ) : (
                                    <XCircleIcon className="h-5 w-5 text-red-400 mr-3" />
                                  )}
                                  <div>
                                    <div className="flex items-center gap-2">
                                      <p className="text-sm font-medium text-white">{result.domain}</p>
                                      {result.isPremium && (
                                        <span className="px-2 py-1 text-xs bg-yellow-500/20 text-yellow-300 rounded-full border border-yellow-500/30">
                                          Premium
                                        </span>
                                      )}
                                      {result.eapFee && (
                                        <span className="px-2 py-1 text-xs bg-blue-500/20 text-blue-300 rounded-full border border-blue-500/30">
                                          EAP
                                        </span>
                                      )}
                                    </div>
                                    <div className="flex items-center gap-2 text-xs text-gray-400">
                                      <span>{result.price}/year</span>
                                      {result.eapFee && (
                                        <span className="text-blue-300">+ {DomainService.formatPrice(result.eapFee)} EAP fee</span>
                                      )}
                                    </div>
                                    <p className="text-xs text-gray-500 capitalize">{result.status.replace(/([A-Z])/g, ' $1').trim()}</p>
                                  </div>
                                </div>
                                {result.available && (
                                  <Button
                                    variant="glass"
                                    size="sm"
                                    glow={true}
                                  >
                                    Register
                                  </Button>
                                )}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </Card>
                )}

                {/* Transfer Domain Tab */}
                {activeTab === 'transfer' && (
                  <Card className="p-6 bg-white/5 backdrop-blur-sm border border-white/10">
                    <div className="max-w-2xl mx-auto text-center">
                      <GlobeAltIcon className="h-10 w-10 text-secondary-500 mx-auto mb-4" />
                      <h2 className="text-xl font-bold text-white mb-2">Transfer a Domain</h2>
                      <p className="text-gray-400 mb-6">Easily transfer your existing domain to our platform. Enter your domain and follow the instructions.</p>
                      <input
                        type="text"
                        className="block w-full rounded-xl border-0 py-3 px-4 text-white ring-1 ring-inset ring-white/10 mb-4
                          placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-secondary-500
                          sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                        placeholder="Enter domain to transfer (e.g., mydomain.com)"
                        value={transferDomain}
                        onChange={(e) => setTransferDomain(e.target.value)}
                      />
                      <Button variant="glass" glow={true}>Start Transfer</Button>
                    </div>
                  </Card>
                )}

                {/* WHOIS Lookup Tab */}
                {activeTab === 'whois' && (
                  <Card className="p-6 bg-white/5 backdrop-blur-sm border border-white/10">
                    <div className="max-w-2xl mx-auto">
                      <div className="text-center mb-6">
                        <MagnifyingGlassIcon className="h-10 w-10 text-secondary-500 mx-auto mb-4" />
                        <h2 className="text-xl font-bold text-white mb-2">WHOIS Lookup</h2>
                        <p className="text-gray-400">Check domain registration details using WHOIS lookup.</p>
                      </div>

                      <form onSubmit={handleWhoisLookup} className="space-y-4">
                        <input
                          type="text"
                          className="block w-full rounded-xl border-0 py-3 px-4 text-white ring-1 ring-inset ring-white/10
                            placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-secondary-500
                            sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
                          placeholder="Enter domain for WHOIS lookup (e.g., mydomain.com)"
                          value={whoisDomain}
                          onChange={(e) => setWhoisDomain(e.target.value)}
                          disabled={isWhoisLoading}
                        />
                        <div className="text-center">
                          <Button
                            type="submit"
                            variant="glass"
                            glow={true}
                            disabled={isWhoisLoading || !whoisDomain.trim()}
                          >
                            {isWhoisLoading ? (
                              <>
                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                Looking up...
                              </>
                            ) : (
                              'Lookup Domain'
                            )}
                          </Button>
                        </div>
                      </form>

                      {/* WHOIS Error */}
                      {whoisError && (
                        <div className="mt-4 p-4 bg-red-500/10 border border-red-500/20 rounded-xl">
                          <div className="flex items-center">
                            <ExclamationTriangleIcon className="h-5 w-5 text-red-400 mr-2" />
                            <p className="text-red-300 text-sm">{whoisError}</p>
                          </div>
                        </div>
                      )}

                      {/* WHOIS Results */}
                      {whoisResult && (
                        <div className="mt-6 space-y-4">
                          <h3 className="text-lg font-medium text-white">Domain Information</h3>
                          <div className="bg-white/5 rounded-xl p-4 space-y-3">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div>
                                <p className="text-sm text-gray-400">Domain</p>
                                <p className="text-white font-medium">{whoisResult.domain}</p>
                              </div>
                              <div>
                                <p className="text-sm text-gray-400">Registrar</p>
                                <p className="text-white">{whoisResult.registrar || 'N/A'}</p>
                              </div>
                              <div>
                                <p className="text-sm text-gray-400">Creation Date</p>
                                <p className="text-white">{whoisResult.creationDate || 'N/A'}</p>
                              </div>
                              <div>
                                <p className="text-sm text-gray-400">Expiration Date</p>
                                <p className="text-white">{whoisResult.expirationDate || 'N/A'}</p>
                              </div>
                            </div>

                            {whoisResult.nameServers && whoisResult.nameServers.length > 0 && (
                              <div>
                                <p className="text-sm text-gray-400 mb-2">Name Servers</p>
                                <div className="space-y-1">
                                  {whoisResult.nameServers.map((ns: string, index: number) => (
                                    <p key={index} className="text-white text-sm font-mono">{ns}</p>
                                  ))}
                                </div>
                              </div>
                            )}

                            {whoisResult.status && whoisResult.status.length > 0 && (
                              <div>
                                <p className="text-sm text-gray-400 mb-2">Status</p>
                                <div className="flex flex-wrap gap-2">
                                  {whoisResult.status.map((status: string, index: number) => (
                                    <span key={index} className="px-2 py-1 text-xs bg-blue-500/20 text-blue-300 rounded-full border border-blue-500/30">
                                      {status}
                                    </span>
                                  ))}
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  </Card>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Features Section */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-white mb-4">Why Choose Our Domain Services?</h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              Get more than just a domain name. Enjoy premium features and excellent support.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {domainFeatures.map((feature) => (
              <Card key={feature.name} className="p-6 text-center bg-white/5 backdrop-blur-sm border border-white/10">
                <feature.icon className="h-12 w-12 text-secondary-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-white mb-2">{feature.name}</h3>
                <p className="text-gray-400 text-sm">{feature.description}</p>
              </Card>
            ))}
          </div>
        </div>

        {/* CTA Section */}
        <div className="bg-gradient-to-r from-secondary-600/20 to-primary-600/20 py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-3xl font-bold text-white mb-4">Ready to Get Started?</h2>
            <p className="text-xl text-gray-300 mb-8">
              Join thousands of users who trust us with their domain management.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/signup">
                <Button variant="glass" glow={true} size="lg">
                  Create Account
                  <ArrowRightIcon className="ml-2 h-5 w-5" />
                </Button>
              </Link>
              <Link to="/dashboard">
                <Button variant="outline" size="lg">
                  View Dashboard
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
}
