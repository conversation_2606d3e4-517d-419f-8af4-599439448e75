import { useState } from 'react';
import { Link } from 'react-router-dom';
import Card from '../components/ui/Card';
import Button from '../components/ui/Button';
import { 
  CheckCircleIcon,
  XCircleIcon,
  GlobeAltIcon,
  MagnifyingGlassIcon,
  SparklesIcon,
  ShieldCheckIcon,
  BoltIcon,
  ArrowRightIcon,
} from '@heroicons/react/24/outline';
import Layout from '../components/layout/Layout';

// Popular domain extensions with pricing
const domainExtensions = [
  { extension: '.com', price: '$12.99', popular: true },
  { extension: '.io', price: '$39.99', popular: true },
  { extension: '.dev', price: '$15.99', popular: true },
  { extension: '.net', price: '$11.99', popular: false },
  { extension: '.org', price: '$13.99', popular: false },
  { extension: '.app', price: '$18.99', popular: false },
  { extension: '.co', price: '$32.99', popular: false },
  { extension: '.me', price: '$19.99', popular: false },
];

// Features for domain management
const domainFeatures = [
  {
    name: 'Free SSL Certificates',
    description: 'Secure your domain with automatic SSL certificate provisioning and renewal.',
    icon: ShieldCheckIcon,
  },
  {
    name: 'DNS Management',
    description: 'Full control over your DNS records with our intuitive management interface.',
    icon: GlobeAltIcon,
  },
  {
    name: 'Domain Protection',
    description: 'Protect your domain with privacy protection and security features.',
    icon: SparklesIcon,
  },
  {
    name: 'Fast Propagation',
    description: 'Lightning-fast DNS propagation ensures your changes take effect quickly.',
    icon: BoltIcon,
  },
];

export default function Domains() {
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState<Array<{
    domain: string;
    available: boolean;
    price: string;
    extension: string;
  }>>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);

  const handleDomainSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!searchTerm.trim()) return;

    setIsSearching(true);
    setHasSearched(true);

    // Simulate API call for domain availability
    // In production, this would call your backend API
    await new Promise(resolve => setTimeout(resolve, 1500));

    const baseDomain = searchTerm.toLowerCase().replace(/\.(com|io|dev|net|org|app|co|me)$/, '');
    
    const results = domainExtensions.map(ext => ({
      domain: `${baseDomain}${ext.extension}`,
      available: Math.random() > 0.3, // Simulate availability
      price: ext.price,
      extension: ext.extension,
    }));

    setSearchResults(results);
    setIsSearching(false);
  };

  return (
    <Layout>
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">
        {/* Hero Section */}
        <div className="relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-secondary-600/20 to-primary-600/20"></div>
          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
            <div className="text-center">
              <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
                Find Your Perfect
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-secondary-400 to-primary-400 block">
                  Domain Name
                </span>
              </h1>
              <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
                Search for available domains and register them instantly. Get started with your online presence today.
              </p>

              {/* Domain Search Form */}
              <Card className="max-w-4xl mx-auto p-8 bg-white/5 backdrop-blur-sm border border-white/10">
                <form onSubmit={handleDomainSearch} className="space-y-6">
                  <div className="flex flex-col sm:flex-row gap-4">
                    <div className="relative flex-grow">
                      <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-4">
                        <MagnifyingGlassIcon className="h-6 w-6 text-gray-400" />
                      </div>
                      <input
                        type="text"
                        className="block w-full rounded-xl border-0 py-4 pl-12 pr-4 text-white text-lg
                          ring-1 ring-inset ring-white/20 placeholder:text-gray-400 
                          focus:ring-2 focus:ring-inset focus:ring-secondary-500 
                          bg-white/10 backdrop-blur-sm transition-all duration-300
                          hover:bg-white/15 focus:bg-white/15"
                        placeholder="Enter your domain name (e.g., myawesome-site)"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        disabled={isSearching}
                      />
                    </div>
                    <Button 
                      type="submit"
                      variant="glass" 
                      glow={true}
                      size="lg"
                      disabled={isSearching || !searchTerm.trim()}
                      className="px-8"
                    >
                      {isSearching ? (
                        <>
                          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                          Searching...
                        </>
                      ) : (
                        <>
                          Search Domains
                          <ArrowRightIcon className="ml-2 h-5 w-5" />
                        </>
                      )}
                    </Button>
                  </div>
                  
                  {/* Popular Extensions */}
                  <div className="text-center">
                    <p className="text-sm text-gray-400 mb-3">Popular extensions:</p>
                    <div className="flex flex-wrap justify-center gap-2">
                      {domainExtensions.filter(ext => ext.popular).map((ext) => (
                        <button
                          key={ext.extension}
                          type="button"
                          onClick={() => setSearchTerm(searchTerm.replace(/\.(com|io|dev|net|org|app|co|me)$/, '') + ext.extension)}
                          className="px-3 py-1 text-sm bg-white/10 hover:bg-white/20 text-white rounded-lg 
                            border border-white/20 transition-all duration-200"
                        >
                          {ext.extension} - {ext.price}/year
                        </button>
                      ))}
                    </div>
                  </div>
                </form>
              </Card>
            </div>
          </div>
        </div>

        {/* Search Results */}
        {hasSearched && (
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-white mb-4">Domain Availability Results</h2>
              <p className="text-gray-400">Choose from available domains below</p>
            </div>

            {searchResults.length > 0 && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {searchResults.map((result, index) => (
                  <Card 
                    key={result.domain} 
                    className="p-6 bg-white/5 backdrop-blur-sm border border-white/10 hover:border-white/20 transition-all duration-300"
                    style={{ animationDelay: `${index * 100}ms` }}
                  >
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center">
                        {result.available ? (
                          <CheckCircleIcon className="h-6 w-6 text-green-400 mr-3" />
                        ) : (
                          <XCircleIcon className="h-6 w-6 text-red-400 mr-3" />
                        )}
                        <div>
                          <p className="text-lg font-semibold text-white">{result.domain}</p>
                          <p className="text-sm text-gray-400">
                            {result.available ? 'Available' : 'Taken'}
                          </p>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <span className="text-xl font-bold text-secondary-400">{result.price}/year</span>
                      {result.available ? (
                        <Button 
                          variant="glass" 
                          size="sm"
                          glow={true}
                        >
                          Register
                        </Button>
                      ) : (
                        <Button 
                          variant="outline" 
                          size="sm"
                          disabled
                        >
                          Unavailable
                        </Button>
                      )}
                    </div>
                  </Card>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Features Section */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-white mb-4">Why Choose Our Domain Services?</h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              Get more than just a domain name. Enjoy premium features and excellent support.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {domainFeatures.map((feature) => (
              <Card key={feature.name} className="p-6 text-center bg-white/5 backdrop-blur-sm border border-white/10">
                <feature.icon className="h-12 w-12 text-secondary-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-white mb-2">{feature.name}</h3>
                <p className="text-gray-400 text-sm">{feature.description}</p>
              </Card>
            ))}
          </div>
        </div>

        {/* CTA Section */}
        <div className="bg-gradient-to-r from-secondary-600/20 to-primary-600/20 py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-3xl font-bold text-white mb-4">Ready to Get Started?</h2>
            <p className="text-xl text-gray-300 mb-8">
              Join thousands of users who trust us with their domain management.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/signup">
                <Button variant="glass" glow={true} size="lg">
                  Create Account
                  <ArrowRightIcon className="ml-2 h-5 w-5" />
                </Button>
              </Link>
              <Link to="/dashboard">
                <Button variant="outline" size="lg">
                  View Dashboard
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
}
