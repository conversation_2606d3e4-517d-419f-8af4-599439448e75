import { logger } from '../utils/logger';
import { appConfig } from '../config';
import { SharedHostingUser, SharedHostingApplication } from '../models/shared-hosting';
import { SSHConnection } from './shared-hosting';

export class NginxManagerService {
  private nginxSitesAvailable = '/etc/nginx/sites-available';
  private nginxSitesEnabled = '/etc/nginx/sites-enabled';

  private sslCertDir = '/etc/ssl/certs/achidas';

  // Create virtual host for user application
  async createVirtualHost(userId: string, applicationId: string, config: {
    subdomain: string;
    domain?: string;
    port: number;
    type: 'static-website' | 'web-service';
    sslEnabled?: boolean;
    customConfig?: string;
  }): Promise<void> {
    const ssh = new SSHConnection();
    
    try {
      await ssh.connect();

      const user = await SharedHostingUser.findOne({ user_id: userId });
      if (!user) {
        throw new Error('User not found');
      }

      const application = await SharedHostingApplication.findOne({ 
        application_id: applicationId,
        user_id: userId 
      });
      if (!application) {
        throw new Error('Application not found');
      }

      // Generate NGINX configuration
      const nginxConfig = this.generateNginxConfig(user, application, config);
      
      // Create configuration file
      const configFileName = `${config.subdomain}.${appConfig.domain.primary}`;
      const configPath = `${this.nginxSitesAvailable}/${configFileName}`;
      
      // Write configuration to server
      await ssh.executeCommand(`cat > ${configPath} << 'EOF'\n${nginxConfig}\nEOF`);
      
      // Enable site by creating symlink
      const enabledPath = `${this.nginxSitesEnabled}/${configFileName}`;
      await ssh.executeCommand(`ln -sf ${configPath} ${enabledPath}`);
      
      // Test NGINX configuration
      const testResult = await ssh.executeCommand('nginx -t 2>&1');
      if (testResult.includes('failed')) {
        throw new Error(`NGINX configuration test failed: ${testResult}`);
      }
      
      // Reload NGINX
      await ssh.executeCommand('systemctl reload nginx');
      
      // Update application with domain info
      await SharedHostingApplication.findOneAndUpdate(
        { application_id: applicationId },
        { 
          subdomain: `${config.subdomain}.${appConfig.domain.primary}`,
          domain: config.domain,
          ssl_enabled: config.sslEnabled || false,
          nginx_config_path: configPath
        }
      );

      logger.info(`Created virtual host for ${config.subdomain}.${appConfig.domain.primary}`);
      
    } catch (error) {
      logger.error('Failed to create virtual host:', error);
      throw error;
    } finally {
      await ssh.disconnect();
    }
  }

  // Remove virtual host
  async removeVirtualHost(userId: string, applicationId: string): Promise<void> {
    const ssh = new SSHConnection();
    
    try {
      await ssh.connect();

      const application = await SharedHostingApplication.findOne({ 
        application_id: applicationId,
        user_id: userId 
      });
      
      if (!application || !application.subdomain) {
        logger.warn(`No virtual host found for application ${applicationId}`);
        return;
      }

      const configFileName = application.subdomain;
      const configPath = `${this.nginxSitesAvailable}/${configFileName}`;
      const enabledPath = `${this.nginxSitesEnabled}/${configFileName}`;
      
      // Remove symlink and configuration file
      await ssh.executeCommand(`rm -f ${enabledPath} ${configPath}`);
      
      // Test and reload NGINX
      await ssh.executeCommand('nginx -t && systemctl reload nginx');
      
      // Update application
      await SharedHostingApplication.findOneAndUpdate(
        { application_id: applicationId },
        { 
          $unset: { 
            subdomain: 1,
            domain: 1,
            nginx_config_path: 1 
          }
        }
      );

      logger.info(`Removed virtual host for ${configFileName}`);
      
    } catch (error) {
      logger.error('Failed to remove virtual host:', error);
      throw error;
    } finally {
      await ssh.disconnect();
    }
  }

  // Generate NGINX configuration based on application type
  private generateNginxConfig(user: any, application: any, config: any): string {
    const serverName = config.domain ? 
      `${config.subdomain}.${appConfig.domain.primary} ${config.domain}` :
      `${config.subdomain}.${appConfig.domain.primary}`;

    const documentRoot = `${user.home_directory}/apps/${application.type}/${application.name}`;
    
    if (config.type === 'static-website') {
      return this.generateStaticSiteConfig(serverName, documentRoot, config);
    } else {
      return this.generateWebServiceConfig(serverName, config.port, config);
    }
  }

  // Generate static website configuration
  private generateStaticSiteConfig(serverName: string, documentRoot: string, config: any): string {
    const sslConfig = config.sslEnabled ? this.generateSSLConfig(serverName) : '';
    const redirectConfig = config.sslEnabled ? this.generateHTTPSRedirectConfig(serverName) : '';

    return `
${redirectConfig}

server {
    ${config.sslEnabled ? 'listen 443 ssl http2;' : 'listen 80;'}
    server_name ${serverName};
    
    root ${documentRoot};
    index index.html index.htm index.php;
    
    ${sslConfig}
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss;
    
    # Static file caching
    location ~* \\.(jpg|jpeg|png|gif|ico|css|js|pdf|txt)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # PHP support (if needed)
    location ~ \\.php$ {
        try_files $uri =404;
        fastcgi_split_path_info ^(.+\\.php)(/.+)$;
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_index index.php;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param PATH_INFO $fastcgi_path_info;
    }
    
    # Try files
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # Deny access to sensitive files
    location ~ /\\. {
        deny all;
    }
    
    location ~ /(composer\\.json|composer\\.lock|package\\.json|\\.env) {
        deny all;
    }
    
    # Access and error logs
    access_log /var/log/nginx/${serverName.split(' ')[0]}_access.log;
    error_log /var/log/nginx/${serverName.split(' ')[0]}_error.log;
    
    ${config.customConfig || ''}
}`;
  }

  // Generate web service configuration (reverse proxy)
  private generateWebServiceConfig(serverName: string, port: number, config: any): string {
    const sslConfig = config.sslEnabled ? this.generateSSLConfig(serverName) : '';
    const redirectConfig = config.sslEnabled ? this.generateHTTPSRedirectConfig(serverName) : '';

    return `
${redirectConfig}

upstream ${serverName.split(' ')[0].replace(/\./g, '_')}_backend {
    server 127.0.0.1:${port};
}

server {
    ${config.sslEnabled ? 'listen 443 ssl http2;' : 'listen 80;'}
    server_name ${serverName};
    
    ${sslConfig}
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    
    # Proxy settings
    location / {
        proxy_pass http://${serverName.split(' ')[0].replace(/\./g, '_')}_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # Buffer settings
        proxy_buffering on;
        proxy_buffer_size 128k;
        proxy_buffers 4 256k;
        proxy_busy_buffers_size 256k;
    }
    
    # Health check endpoint
    location /health {
        access_log off;
        return 200 "healthy\\n";
        add_header Content-Type text/plain;
    }
    
    # Access and error logs
    access_log /var/log/nginx/${serverName.split(' ')[0]}_access.log;
    error_log /var/log/nginx/${serverName.split(' ')[0]}_error.log;
    
    ${config.customConfig || ''}
}`;
  }

  // Generate SSL configuration
  private generateSSLConfig(serverName: string): string {
    const certName = serverName.split(' ')[0];
    
    return `
    ssl_certificate ${this.sslCertDir}/${certName}.crt;
    ssl_certificate_key ${this.sslCertDir}/${certName}.key;
    
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # HSTS
    add_header Strict-Transport-Security "max-age=63072000" always;`;
  }

  // Generate HTTPS redirect configuration
  private generateHTTPSRedirectConfig(serverName: string): string {
    return `
server {
    listen 80;
    server_name ${serverName};
    return 301 https://$server_name$request_uri;
}`;
  }

  // Update virtual host configuration
  async updateVirtualHost(userId: string, applicationId: string, updates: any): Promise<void> {
    // Remove existing virtual host
    await this.removeVirtualHost(userId, applicationId);
    
    // Create new virtual host with updated configuration
    await this.createVirtualHost(userId, applicationId, updates);
  }

  // Get NGINX status and configuration
  async getNginxStatus(): Promise<any> {
    const ssh = new SSHConnection();
    
    try {
      await ssh.connect();
      
      // Get NGINX status
      const status = await ssh.executeCommand('systemctl is-active nginx');
      
      // Get configuration test result
      const configTest = await ssh.executeCommand('nginx -t 2>&1');
      
      // Get enabled sites count
      const enabledSites = await ssh.executeCommand(`ls -1 ${this.nginxSitesEnabled} | wc -l`);
      
      // Get available sites count
      const availableSites = await ssh.executeCommand(`ls -1 ${this.nginxSitesAvailable} | wc -l`);
      
      return {
        status: status.trim(),
        configValid: !configTest.includes('failed'),
        configTest: configTest.trim(),
        enabledSites: parseInt(enabledSites.trim()),
        availableSites: parseInt(availableSites.trim()),
        timestamp: new Date().toISOString()
      };
      
    } catch (error) {
      logger.error('Failed to get NGINX status:', error);
      throw error;
    } finally {
      await ssh.disconnect();
    }
  }

  // Reload NGINX configuration
  async reloadNginx(): Promise<void> {
    const ssh = new SSHConnection();
    
    try {
      await ssh.connect();
      
      // Test configuration first
      const testResult = await ssh.executeCommand('nginx -t 2>&1');
      if (testResult.includes('failed')) {
        throw new Error(`NGINX configuration test failed: ${testResult}`);
      }
      
      // Reload NGINX
      await ssh.executeCommand('systemctl reload nginx');
      
      logger.info('NGINX configuration reloaded successfully');
      
    } catch (error) {
      logger.error('Failed to reload NGINX:', error);
      throw error;
    } finally {
      await ssh.disconnect();
    }
  }
}

// Export singleton instance
export const nginxManager = new NginxManagerService();
