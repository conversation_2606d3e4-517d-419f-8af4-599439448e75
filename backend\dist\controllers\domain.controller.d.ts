import { FastifyRequest, FastifyReply } from 'fastify';
import { DomainAvailabilityRequest, DomainTransferRequest, WhoisLookupRequest } from '../services/resellerbiz';
export declare function checkDomainAvailabilityController(request: FastifyRequest<{
    Body: DomainAvailabilityRequest;
}>, reply: FastifyReply): Promise<FastifyReply>;
export declare function transferDomainController(request: FastifyRequest<{
    Body: DomainTransferRequest;
}>, reply: FastifyReply): Promise<FastifyReply>;
export declare function whoisLookupController(request: FastifyRequest<{
    Body: WhoisLookupRequest;
}>, reply: FastifyReply): Promise<FastifyReply>;
export declare function getDomainSuggestionsController(request: FastifyRequest<{
    Querystring: {
        keyword: string;
        tlds?: string;
        limit?: string;
    };
}>, reply: FastifyReply): Promise<FastifyReply>;
export declare function getPopularTldsController(_request: FastifyRequest, reply: FastifyReply): Promise<FastifyReply>;
//# sourceMappingURL=domain.controller.d.ts.map