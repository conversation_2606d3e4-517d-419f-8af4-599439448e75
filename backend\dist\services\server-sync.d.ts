import { SSHConnection } from './shared-hosting';
export declare class ServerSyncService {
    private syncInterval;
    private isSyncing;
    constructor();
    startSyncSchedule(): void;
    stopSync(): void;
    syncAllServers(): Promise<void>;
    syncServerToServer(primaryServer: any, secondaryServer: any): Promise<void>;
    syncUserData(primarySSH: SSHConnection, secondarySSH: SSHConnection, user: any, secondaryServer: any): Promise<void>;
    createUserOnSecondary(secondarySSH: SSHConnection, user: any): Promise<void>;
    syncUserServices(primarySSH: SSHConnection, secondarySSH: SSHConnection, user: any): Promise<void>;
    syncNginxConfigs(primarySSH: SSHConnection, secondarySSH: SSHConnection): Promise<void>;
    syncSSLCertificates(primarySSH: SSHConnection, secondarySSH: SSHConnection): Promise<void>;
    triggerManualSync(): Promise<void>;
    getSyncStatus(): {
        isActive: boolean;
        lastSync: Date | null;
        nextSync: Date | null;
    };
}
export declare const serverSync: ServerSyncService;
//# sourceMappingURL=server-sync.d.ts.map