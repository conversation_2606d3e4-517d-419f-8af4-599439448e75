name: lint

on:
  pull_request:
  push:
    branches: [ master ]

env:
  NODE_VERSION: 18.x

jobs:
  lint-js:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          persist-credentials: false
      - name: Use Node.js ${{ env.NODE_VERSION }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
      - name: Check Node.js version
        run: node -pe process.versions
      - name: Install ESLint + ESLint configs/plugins
        run: npm install --only=dev
      - name: Lint files
        run: npm run lint
