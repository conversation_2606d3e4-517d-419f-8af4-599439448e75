# 🚀 UNIFIED API GUIDE - All Services in One Place

## 📍 **BASE URL**
All routes are now consolidated under: `http://localhost:3000/api/v1/services`

## 🏗️ **HOSTING TIER ARCHITECTURE**

### **1. SHARED HOSTING** (Multi-user on existing server)
- **Server**: Uses existing shared server (*************)
- **Isolation**: Linux user isolation with chmod 700
- **Cost**: Ultra-affordable ($2-5/month)
- **Target**: Students, startups, small businesses

### **2. DEDICATED HOSTING** (Individual server per user)
- **Server**: Creates NEW Vultr server instantly
- **Isolation**: Full VM isolation
- **Cost**: Premium but affordable ($5-20/month)
- **Target**: Growing businesses, agencies

### **3. ENTERPRISE HOSTING** (Premium dedicated with enhanced features)
- **Server**: Creates NEW high-spec Vultr server
- **Isolation**: Enterprise-grade with monitoring
- **Cost**: Premium ($20+/month)
- **Target**: Large businesses, SaaS platforms

---

## 🌐 **PUBLIC ROUTES (No Authentication)**

### **Get Server Regions**
```bash
GET /api/v1/services/regions
```

### **Get Server Plans**
```bash
GET /api/v1/services/plans
```

### **Get Hosting Plans**
```bash
GET /api/v1/services/hosting/plans
GET /api/v1/services/hosting/plans/recommend
GET /api/v1/services/hosting/plans/starter
```

---

## 🔐 **PROTECTED ROUTES (Authentication Required)**

### **SHARED HOSTING ROUTES**

#### **Create Shared User**
```bash
POST /api/v1/services/hosting/shared/users
Authorization: Bearer your-jwt-token
Content-Type: application/json

{
  "user_id": "user123",
  "username": "myapp",
  "plan": "starter",
  "server_id": "optional-server-id"
}
```

#### **Create Shared Application**
```bash
POST /api/v1/services/hosting/shared/users/{userId}/applications
Authorization: Bearer your-jwt-token
Content-Type: application/json

{
  "name": "my-nodejs-app",
  "type": "web-service",           // or "static-website"
  "framework": "nodejs",           // nodejs, python, rust, php, go, java, dotnet, ruby, html, react, vue, angular
  "domain": "custom-domain.com",   // optional
  "git_repo": "https://github.com/user/repo", // optional
  "build_command": "npm install",  // optional - uses your package.json
  "start_command": "npm start"     // optional - uses your package.json
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "app_1703123456789_abc123",
    "name": "my-nodejs-app",
    "type": "web-service",
    "framework": "nodejs",
    "subdomain": "mynodejsapp-user123",
    "directory": "/var/www/user_user123/apps/web-service/my-nodejs-app",
    "status": "running",
    "port": 3001,
    "ssl_enabled": true,
    "created_at": "2024-12-21T10:30:45.789Z"
  }
}
```

### **DEDICATED/ENTERPRISE SERVER ROUTES**

#### **Provision New Server**
```bash
POST /api/v1/services/provision
Authorization: Bearer your-jwt-token
Content-Type: application/json

{
  "plan": "vc2-1c-1gb",           // Vultr plan ID
  "region": "jnb",                // Johannesburg, South Africa
  "hostname": "my-dedicated-app",
  "tier": "dedicated",            // "dedicated" or "enterprise"
  "os": "Ubuntu 22.04 LTS"        // optional
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "vultr-instance-id",
    "hostname": "my-dedicated-app",
    "main_ip": "45.76.123.456",
    "region": "jnb",
    "plan": "vc2-1c-1gb",
    "tier": "dedicated",
    "status": "active",
    "created_at": "2024-12-21T10:30:45.789Z"
  }
}
```

### **APPLICATION MANAGEMENT ROUTES (All Tiers)**

#### **Create Application (General)**
```bash
POST /api/v1/services/applications
Authorization: Bearer your-jwt-token
Content-Type: application/json

{
  "name": "My Application",
  "description": "My awesome app",
  "repository": {
    "url": "https://github.com/user/repo",
    "branch": "main",
    "provider": "github"
  },
  "runtime_config": {
    "runtime_type": "nodejs",
    "version": "18",
    "build_command": "npm install",
    "start_command": "npm start"
  },
  "environment": {
    "NODE_ENV": "production",
    "PORT": "3000"
  },
  "hosting_tier": "shared"        // "shared", "dedicated", or "enterprise"
}
```

#### **Get All Applications**
```bash
GET /api/v1/services/applications
Authorization: Bearer your-jwt-token
```

#### **Get Application by ID**
```bash
GET /api/v1/services/applications/{id}
Authorization: Bearer your-jwt-token
```

#### **Update Application**
```bash
PUT /api/v1/services/applications/{id}
Authorization: Bearer your-jwt-token
Content-Type: application/json

{
  "name": "Updated App Name",
  "description": "Updated description"
}
```

#### **Deploy Application**
```bash
POST /api/v1/services/applications/{id}/deploy
Authorization: Bearer your-jwt-token
```

#### **Stop Application**
```bash
POST /api/v1/services/applications/{id}/stop
Authorization: Bearer your-jwt-token
```

#### **Delete Application**
```bash
DELETE /api/v1/services/applications/{id}
Authorization: Bearer your-jwt-token
```

#### **Get Application Statistics**
```bash
GET /api/v1/services/applications/stats
Authorization: Bearer your-jwt-token
```

### **SERVER MANAGEMENT ROUTES**

#### **Get All Servers**
```bash
GET /api/v1/services/
Authorization: Bearer your-jwt-token
```

#### **Get Server by ID**
```bash
GET /api/v1/services/{serverId}
Authorization: Bearer your-jwt-token
```

#### **Get Server Metrics**
```bash
GET /api/v1/services/{serverId}/metrics
Authorization: Bearer your-jwt-token
```

#### **Get Shared Server Status**
```bash
GET /api/v1/services/shared/status
Authorization: Bearer your-jwt-token
```

---

## 🎯 **QUICK START EXAMPLES**

### **Example 1: Create Shared Node.js App**
```bash
# Step 1: Create shared user (if not exists)
curl -X POST "http://localhost:3000/api/v1/services/hosting/shared/users" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{"user_id": "user123", "username": "myapp", "plan": "starter"}'

# Step 2: Create Node.js application
curl -X POST "http://localhost:3000/api/v1/services/hosting/shared/users/user123/applications" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "my-api",
    "type": "web-service",
    "framework": "nodejs"
  }'
```

### **Example 2: Create Dedicated Server for Python App**
```bash
curl -X POST "http://localhost:3000/api/v1/services/provision" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{
    "plan": "vc2-2c-4gb",
    "region": "jnb",
    "hostname": "python-microservice",
    "tier": "dedicated"
  }'
```

### **Example 3: Create Static Website (React)**
```bash
curl -X POST "http://localhost:3000/api/v1/services/hosting/shared/users/user123/applications" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "my-react-site",
    "type": "static-website",
    "framework": "react",
    "build_command": "npm run build"
  }'
```

---

## ✨ **KEY FEATURES**

1. **🔄 User-Driven Dependencies**: Uses YOUR package.json, requirements.txt, etc.
2. **⚡ Instant Deployment**: Shared hosting deploys immediately
3. **🌍 Real Infrastructure**: Dedicated/Enterprise creates actual Vultr servers
4. **🏃‍♂️ No Files Required Initially**: Just specify language/framework
5. **🌍 African-Optimized**: Servers in Johannesburg for low latency
6. **🔒 Secure Isolation**: Linux user isolation for shared, VM isolation for dedicated
7. **💰 Intelligent Pricing**: Pay-as-you-go with African market focus

**All routes are now unified under `/api/v1/services` - one endpoint for all your hosting needs!**
