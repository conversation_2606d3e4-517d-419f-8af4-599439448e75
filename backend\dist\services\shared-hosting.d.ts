export declare class SSHConnection {
    private client;
    private config;
    constructor();
    connect(): Promise<void>;
    executeCommand(command: string): Promise<string>;
    executeCommandDetailed(command: string): Promise<{
        stdout: string;
        stderr: string;
    }>;
    disconnect(): Promise<void>;
}
export interface SharedHostingUser {
    id: string;
    user_id: string;
    username: string;
    linux_username: string;
    home_directory: string;
    plan: string;
    status: 'active' | 'suspended' | 'disabled';
    server_id: string;
    server_ip: string;
    port: number;
    ssh_port: number;
    ftp_port: number;
    resource_limits: {
        cpu_quota: number;
        memory_max: number;
        bandwidth_limit: number;
        storage_limit: number;
    };
    usage: {
        cpu_usage: number;
        memory_usage: number;
        bandwidth_used: number;
        storage_used: number;
    };
    created_at: string;
    last_login?: string;
    applications: SharedHostingApplication[];
}
export interface SharedHostingApplication {
    id: string;
    name: string;
    type: 'static-website' | 'web-service';
    framework?: 'html' | 'react' | 'vue' | 'angular' | 'nodejs' | 'python' | 'rust' | 'php' | 'go' | 'java' | 'dotnet' | 'ruby';
    domain?: string;
    subdomain: string;
    directory: string;
    status: 'running' | 'stopped' | 'building' | 'error';
    port?: number;
    ssl_enabled: boolean;
    created_at: string;
    last_deployed?: string;
}
export interface CreateSharedUserRequest {
    user_id: string;
    username: string;
    plan: string;
    server_id?: string;
}
export interface CreateApplicationRequest {
    name: string;
    type: 'static-website' | 'web-service';
    framework?: 'html' | 'react' | 'vue' | 'angular' | 'nodejs' | 'python' | 'rust' | 'php' | 'go' | 'java' | 'dotnet' | 'ruby';
    domain?: string;
    git_repo?: string;
    build_command?: string;
    start_command?: string;
}
export interface FileStructureItem {
    name: string;
    type: 'file' | 'directory';
    size?: number;
    permissions: string;
    owner: string;
    modified: string;
    path: string;
}
export interface SharedHostingServer {
    id: string;
    ip_address: string;
    hostname: string;
    region: string;
    status: 'active' | 'maintenance' | 'offline';
    max_users: number;
    current_users: number;
    cpu_usage: number;
    memory_usage: number;
    disk_usage: number;
    load_average: number;
    last_sync: Date;
    created_at: Date;
}
export interface PaymentStatus {
    user_id: string;
    status: 'active' | 'grace_period' | 'suspended' | 'deleted';
    last_payment_date?: Date;
    grace_period_start?: Date;
    grace_period_end?: Date;
    grace_period_days: number;
    deletion_scheduled?: Date;
}
declare class SharedHostingService {
    private readonly BASE_PORT;
    private readonly SSH_BASE_PORT;
    private readonly FTP_BASE_PORT;
    private readonly MAX_USERS_PER_SERVER;
    private serverPool;
    private paymentStatuses;
    constructor();
    private initializeProductionServices;
    private initializeServerMetrics;
    private initializeServerPool;
    private initializePaymentTracking;
    private getOptimalServer;
    private updateServerMetrics;
    updateUserPaymentStatus(userId: string, status: 'active' | 'grace_period' | 'suspended' | 'deleted', gracePeriodDays?: number): Promise<void>;
    processPaymentBasedLifecycle(): Promise<void>;
    getUserPaymentStatus(userId: string): PaymentStatus | undefined;
    getServerPoolStatus(): SharedHostingServer[];
    addServerToPool(serverConfig: Omit<SharedHostingServer, 'current_users' | 'cpu_usage' | 'memory_usage' | 'disk_usage' | 'load_average' | 'last_sync' | 'created_at'>): Promise<void>;
    removeServerFromPool(serverId: string): Promise<void>;
    synchronizeServers(): Promise<void>;
    private syncServerToServer;
    startAutoSync(): void;
    stopAutoSync(): void;
    startPaymentLifecycleMonitoring(): void;
    stopPaymentLifecycleMonitoring(): void;
    createUser(userData: CreateSharedUserRequest): Promise<SharedHostingUser>;
    private formatUserResponse;
    private mapStatus;
    private updateServerUserCount;
    private createPaymentStatus;
    private getStartDateForPeriod;
    collectUsageAnalytics(): Promise<void>;
    private collectUserUsageData;
    private storeUsageAnalytics;
    private parseNetworkStats;
    startAnalyticsCollection(): void;
    stopAnalyticsCollection(): void;
    private cleanupOldAnalytics;
    shutdown(): void;
    testSSHConnection(): Promise<any>;
    private createLinuxUser;
    private setupResourceLimits;
    private createUserDirectoryStructure;
    private setupBandwidthThrottling;
    private getPlanLimits;
    private getNextAvailablePort;
    private getNextAvailableSSHPort;
    private getNextAvailableFTPPort;
    private generateSubdomain;
    private generateUniqueSubdomain;
    private isSubdomainUnique;
    createApplication(userId: string, appData: CreateApplicationRequest): Promise<SharedHostingApplication>;
    private setupApplicationEnvironment;
    private setupStaticWebsite;
    private getFrameworkInfo;
    private createReactFiles;
    private createVueFiles;
    private createAngularFiles;
    private setupWebService;
    private installFrameworkRuntime;
    private installNodeJS;
    private installPython;
    private installRust;
    private installPHP;
    private installGo;
    private installJava;
    private installDotNet;
    private installRuby;
    private setupNodeJSService;
    private setupPythonService;
    private setupRustService;
    private setupPHPService;
    private setupGoService;
    private setupJavaService;
    private setupDotNetService;
    private setupRubyService;
    getUsers(filters: {
        page: number;
        limit: number;
        status?: string;
        plan?: string;
        server_id?: string;
        search?: string;
    }): Promise<{
        users: SharedHostingUser[];
        total: number;
        page: number;
        limit: number;
        totalPages: number;
    }>;
    getUser(userId: string): Promise<SharedHostingUser | null>;
    updateUser(userId: string, updateData: {
        plan?: string;
        status?: 'active' | 'suspended' | 'disabled';
        resource_limits?: {
            cpu_quota?: number;
            memory_max?: number;
            bandwidth_limit?: number;
            storage_limit?: number;
        };
    }): Promise<SharedHostingUser>;
    deleteUser(userId: string): Promise<void>;
    getAnalytics(period: 'day' | 'week' | 'month' | 'year', serverId?: string): Promise<any>;
    getUserResourceUsage(userId: string, period: 'day' | 'week' | 'month'): Promise<any>;
    getServerCapacity(serverId?: string): Promise<any>;
    suspendUser(userId: string, reason?: string): Promise<void>;
    reactivateUser(userId: string): Promise<void>;
    resetUserPassword(userId: string, newPassword?: string): Promise<{
        password: string;
    }>;
    getUserApplications(userId: string, filters: {
        page: number;
        limit: number;
        status?: string;
        type?: string;
    }): Promise<{
        applications: any[];
        total: number;
        page: number;
        limit: number;
        totalPages: number;
    }>;
    updateApplication(userId: string, appId: string, updateData: {
        name?: string;
        domain?: string;
        git_repo?: string;
        build_command?: string;
        start_command?: string;
        status?: 'running' | 'stopped';
    }): Promise<any>;
    deleteApplication(userId: string, appId: string): Promise<void>;
}
export declare function getSharedHostingService(): SharedHostingService;
export {};
//# sourceMappingURL=shared-hosting.d.ts.map