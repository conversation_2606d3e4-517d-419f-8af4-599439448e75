import { logger } from '../utils/logger';
import { exec } from 'child_process';
import { promisify } from 'util';
import * as fs from 'fs/promises';
import * as path from 'path';
import { Client } from 'ssh2';
import { appConfig } from '../config';
import {
  SharedHostingUser,
  SharedHostingApplication,
  ServerMetrics,
  PaymentStatus,
  UserUsageAnalytics,
  ISharedHostingUser,
  ISharedHostingApplication,
  IServerMetrics,
  IPaymentStatus,
  IUserUsageAnalytics
} from '../models/shared-hosting';
import { getApplicationService } from './application';
import { CloudflareDNSService } from './cloudflare-dns';

const execAsync = promisify(exec);

// SSH Connection utility for shared server management
export class SSHConnection {
  private client: Client;
  private config: {
    host: string;
    port: number;
    username: string;
    password?: string;
    privateKey?: Buffer;
    passphrase?: string;
  };

  constructor() {
    this.client = new Client();

    // Use shared server configuration from environment
    const sharedServerIP = process.env['SHARED_SERVER_IP'] || '*************';
    const sshPort = parseInt(process.env['SHARED_SERVER_SSH_PORT'] || '22');
    const sshUser = process.env['SHARED_SERVER_SSH_USER'] || 'root';
    const sshPassword = process.env['SHARED_SERVER_SSH_PASSWORD'];
    const sshKeyPath = process.env['SHARED_SERVER_SSH_KEY_PATH'];

    this.config = {
      host: sharedServerIP,
      port: sshPort,
      username: sshUser,
      ...(sshPassword && { password: sshPassword }),
      ...(sshKeyPath && {
        privateKey: require('fs').readFileSync(sshKeyPath)
      })
    };

    logger.info(`SSH Configuration initialized:`, {
      host: this.config.host,
      port: this.config.port,
      username: this.config.username,
      hasPassword: !!this.config.password,
      hasPrivateKey: !!this.config.privateKey
    });
  }

  async connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      logger.info(`Attempting SSH connection to ${this.config.host}:${this.config.port} as ${this.config.username}`);

      const timeout = setTimeout(() => {
        logger.error(`SSH connection timeout after 30 seconds to ${this.config.host}`);
        this.client.destroy();
        reject(new Error(`SSH connection timeout to ${this.config.host}`));
      }, 30000);

      this.client.on('ready', () => {
        clearTimeout(timeout);
        logger.info(`SSH successfully connected to ${this.config.host}:${this.config.port}`);
        resolve();
      });

      this.client.on('error', (err) => {
        clearTimeout(timeout);
        logger.error(`SSH connection error to ${this.config.host}:${this.config.port}:`, {
          error: err.message,
          code: (err as any).code,
          level: (err as any).level,
          description: (err as any).description
        });
        reject(err);
      });

      this.client.on('close', () => {
        logger.debug(`SSH connection closed to ${this.config.host}`);
      });

      try {
        this.client.connect(this.config);
      } catch (error) {
        clearTimeout(timeout);
        logger.error(`Failed to initiate SSH connection to ${this.config.host}:`, error);
        reject(error);
      }
    });
  }

  async executeCommand(command: string): Promise<{ stdout: string; stderr: string }> {
    return new Promise((resolve, reject) => {
      this.client.exec(command, (err, stream) => {
        if (err) {
          reject(err);
          return;
        }

        let stdout = '';
        let stderr = '';

        stream.on('close', (code: number) => {
          if (code !== 0) {
            logger.warn(`Command exited with code ${code}: ${command}`);
          }
          resolve({ stdout, stderr });
        });

        stream.on('data', (data: Buffer) => {
          stdout += data.toString();
        });

        stream.stderr.on('data', (data: Buffer) => {
          stderr += data.toString();
        });
      });
    });
  }

  async disconnect(): Promise<void> {
    this.client.end();
    logger.info('SSH connection closed');
  }
}

// Shared hosting user types
export interface SharedHostingUser {
  id: string;
  user_id: string;
  username: string;
  linux_username: string;
  home_directory: string;
  plan: string;
  status: 'active' | 'suspended' | 'disabled';
  server_id: string;
  server_ip: string;
  port: number;
  ssh_port: number;
  ftp_port: number;
  resource_limits: {
    cpu_quota: number; // percentage
    memory_max: number; // MB
    bandwidth_limit: number; // GB/month
    storage_limit: number; // GB
  };
  usage: {
    cpu_usage: number;
    memory_usage: number;
    bandwidth_used: number;
    storage_used: number;
  };
  created_at: string;
  last_login?: string;
  applications: SharedHostingApplication[];
}

export interface SharedHostingApplication {
  id: string;
  name: string;
  type: 'static-website' | 'web-service';
  framework?: 'html' | 'react' | 'vue' | 'angular' | 'nodejs' | 'python' | 'rust' | 'php' | 'go' | 'java' | 'dotnet' | 'ruby';
  domain?: string;
  subdomain: string;
  directory: string;
  status: 'running' | 'stopped' | 'building' | 'error';
  port?: number;
  ssl_enabled: boolean;
  created_at: string;
  last_deployed?: string;
}

export interface CreateSharedUserRequest {
  user_id: string;
  username: string;
  plan: string;
  server_id?: string;
}

export interface CreateApplicationRequest {
  name: string;
  type: 'static-website' | 'web-service';
  framework?: 'html' | 'react' | 'vue' | 'angular' | 'nodejs' | 'python' | 'rust' | 'php' | 'go' | 'java' | 'dotnet' | 'ruby';
  domain?: string;
  git_repo?: string;
  build_command?: string;
  start_command?: string;
}

export interface FileStructureItem {
  name: string;
  type: 'file' | 'directory';
  size?: number;
  permissions: string;
  owner: string;
  modified: string;
  path: string;
}

// Server pool interface for load balancing
export interface SharedHostingServer {
  id: string;
  ip_address: string;
  hostname: string;
  region: string;
  status: 'active' | 'maintenance' | 'offline';
  max_users: number;
  current_users: number;
  cpu_usage: number;
  memory_usage: number;
  disk_usage: number;
  load_average: number;
  last_sync: Date;
  created_at: Date;
}

// Payment status interface
export interface PaymentStatus {
  user_id: string;
  status: 'active' | 'grace_period' | 'suspended' | 'deleted';
  last_payment_date?: Date;
  grace_period_start?: Date;
  grace_period_end?: Date;
  grace_period_days: number; // 30 or 60 days
  deletion_scheduled?: Date;
}

class SharedHostingService {
  private readonly BASE_PORT = appConfig.sharedHosting.basePort;
  private readonly SSH_BASE_PORT = appConfig.sharedHosting.sshBasePort;
  private readonly FTP_BASE_PORT = appConfig.sharedHosting.ftpBasePort;
  private readonly MAX_USERS_PER_SERVER = appConfig.sharedHosting.maxUsersPerServer;

  // Server pool for load balancing
  private serverPool: SharedHostingServer[] = [];

  // Payment tracking
  private paymentStatuses: Map<string, PaymentStatus> = new Map();

  constructor() {
    this.initializeServerPool();
    this.initializePaymentTracking();
    this.initializeProductionServices();
  }

  // Initialize all production services
  private initializeProductionServices(): void {
    // Start analytics collection service
    this.startAnalyticsCollection();

    // Initialize server metrics collection
    this.initializeServerMetrics();

    logger.info('All production services initialized');
  }

  // Initialize server metrics collection
  private async initializeServerMetrics(): Promise<void> {
    try {
      // Update server metrics every 2 minutes
      const metricsInterval = setInterval(async () => {
        try {
          await this.updateServerMetrics();
        } catch (error) {
          logger.error('Server metrics update error:', error);
        }
      }, 2 * 60 * 1000); // 2 minutes

      // Store interval reference for cleanup
      (this as any).metricsInterval = metricsInterval;

      logger.info('Server metrics collection initialized');
    } catch (error) {
      logger.error('Failed to initialize server metrics:', error);
    }
  }

  // Initialize server pool based on environment configuration
  private async initializeServerPool(): Promise<void> {
    try {
      // Get server configuration from environment
      const serverConfig = process.env['SHARED_HOSTING_SERVERS'];

      if (serverConfig) {
        // Parse multiple servers from environment (JSON format)
        const servers = JSON.parse(serverConfig);
        this.serverPool = servers.map((server: any) => ({
          ...server,
          last_sync: new Date(),
          created_at: new Date()
        }));
      } else {
        // Default to single server setup using SSH configuration
        const defaultServerIp = appConfig.ssh.host || '127.0.0.1';
        this.serverPool = [{
          id: 'default-shared-01',
          ip_address: defaultServerIp,
          hostname: 'achidas-shared-1',
          region: 'default',
          status: 'active' as const,
          max_users: this.MAX_USERS_PER_SERVER,
          current_users: 0,
          cpu_usage: 0,
          memory_usage: 0,
          disk_usage: 0,
          load_average: 0,
          last_sync: new Date(),
          created_at: new Date()
        }];
      }

      logger.info(`Initialized server pool with ${this.serverPool.length} servers`);
    } catch (error) {
      logger.error('Failed to initialize server pool:', error);
      // Fallback to single server
      const defaultServerIp = appConfig.ssh.host || '127.0.0.1';
      this.serverPool = [{
        id: 'default-shared-01',
        ip_address: defaultServerIp,
        hostname: 'achidas-shared-1',
        region: 'default',
        status: 'active' as const,
        max_users: this.MAX_USERS_PER_SERVER,
        current_users: 0,
        cpu_usage: 0,
        memory_usage: 0,
        disk_usage: 0,
        load_average: 0,
        last_sync: new Date(),
        created_at: new Date()
      }];
    }
  }

  // Initialize payment tracking system
  private async initializePaymentTracking(): Promise<void> {
    try {
      // Start payment monitoring interval
      const checkInterval = appConfig.payment.checkIntervalHours * 60 * 60 * 1000;

      setInterval(async () => {
        try {
          await this.processPaymentBasedLifecycle();
        } catch (error) {
          logger.error('Payment lifecycle processing failed:', error);
        }
      }, checkInterval);

      logger.info(`Payment tracking system initialized with ${appConfig.payment.checkIntervalHours}h check interval`);
    } catch (error) {
      logger.error('Failed to initialize payment tracking:', error);
    }
  }

  // Get optimal server for new user allocation
  private async getOptimalServer(): Promise<SharedHostingServer> {
    // Update server metrics before selection
    await this.updateServerMetrics();

    // Filter active servers with capacity
    const availableServers = this.serverPool.filter(server =>
      server.status === 'active' &&
      server.current_users < server.max_users
    );

    if (availableServers.length === 0) {
      throw new Error('No available servers with capacity');
    }

    // Single server mode
    if (availableServers.length === 1) {
      return availableServers[0]!;
    }

    // Load balancing algorithm: prefer servers with lower utilization
    const sortedServers = availableServers.sort((a, b) => {
      const utilizationA = (a.current_users / a.max_users) + (a.cpu_usage / 100) + (a.memory_usage / 100);
      const utilizationB = (b.current_users / b.max_users) + (b.cpu_usage / 100) + (b.memory_usage / 100);
      return utilizationA - utilizationB;
    });

    return sortedServers[0]!;
  }

  // Update server metrics for all servers in pool
  private async updateServerMetrics(): Promise<void> {
    const updatePromises = this.serverPool.map(async (server) => {
      try {
        // Create SSH connection with server-specific config
        const ssh = new SSHConnection();
        // Override the default config for this specific server
        (ssh as any).config.host = server.ip_address;
        await ssh.connect();

        // Get current user count
        const userCountResult = await ssh.executeCommand("ls /home | grep '^user_' | wc -l");
        server.current_users = parseInt(userCountResult.stdout.trim()) || 0;

        // Get CPU usage
        const cpuResult = await ssh.executeCommand("top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1");
        server.cpu_usage = parseFloat(cpuResult.stdout.trim()) || 0;

        // Get memory usage
        const memResult = await ssh.executeCommand("free | grep Mem | awk '{printf \"%.1f\", $3/$2 * 100.0}'");
        server.memory_usage = parseFloat(memResult.stdout.trim()) || 0;

        // Get disk usage
        const diskResult = await ssh.executeCommand("df / | tail -1 | awk '{print $5}' | cut -d'%' -f1");
        server.disk_usage = parseFloat(diskResult.stdout.trim()) || 0;

        // Get load average
        const loadResult = await ssh.executeCommand("uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | cut -d',' -f1");
        server.load_average = parseFloat(loadResult.stdout.trim()) || 0;

        server.last_sync = new Date();
        await ssh.disconnect();
      } catch (error) {
        logger.error(`Failed to update metrics for server ${server.id}:`, error);
        server.status = 'offline';
      }
    });

    await Promise.allSettled(updatePromises);
  }

  // Initialize payment status for new user
  private async initializeUserPaymentStatus(userId: string): Promise<void> {
    const paymentStatus: PaymentStatus = {
      user_id: userId,
      status: 'active',
      last_payment_date: new Date(),
      grace_period_days: 30, // Default 30 days grace period
    };

    this.paymentStatuses.set(userId, paymentStatus);
    logger.info(`Initialized payment status for user: ${userId}`);
  }

  // Update user payment status
  async updateUserPaymentStatus(userId: string, status: 'active' | 'grace_period' | 'suspended' | 'deleted', gracePeriodDays?: number): Promise<void> {
    const paymentStatus = this.paymentStatuses.get(userId);

    if (!paymentStatus) {
      throw new Error(`Payment status not found for user: ${userId}`);
    }

    const now = new Date();

    switch (status) {
      case 'active':
        paymentStatus.status = 'active';
        paymentStatus.last_payment_date = now;
        delete paymentStatus.grace_period_start;
        delete paymentStatus.grace_period_end;
        delete paymentStatus.deletion_scheduled;
        break;

      case 'grace_period':
        paymentStatus.status = 'grace_period';
        paymentStatus.grace_period_start = now;
        paymentStatus.grace_period_days = gracePeriodDays || 30;

        const graceEnd = new Date(now);
        graceEnd.setDate(graceEnd.getDate() + paymentStatus.grace_period_days);
        paymentStatus.grace_period_end = graceEnd;

        const deletionDate = new Date(graceEnd);
        deletionDate.setDate(deletionDate.getDate() + 7); // 7 days after grace period
        paymentStatus.deletion_scheduled = deletionDate;
        break;

      case 'suspended':
        paymentStatus.status = 'suspended';
        break;

      case 'deleted':
        paymentStatus.status = 'deleted';
        break;
    }

    this.paymentStatuses.set(userId, paymentStatus);
    logger.info(`Updated payment status for user ${userId} to: ${status}`);
  }

  // Check and process payment-based user lifecycle
  async processPaymentBasedLifecycle(): Promise<void> {
    const now = new Date();

    for (const [userId, paymentStatus] of Array.from(this.paymentStatuses.entries())) {
      try {
        switch (paymentStatus.status) {
          case 'active':
            // Check if payment is overdue (implement your payment check logic here)
            const daysSinceLastPayment = Math.floor((now.getTime() - (paymentStatus.last_payment_date?.getTime() || 0)) / (1000 * 60 * 60 * 24));

            if (daysSinceLastPayment > 35) { // 5 days past due
              await this.updateUserPaymentStatus(userId, 'grace_period');
              await this.suspendUser(userId, 'Payment overdue - entering grace period');
            }
            break;

          case 'grace_period':
            if (paymentStatus.grace_period_end && now > paymentStatus.grace_period_end) {
              await this.updateUserPaymentStatus(userId, 'suspended');
              logger.warn(`User ${userId} grace period expired, scheduling for deletion`);
            }
            break;

          case 'suspended':
            if (paymentStatus.deletion_scheduled && now > paymentStatus.deletion_scheduled) {
              await this.deleteUser(userId);
              await this.updateUserPaymentStatus(userId, 'deleted');
              logger.info(`User ${userId} deleted due to non-payment`);
            }
            break;
        }
      } catch (error) {
        logger.error(`Failed to process payment lifecycle for user ${userId}:`, error);
      }
    }
  }

  // Get payment status for user
  getUserPaymentStatus(userId: string): PaymentStatus | undefined {
    return this.paymentStatuses.get(userId);
  }

  // Get server pool status
  getServerPoolStatus(): SharedHostingServer[] {
    return [...this.serverPool];
  }

  // Add new server to pool
  async addServerToPool(serverConfig: Omit<SharedHostingServer, 'current_users' | 'cpu_usage' | 'memory_usage' | 'disk_usage' | 'load_average' | 'last_sync' | 'created_at'>): Promise<void> {
    const newServer: SharedHostingServer = {
      ...serverConfig,
      current_users: 0,
      cpu_usage: 0,
      memory_usage: 0,
      disk_usage: 0,
      load_average: 0,
      last_sync: new Date(),
      created_at: new Date()
    };

    this.serverPool.push(newServer);
    logger.info(`Added new server to pool: ${newServer.id}`);
  }

  // Remove server from pool
  async removeServerFromPool(serverId: string): Promise<void> {
    const serverIndex = this.serverPool.findIndex(server => server.id === serverId);

    if (serverIndex === -1) {
      throw new Error(`Server not found: ${serverId}`);
    }

    const server = this.serverPool[serverIndex]!;

    if (server.current_users > 0) {
      throw new Error(`Cannot remove server ${serverId} - still has ${server.current_users} users`);
    }

    this.serverPool.splice(serverIndex, 1);
    logger.info(`Removed server from pool: ${serverId}`);
  }

  // Synchronize files between servers using rsync
  async synchronizeServers(): Promise<void> {
    if (this.serverPool.length <= 1) {
      logger.info('Single server mode - no synchronization needed');
      return;
    }

    const activeServers = this.serverPool.filter(server => server.status === 'active');

    if (activeServers.length <= 1) {
      logger.info('Only one active server - no synchronization needed');
      return;
    }

    // Use first server as primary source
    const primaryServer = activeServers[0]!;
    const secondaryServers = activeServers.slice(1);

    logger.info(`Starting synchronization from primary server: ${primaryServer.id}`);

    const syncPromises = secondaryServers.map(async (targetServer) => {
      try {
        await this.syncServerToServer(primaryServer, targetServer);
        logger.info(`Synchronized ${primaryServer.id} -> ${targetServer.id}`);
      } catch (error) {
        logger.error(`Failed to sync ${primaryServer.id} -> ${targetServer.id}:`, error);
      }
    });

    await Promise.allSettled(syncPromises);
    logger.info('Server synchronization completed');
  }

  // Sync files from source server to target server
  private async syncServerToServer(sourceServer: SharedHostingServer, targetServer: SharedHostingServer): Promise<void> {
    const ssh = new SSHConnection();
    try {
      // Connect to target server
      (ssh as any).config.host = targetServer.ip_address;
      await ssh.connect();

      // Rsync command to sync from source to target
      // Sync user home directories and web files
      const rsyncCommand = `rsync -avz --delete -e "ssh -o StrictHostKeyChecking=no" root@${sourceServer.ip_address}:/var/www/ /var/www/`;

      await ssh.executeCommand(rsyncCommand);

      // Also sync user accounts (passwd, shadow, group files)
      const userSyncCommands = [
        `rsync -avz -e "ssh -o StrictHostKeyChecking=no" root@${sourceServer.ip_address}:/etc/passwd /etc/passwd.new`,
        `rsync -avz -e "ssh -o StrictHostKeyChecking=no" root@${sourceServer.ip_address}:/etc/shadow /etc/shadow.new`,
        `rsync -avz -e "ssh -o StrictHostKeyChecking=no" root@${sourceServer.ip_address}:/etc/group /etc/group.new`,
        // Backup current files and replace
        `cp /etc/passwd /etc/passwd.backup`,
        `cp /etc/shadow /etc/shadow.backup`,
        `cp /etc/group /etc/group.backup`,
        `mv /etc/passwd.new /etc/passwd`,
        `mv /etc/shadow.new /etc/shadow`,
        `mv /etc/group.new /etc/group`
      ];

      for (const command of userSyncCommands) {
        await ssh.executeCommand(command);
      }

      // Update target server's last sync time
      targetServer.last_sync = new Date();

    } catch (error) {
      logger.error(`Rsync failed between ${sourceServer.id} and ${targetServer.id}:`, error);
      throw error;
    } finally {
      await ssh.disconnect();
    }
  }

  // Start automatic synchronization (every 5 minutes)
  startAutoSync(): void {
    // Clear any existing interval
    if ((this as any).syncInterval) {
      clearInterval((this as any).syncInterval);
    }

    // Set up 5-minute sync interval
    (this as any).syncInterval = setInterval(async () => {
      try {
        await this.synchronizeServers();
      } catch (error) {
        logger.error('Auto-sync failed:', error);
      }
    }, 5 * 60 * 1000); // 5 minutes

    logger.info('Auto-sync started (5-minute intervals)');
  }

  // Stop automatic synchronization
  stopAutoSync(): void {
    if ((this as any).syncInterval) {
      clearInterval((this as any).syncInterval);
      (this as any).syncInterval = null;
      logger.info('Auto-sync stopped');
    }
  }

  // Start payment lifecycle monitoring (daily check)
  startPaymentLifecycleMonitoring(): void {
    // Clear any existing interval
    if ((this as any).paymentInterval) {
      clearInterval((this as any).paymentInterval);
    }

    // Set up daily payment check
    (this as any).paymentInterval = setInterval(async () => {
      try {
        await this.processPaymentBasedLifecycle();
      } catch (error) {
        logger.error('Payment lifecycle processing failed:', error);
      }
    }, 24 * 60 * 60 * 1000); // 24 hours

    logger.info('Payment lifecycle monitoring started (daily checks)');
  }

  // Stop payment lifecycle monitoring
  stopPaymentLifecycleMonitoring(): void {
    if ((this as any).paymentInterval) {
      clearInterval((this as any).paymentInterval);
      (this as any).paymentInterval = null;
      logger.info('Payment lifecycle monitoring stopped');
    }
  }

  // Create a new shared hosting user with Linux isolation
  async createUser(userData: CreateSharedUserRequest): Promise<SharedHostingUser> {
    const startTime = Date.now();
    logger.info(`Starting user creation process for: ${userData.username}`, {
      user_id: userData.user_id,
      username: userData.username,
      plan: userData.plan
    });

    try {
      // Check if user already exists
      logger.info(`Checking for existing user: ${userData.username}`);
      const existingUser = await SharedHostingUser.findOne({
        $or: [
          { user_id: userData.user_id },
          { username: userData.username }
        ]
      });

      if (existingUser) {
        logger.warn(`User already exists: ${userData.username}`, {
          existing_user_id: existingUser.user_id,
          existing_status: existingUser.status
        });
        throw new Error(`User with ID '${userData.user_id}' or username '${userData.username}' already exists`);
      }

      // Get optimal server for user allocation
      logger.info(`Getting optimal server for user: ${userData.username}`);
      const selectedServer = await this.getOptimalServer();
      logger.info(`Selected server for user ${userData.username}:`, {
        server_id: selectedServer.id,
        server_ip: selectedServer.ip_address,
        current_users: selectedServer.current_users,
        max_users: selectedServer.max_users
      });

      const linuxUsername = `user_${userData.username.toLowerCase().replace(/[^a-z0-9]/g, '')}`;
      const homeDirectory = `/var/www/${linuxUsername}`;
      const port = await this.getNextAvailablePort();
      const sshPort = await this.getNextAvailableSSHPort();
      const ftpPort = await this.getNextAvailableFTPPort();

      logger.info(`Generated user configuration for ${userData.username}:`, {
        linux_username: linuxUsername,
        home_directory: homeDirectory,
        port,
        ssh_port: sshPort,
        ftp_port: ftpPort
      });

      // Create database record first (before SSH operations)
      logger.info(`Creating database record for user: ${userData.username}`);
      const newUser = new SharedHostingUser({
        user_id: userData.user_id,
        username: userData.username,
        linux_username: linuxUsername,
        email: `${userData.username}@poolot.com`,
        home_directory: homeDirectory,
        plan: userData.plan,
        status: 'pending', // Start as pending until SSH setup completes
        server_id: selectedServer.id,
        server_ip: selectedServer.ip_address,
        port,
        ssh_port: sshPort,
        ftp_port: ftpPort,
        resource_limits: this.getPlanLimits(userData.plan),
        usage: {
          cpu_usage: 0,
          memory_usage: 0,
          bandwidth_used: 0,
          storage_used: 0
        },
        applications: []
      });

      // Save to database
      logger.info(`Saving user to database: ${userData.username}`);
      const savedUser = await newUser.save();
      logger.info(`User saved to database successfully: ${userData.username}`, {
        database_id: savedUser._id,
        user_id: savedUser.user_id,
        status: savedUser.status
      });

      try {
        logger.info(`Starting SSH operations for user: ${userData.username}`);
        const sshStartTime = Date.now();

        // Create Linux user with isolated home directory on selected server
        logger.info(`Creating Linux user: ${linuxUsername}`);
        await this.createLinuxUser(linuxUsername, homeDirectory, selectedServer);
        logger.info(`Linux user created successfully: ${linuxUsername}`);

        // Set up resource limits on selected server
        logger.info(`Setting up resource limits for: ${linuxUsername}`);
        await this.setupResourceLimits(linuxUsername, userData.plan, selectedServer);
        logger.info(`Resource limits configured for: ${linuxUsername}`);

        // Create user directory structure on selected server
        logger.info(`Creating directory structure for: ${linuxUsername}`);
        await this.createUserDirectoryStructure(homeDirectory, linuxUsername, selectedServer);
        logger.info(`Directory structure created for: ${linuxUsername}`);

        // Set up bandwidth throttling on selected server
        logger.info(`Setting up bandwidth throttling for: ${linuxUsername}`);
        await this.setupBandwidthThrottling(linuxUsername, port, selectedServer);
        logger.info(`Bandwidth throttling configured for: ${linuxUsername}`);

        const sshDuration = Date.now() - sshStartTime;
        logger.info(`All SSH operations completed for: ${userData.username}`, {
          duration_ms: sshDuration,
          linux_username: linuxUsername
        });

        // Update status to active after successful SSH setup
        logger.info(`Updating user status to active: ${userData.username}`);
        savedUser.status = 'active';
        await savedUser.save();

        // Update server metrics
        logger.info(`Updating server metrics for: ${selectedServer.id}`);
        await this.updateServerUserCount(selectedServer.id, 1);

        // Create payment status for new user
        logger.info(`Creating payment status for: ${userData.username}`);
        await this.createPaymentStatus(userData.user_id, userData.plan);

        const totalDuration = Date.now() - startTime;
        logger.info(`User creation completed successfully: ${userData.username}`, {
          total_duration_ms: totalDuration,
          ssh_duration_ms: sshDuration,
          linux_username: linuxUsername,
          home_directory: homeDirectory,
          server_id: selectedServer.id,
          final_status: 'active'
        });

        // Return formatted user object
        return this.formatUserResponse(savedUser);

      } catch (sshError) {
        // If SSH setup fails, mark user as failed and cleanup
        savedUser.status = 'deleted';
        await savedUser.save();

        logger.error(`SSH setup failed for user ${linuxUsername}, marked as deleted:`, sshError);
        throw new Error(`Failed to setup user environment: ${sshError instanceof Error ? sshError.message : 'Unknown error'}`);
      }

    } catch (error) {
      logger.error('Failed to create shared hosting user:', error);
      throw error;
    }
  }

  // Helper method to format user response
  private formatUserResponse(user: any): SharedHostingUser {
    return {
      id: user._id.toString(),
      user_id: user.user_id,
      username: user.username,
      linux_username: user.linux_username,
      home_directory: user.home_directory,
      plan: user.plan,
      status: this.mapStatus(user.status),
      server_id: user.server_id,
      server_ip: user.server_ip,
      port: user.port,
      ssh_port: user.ssh_port,
      ftp_port: user.ftp_port,
      resource_limits: user.resource_limits,
      usage: user.usage,
      created_at: user.created_at.toISOString(),
      applications: (user.applications || []).map((app: any) => ({
        id: app._id?.toString() || app.id,
        name: app.name,
        type: app.type,
        framework: app.framework,
        domain: app.domain,
        subdomain: app.subdomain,
        directory: app.directory,
        status: app.status,
        port: app.port,
        ssl_enabled: app.ssl_enabled,
        created_at: app.created_at?.toISOString(),
        last_deployed: app.last_deployed?.toISOString()
      }))
    };
  }

  // Helper method to map database status to service interface status
  private mapStatus(dbStatus: string): 'active' | 'suspended' | 'disabled' {
    switch (dbStatus) {
      case 'active': return 'active';
      case 'suspended': return 'suspended';
      case 'deleted':
      case 'pending':
      default: return 'disabled';
    }
  }

  // Update server user count in database
  private async updateServerUserCount(serverId: string, increment: number): Promise<void> {
    try {
      await ServerMetrics.findOneAndUpdate(
        { server_id: serverId },
        {
          $inc: { current_users: increment },
          $set: { last_updated: new Date() }
        },
        { upsert: true }
      );
    } catch (error) {
      logger.error(`Failed to update server user count for ${serverId}:`, error);
    }
  }

  // Create payment status for new user
  private async createPaymentStatus(userId: string, plan: string): Promise<void> {
    try {
      const paymentStatus = new PaymentStatus({
        user_id: userId,
        plan: plan,
        status: 'active', // Start as active, will be updated by payment monitoring
        last_payment: new Date(),
        next_payment: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        grace_period_end: null,
        deletion_scheduled: null
      });

      await paymentStatus.save();
      logger.info(`Created payment status for user: ${userId}`);
    } catch (error) {
      logger.error(`Failed to create payment status for user ${userId}:`, error);
    }
  }

  // Helper method to get start date for different periods
  private getStartDateForPeriod(period: string): Date {
    const now = new Date();
    switch (period) {
      case '24h':
        return new Date(now.getTime() - 24 * 60 * 60 * 1000);
      case '7d':
        return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      case '30d':
        return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      case '90d':
        return new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
      default:
        return new Date(now.getTime() - 24 * 60 * 60 * 1000); // Default to 24h
    }
  }

  // Production method to collect and store usage analytics
  async collectUsageAnalytics(): Promise<void> {
    try {
      logger.info('Starting usage analytics collection...');

      // Get all active users
      const activeUsers = await SharedHostingUser.find({
        status: { $in: ['active', 'suspended'] }
      }).lean();

      for (const user of activeUsers) {
        try {
          // Collect real-time usage data via SSH
          const usageData = await this.collectUserUsageData(user.user_id, user.server_id);

          // Store in database
          await this.storeUsageAnalytics(user.user_id, usageData);

        } catch (userError) {
          logger.error(`Failed to collect usage for user ${user.user_id}:`, userError);
        }
      }

      logger.info(`Completed usage analytics collection for ${activeUsers.length} users`);
    } catch (error) {
      logger.error('Failed to collect usage analytics:', error);
    }
  }

  // Collect real usage data from server via SSH
  private async collectUserUsageData(userId: string, serverId: string): Promise<any> {
    const ssh = new SSHConnection();
    try {
      await ssh.connect();

      const user = await SharedHostingUser.findOne({ user_id: userId });
      if (!user) {
        throw new Error('User not found');
      }

      // Get CPU usage for user processes
      const cpuUsage = await ssh.executeCommand(
        `ps -u ${user.linux_username} -o %cpu --no-headers | awk '{sum += $1} END {print sum}'`
      );

      // Get memory usage for user processes
      const memoryUsage = await ssh.executeCommand(
        `ps -u ${user.linux_username} -o %mem --no-headers | awk '{sum += $1} END {print sum}'`
      );

      // Get disk usage for user directory
      const diskUsage = await ssh.executeCommand(
        `du -s ${user.home_directory} | awk '{print $1}'`
      );

      // Get network usage (simplified - would need more sophisticated monitoring in production)
      const networkStats = await ssh.executeCommand(
        `cat /proc/net/dev | grep -E "(eth0|ens)" | awk '{print $2, $10}'`
      );

      return {
        cpu_usage_avg: parseFloat(cpuUsage.stdout.trim()) || 0,
        cpu_usage_max: parseFloat(cpuUsage.stdout.trim()) || 0, // Simplified
        memory_usage_avg: parseFloat(memoryUsage.stdout.trim()) || 0,
        memory_usage_max: parseFloat(memoryUsage.stdout.trim()) || 0, // Simplified
        storage_used: parseInt(diskUsage.stdout.trim()) || 0,
        bandwidth_used: this.parseNetworkStats(networkStats.stdout),
        requests_count: 0, // Would need web server log analysis
        uptime_percentage: 100, // Simplified
        error_rate: 0 // Would need application log analysis
      };

    } catch (error) {
      logger.error(`Failed to collect usage data for user ${userId}:`, error);
      return {
        cpu_usage_avg: 0,
        cpu_usage_max: 0,
        memory_usage_avg: 0,
        memory_usage_max: 0,
        storage_used: 0,
        bandwidth_used: 0,
        requests_count: 0,
        uptime_percentage: 100,
        error_rate: 0
      };
    } finally {
      await ssh.disconnect();
    }
  }

  // Store usage analytics in database
  private async storeUsageAnalytics(userId: string, usageData: any): Promise<void> {
    try {
      const today = new Date();
      today.setHours(0, 0, 0, 0); // Start of day

      // Update or create today's analytics record
      await UserUsageAnalytics.findOneAndUpdate(
        { user_id: userId, date: today },
        {
          user_id: userId,
          date: today,
          ...usageData
        },
        { upsert: true, new: true }
      );

      logger.debug(`Stored usage analytics for user: ${userId}`);
    } catch (error) {
      logger.error(`Failed to store usage analytics for user ${userId}:`, error);
    }
  }

  // Helper to parse network statistics
  private parseNetworkStats(networkStats: string): number {
    try {
      const lines = networkStats.trim().split('\n');
      let totalBytes = 0;

      for (const line of lines) {
        const parts = line.trim().split(/\s+/);
        if (parts.length >= 2 && parts[0] && parts[1]) {
          totalBytes += parseInt(parts[0]) + parseInt(parts[1]); // RX + TX bytes
        }
      }

      return Math.round(totalBytes / (1024 * 1024)); // Convert to MB
    } catch (error) {
      return 0;
    }
  }

  // Start analytics collection service (production-ready background service)
  startAnalyticsCollection(): void {
    logger.info('Starting analytics collection service...');

    // Collect analytics every 5 minutes
    const analyticsInterval = setInterval(async () => {
      try {
        await this.collectUsageAnalytics();
      } catch (error) {
        logger.error('Analytics collection error:', error);
      }
    }, 5 * 60 * 1000); // 5 minutes

    // Store interval reference for cleanup
    (this as any).analyticsInterval = analyticsInterval;

    // Cleanup old analytics data daily
    const cleanupInterval = setInterval(async () => {
      try {
        await this.cleanupOldAnalytics();
      } catch (error) {
        logger.error('Analytics cleanup error:', error);
      }
    }, 24 * 60 * 60 * 1000); // 24 hours

    // Store interval reference for cleanup
    (this as any).cleanupInterval = cleanupInterval;

    logger.info('Analytics collection service started');
  }

  // Stop analytics collection service
  stopAnalyticsCollection(): void {
    if ((this as any).analyticsInterval) {
      clearInterval((this as any).analyticsInterval);
      delete (this as any).analyticsInterval;
    }

    if ((this as any).cleanupInterval) {
      clearInterval((this as any).cleanupInterval);
      delete (this as any).cleanupInterval;
    }

    logger.info('Analytics collection service stopped');
  }

  // Cleanup old analytics data (keep last 90 days)
  private async cleanupOldAnalytics(): Promise<void> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - 90); // 90 days ago

      const result = await UserUsageAnalytics.deleteMany({
        date: { $lt: cutoffDate }
      });

      logger.info(`Cleaned up ${result.deletedCount} old analytics records`);
    } catch (error) {
      logger.error('Failed to cleanup old analytics:', error);
    }
  }

  // Graceful shutdown of all services
  shutdown(): void {
    logger.info('Shutting down shared hosting service...');

    // Stop analytics collection
    this.stopAnalyticsCollection();

    // Stop payment monitoring
    this.stopPaymentLifecycleMonitoring();

    // Stop server metrics collection
    if ((this as any).metricsInterval) {
      clearInterval((this as any).metricsInterval);
      delete (this as any).metricsInterval;
    }

    logger.info('Shared hosting service shutdown complete');
  }

  // Test SSH connection (for debugging)
  async testSSHConnection(): Promise<any> {
    const ssh = new SSHConnection();
    const testResults: any = {
      connection_test: 'failed',
      configuration: {},
      commands_test: [],
      timestamp: new Date().toISOString()
    };

    try {
      logger.info('Starting SSH connection test...');

      // Log SSH configuration (without sensitive data)
      testResults.configuration = {
        host: ssh['config'].host,
        port: ssh['config'].port,
        username: ssh['config'].username,
        has_password: !!ssh['config'].password,
        has_private_key: !!ssh['config'].privateKey
      };

      logger.info('SSH Configuration:', testResults.configuration);

      // Test connection
      logger.info('Testing SSH connection...');
      await ssh.connect();
      testResults.connection_test = 'success';
      logger.info('SSH connection successful!');

      // Test basic commands
      const testCommands = [
        'whoami',
        'pwd',
        'ls -la /',
        'df -h',
        'free -m',
        'uname -a'
      ];

      for (const command of testCommands) {
        try {
          logger.info(`Testing command: ${command}`);
          const result = await ssh.executeCommand(command);
          testResults.commands_test.push({
            command,
            status: 'success',
            stdout: result.stdout.trim(),
            stderr: result.stderr.trim()
          });
          logger.info(`Command '${command}' executed successfully`);
        } catch (cmdError) {
          const errorMessage = cmdError instanceof Error ? cmdError.message : 'Unknown error';
          testResults.commands_test.push({
            command,
            status: 'failed',
            error: errorMessage
          });
          logger.error(`Command '${command}' failed:`, errorMessage);
        }
      }

      testResults.overall_status = 'success';
      logger.info('SSH connection test completed successfully');

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : undefined;

      testResults.connection_error = errorMessage;
      testResults.overall_status = 'failed';

      logger.error('SSH connection test failed:', {
        error: errorMessage,
        stack: errorStack,
        config: testResults.configuration
      });
    } finally {
      try {
        await ssh.disconnect();
        logger.info('SSH connection closed');
      } catch (disconnectError) {
        logger.warn('Error closing SSH connection:', disconnectError);
      }
    }

    return testResults;
  }

  // Create Linux user with proper isolation using SSH
  private async createLinuxUser(username: string, homeDirectory: string, server?: SharedHostingServer): Promise<void> {
    const ssh = new SSHConnection();
    try {
      logger.info(`Starting Linux user creation for: ${username}`, {
        home_directory: homeDirectory,
        server_ip: server?.ip_address || 'default'
      });

      // Override SSH config for specific server if provided
      if (server) {
        logger.info(`Using specific server for user creation: ${server.ip_address}`);
        (ssh as any).config.host = server.ip_address;
      }

      logger.info(`Connecting to SSH for user creation: ${username}`);
      await ssh.connect();
      logger.info(`SSH connected successfully for user creation: ${username}`);

      // Check if user already exists
      logger.info(`Checking if Linux user already exists: ${username}`);
      try {
        const userCheck = await ssh.executeCommand(`id ${username}`);
        if (userCheck.stdout.trim()) {
          logger.warn(`Linux user already exists: ${username}`, { output: userCheck.stdout });
          return; // User already exists, skip creation
        }
      } catch (checkError) {
        logger.info(`User does not exist, proceeding with creation: ${username}`);
      }

      // Create user with custom home directory
      logger.info(`Creating Linux user: ${username} with home: ${homeDirectory}`);
      const createUserResult = await ssh.executeCommand(`adduser --disabled-password --gecos "" --home ${homeDirectory} ${username}`);
      logger.info(`User creation command completed: ${username}`, {
        stdout: createUserResult.stdout,
        stderr: createUserResult.stderr
      });

      // Set strict permissions (only user can access their directory)
      logger.info(`Setting directory permissions for: ${username}`);
      const chmodResult = await ssh.executeCommand(`chmod 700 ${homeDirectory}`);
      logger.debug(`chmod result for ${username}:`, { stdout: chmodResult.stdout, stderr: chmodResult.stderr });

      const chownResult = await ssh.executeCommand(`chown ${username}:${username} ${homeDirectory}`);
      logger.debug(`chown result for ${username}:`, { stdout: chownResult.stdout, stderr: chownResult.stderr });

      // Disable cron access to prevent background processes
      logger.info(`Disabling cron access for: ${username}`);
      const cronResult = await ssh.executeCommand(`echo "${username}" >> /etc/cron.deny`);
      logger.debug(`cron deny result for ${username}:`, { stdout: cronResult.stdout, stderr: cronResult.stderr });

      logger.info(`Successfully created Linux user: ${username}`, {
        home_directory: homeDirectory,
        permissions_set: true,
        cron_disabled: true
      });

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : undefined;

      logger.error(`Failed to create Linux user ${username}:`, {
        error: errorMessage,
        stack: errorStack,
        home_directory: homeDirectory,
        server_ip: server?.ip_address || 'default'
      });
      throw error;
    } finally {
      logger.info(`Disconnecting SSH for user creation: ${username}`);
      await ssh.disconnect();
    }
  }

  // Set up systemd resource limits for CPU and memory using SSH
  // Updated for shared hosting: No artificial resource limits applied
  private async setupResourceLimits(username: string, plan: string, server?: SharedHostingServer): Promise<void> {
    // For shared hosting, we don't apply artificial resource limits
    // Users get full server access and resources are managed by the OS
    logger.info(`Skipping resource limits setup for shared hosting user: ${username}, plan: ${plan} - Full server access enabled`);

    // Note: In a production environment, you might want to set up basic security limits
    // like process limits or file descriptor limits, but not CPU/memory quotas
    // This allows for true shared hosting where users can utilize available server resources
  }

  // Create user directory structure for web hosting using SSH
  private async createUserDirectoryStructure(homeDirectory: string, username: string, server?: SharedHostingServer): Promise<void> {
    const ssh = new SSHConnection();
    try {
      // Override SSH config for specific server if provided
      if (server) {
        (ssh as any).config.host = server.ip_address;
      }
      await ssh.connect();
      const directories = [
        'public_html',      // Web root
        'logs',            // Application logs
        'tmp',             // Temporary files
        'backups',         // User backups
        'ssl',             // SSL certificates
        'apps',            // Application directories
        'apps/static',     // Static websites
        'apps/web-service', // Web services
        'apps/nodejs',     // Node.js applications
        'apps/php'         // PHP applications
      ];

      for (const dir of directories) {
        const fullPath = `${homeDirectory}/${dir}`;
        await ssh.executeCommand(`mkdir -p ${fullPath}`);
        await ssh.executeCommand(`chown ${username}:${username} ${fullPath}`);
        await ssh.executeCommand(`chmod 755 ${fullPath}`);
      }

      // Create default index.html
      const defaultHtml = `<!DOCTYPE html>
<html>
<head>
    <title>Welcome to ${username}'s Website</title>
    <style>
        body { font-family: Arial, sans-serif; text-align: center; margin-top: 50px; }
        .container { max-width: 600px; margin: 0 auto; }
        .logo { color: #007bff; font-size: 2em; margin-bottom: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🚀 Achidas Hosting</div>
        <h1>Welcome to your new website!</h1>
        <p>Your shared hosting account is ready. Upload your files to the <code>public_html</code> directory.</p>
        <p>Server: ${appConfig.ssh.host}</p>
        <p>Username: ${username}</p>
    </div>
</body>
</html>`;

      // Write the HTML file using SSH
      const escapedHtml = defaultHtml.replace(/'/g, "'\"'\"'");
      await ssh.executeCommand(`echo '${escapedHtml}' > ${homeDirectory}/public_html/index.html`);
      await ssh.executeCommand(`chown ${username}:${username} ${homeDirectory}/public_html/index.html`);

      logger.info(`Created directory structure for user: ${username}`);
    } catch (error) {
      logger.error(`Failed to create directory structure for ${username}:`, error);
      throw error;
    } finally {
      await ssh.disconnect();
    }
  }

  // Set up bandwidth throttling using tc (traffic control)
  // Updated for shared hosting: No bandwidth throttling applied
  private async setupBandwidthThrottling(username: string, port: number, server?: SharedHostingServer): Promise<void> {
    // For shared hosting, we don't apply bandwidth throttling
    // Users get full server bandwidth access
    logger.info(`Skipping bandwidth throttling for shared hosting user: ${username} on port: ${port} - Full bandwidth access enabled`);

    // Note: In a production environment, you might want to implement fair queuing
    // or other traffic management at the network level, but not per-user throttling
    // This allows for true shared hosting where users can utilize available bandwidth
  }

  // Get plan resource limits - Updated for single shared plan with full server access
  private getPlanLimits(plan: string): SharedHostingUser['resource_limits'] {
    // For shared hosting, users get full server access without artificial limits
    // Resource management is handled by the OS and server capacity
    const sharedPlanLimits: SharedHostingUser['resource_limits'] = {
      cpu_quota: 100, // Full CPU access (100% of available)
      memory_max: 0,  // No memory limit (use server capacity)
      bandwidth_limit: 0, // No bandwidth limit (use server capacity)
      storage_limit: 0    // No storage limit (use server capacity)
    };

    // All shared hosting users get the same full access
    return sharedPlanLimits;
  }

  // Get next available port for user applications
  private async getNextAvailablePort(): Promise<number> {
    // In production, this should check a database or port registry
    return this.BASE_PORT + Math.floor(Math.random() * 1000);
  }

  private async getNextAvailableSSHPort(): Promise<number> {
    return this.SSH_BASE_PORT + Math.floor(Math.random() * 100);
  }

  private async getNextAvailableFTPPort(): Promise<number> {
    return this.FTP_BASE_PORT + Math.floor(Math.random() * 100);
  }

  // Helper method to generate subdomain from application name
  private generateSubdomain(applicationName: string): string {
    return applicationName
      .toLowerCase()
      .replace(/[^a-z0-9-]/g, '-') // Replace non-alphanumeric with hyphens
      .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
      .replace(/^-|-$/g, ''); // Remove leading/trailing hyphens
  }

  // Helper method to generate unique subdomain with dynamic identifier
  private async generateUniqueSubdomain(applicationName: string, userId: string): Promise<string> {
    const baseSubdomain = this.generateSubdomain(applicationName);

    // First try the base subdomain
    if (await this.isSubdomainUnique(baseSubdomain)) {
      return baseSubdomain;
    }

    // If not unique, add user identifier (first 6 chars of user ID)
    const userIdentifier = userId.substring(0, 6).toLowerCase();
    const subdomainWithUser = `${baseSubdomain}-${userIdentifier}`;

    if (await this.isSubdomainUnique(subdomainWithUser)) {
      return subdomainWithUser;
    }

    // If still not unique, add timestamp
    const timestamp = Date.now().toString().slice(-6); // Last 6 digits of timestamp
    const subdomainWithTimestamp = `${baseSubdomain}-${userIdentifier}-${timestamp}`;

    if (await this.isSubdomainUnique(subdomainWithTimestamp)) {
      return subdomainWithTimestamp;
    }

    // If still not unique, add random string
    const randomString = Math.random().toString(36).substring(2, 8);
    return `${baseSubdomain}-${userIdentifier}-${randomString}`;
  }

  // Helper method to check subdomain uniqueness across all shared hosting applications
  private async isSubdomainUnique(subdomain: string): Promise<boolean> {
    try {
      // Check in shared hosting applications collection
      const existingApp = await SharedHostingApplication.findOne({ subdomain: `${subdomain}.${appConfig.domain.primary}` });

      // Also check in main applications collection for global uniqueness
      const mainAppService = getApplicationService();
      const isUniqueInMain = await mainAppService['isSubdomainUnique'](subdomain);

      return !existingApp && isUniqueInMain;
    } catch (error) {
      logger.error('Error checking subdomain uniqueness:', error);
      return false;
    }
  }

  // Create application for shared user
  async createApplication(userId: string, appData: CreateApplicationRequest): Promise<SharedHostingApplication> {
    try {
      // Generate unique subdomain with dynamic identifier if needed
      const subdomain = await this.generateUniqueSubdomain(appData.name, userId);

      const appId = `app_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      const fullDomain = `${subdomain}.${appConfig.domain.primary}`;
      const directory = `/var/www/user_${userId}/apps/${appData.type}/${appData.name}`;

      // Create application directory
      await execAsync(`sudo mkdir -p ${directory}`);
      await execAsync(`sudo chown user_${userId}:user_${userId} ${directory}`);

      // Set up application based on type
      await this.setupApplicationEnvironment(directory, appData.type, appData);

      const application: SharedHostingApplication = {
        id: appId,
        name: appData.name,
        type: appData.type,
        ...(appData.framework && { framework: appData.framework }),
        ...(appData.domain && { domain: appData.domain }),
        subdomain: fullDomain,
        directory,
        status: 'stopped',
        ssl_enabled: false,
        created_at: new Date().toISOString()
      };

      // Create DNS record for the application
      try {
        const cloudflareDNS = new CloudflareDNSService();
        await cloudflareDNS.createDNSRecord({
          type: 'A',
          name: fullDomain,
          content: '*************', // Default shared hosting server IP
          ttl: 300,
          proxied: false
        });

        logger.info(`Created DNS record for ${fullDomain} -> *************`);
      } catch (dnsError) {
        logger.error(`Failed to create DNS record for ${fullDomain}:`, dnsError);
        // Don't fail the application creation if DNS fails, just log the error
      }

      logger.info(`Created application: ${appData.name} for user: ${userId} with subdomain: ${fullDomain}`);
      return application;
    } catch (error) {
      logger.error('Failed to create application:', error);
      throw new Error('Failed to create application');
    }
  }

  // Set up application environment based on type and framework
  private async setupApplicationEnvironment(directory: string, type: string, appData: CreateApplicationRequest): Promise<void> {
    try {
      if (type === 'static-website') {
        await this.setupStaticWebsite(directory, appData.framework);
      } else if (type === 'web-service') {
        await this.setupWebService(directory, appData);
      }
    } catch (error) {
      logger.error(`Failed to setup ${type} environment:`, error);
      throw error;
    }
  }

  // Set up static website environment
  private async setupStaticWebsite(directory: string, framework?: string): Promise<void> {
    const frameworkInfo = this.getFrameworkInfo(framework);

    const indexHtml = `
<!DOCTYPE html>
<html>
<head>
    <title>${frameworkInfo.title}</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f8f9fa; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .logo { color: #007bff; font-size: 2.5em; margin-bottom: 10px; }
        .framework-badge { background: ${frameworkInfo.color}; color: white; padding: 5px 15px; border-radius: 20px; font-size: 0.9em; }
        .instructions { background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">${frameworkInfo.icon}</div>
            <h1>${frameworkInfo.title}</h1>
            <span class="framework-badge">${framework || 'HTML'}</span>
            <p>${frameworkInfo.description}</p>
        </div>
        <div class="instructions">
            <h2>Getting Started</h2>
            ${frameworkInfo.instructions}
        </div>
        <div class="content">
            <h3>File Structure</h3>
            <p>Your files should be organized as follows:</p>
            <ul>
                ${frameworkInfo.structure.map((item: any) => `<li><strong>${item.name}</strong> - ${item.description}</li>`).join('')}
            </ul>
        </div>
    </div>
</body>
</html>`;

    await fs.writeFile(path.join(directory, 'index.html'), indexHtml);

    // Create framework-specific directories
    const directories = ['css', 'js', 'images', 'assets'];
    if (framework === 'react' || framework === 'vue' || framework === 'angular') {
      directories.push('dist', 'build', 'public');
    }

    await execAsync(`sudo mkdir -p ${directory}/{${directories.join(',')}}`);

    // Create framework-specific files
    if (framework === 'react') {
      await this.createReactFiles(directory);
    } else if (framework === 'vue') {
      await this.createVueFiles(directory);
    } else if (framework === 'angular') {
      await this.createAngularFiles(directory);
    }
  }

  // Get framework-specific information
  private getFrameworkInfo(framework?: string) {
    const frameworks: Record<string, any> = {
      html: {
        title: 'Static HTML Website',
        icon: '🌐',
        color: '#e34c26',
        description: 'Upload your HTML, CSS, and JavaScript files here.',
        instructions: `
          <ol>
            <li>Replace this index.html with your own</li>
            <li>Upload your CSS files to the 'css' folder</li>
            <li>Upload your JavaScript files to the 'js' folder</li>
            <li>Upload images to the 'images' folder</li>
          </ol>`,
        structure: [
          { name: 'index.html', description: 'Main HTML file' },
          { name: 'css/', description: 'Stylesheets' },
          { name: 'js/', description: 'JavaScript files' },
          { name: 'images/', description: 'Images and media' }
        ]
      },
      react: {
        title: 'React Application',
        icon: '⚛️',
        color: '#61dafb',
        description: 'Upload your React build files (npm run build output).',
        instructions: `
          <ol>
            <li>Build your React app locally: <code>npm run build</code></li>
            <li>Upload the contents of the 'build' or 'dist' folder</li>
            <li>Ensure index.html is in the root directory</li>
            <li>Static assets should be in the 'static' folder</li>
          </ol>`,
        structure: [
          { name: 'index.html', description: 'Main HTML file' },
          { name: 'static/', description: 'Built CSS, JS, and media files' },
          { name: 'build/', description: 'React build output' },
          { name: 'public/', description: 'Public assets' }
        ]
      },
      vue: {
        title: 'Vue.js Application',
        icon: '🖖',
        color: '#4fc08d',
        description: 'Upload your Vue.js build files (npm run build output).',
        instructions: `
          <ol>
            <li>Build your Vue app locally: <code>npm run build</code></li>
            <li>Upload the contents of the 'dist' folder</li>
            <li>Ensure index.html is in the root directory</li>
            <li>Assets should be in the appropriate folders</li>
          </ol>`,
        structure: [
          { name: 'index.html', description: 'Main HTML file' },
          { name: 'dist/', description: 'Vue build output' },
          { name: 'assets/', description: 'Built assets' },
          { name: 'css/', description: 'Stylesheets' }
        ]
      },
      angular: {
        title: 'Angular Application',
        icon: '🅰️',
        color: '#dd0031',
        description: 'Upload your Angular build files (ng build output).',
        instructions: `
          <ol>
            <li>Build your Angular app: <code>ng build --prod</code></li>
            <li>Upload the contents of the 'dist' folder</li>
            <li>Ensure index.html is in the root directory</li>
            <li>Configure routing for SPA if needed</li>
          </ol>`,
        structure: [
          { name: 'index.html', description: 'Main HTML file' },
          { name: 'dist/', description: 'Angular build output' },
          { name: 'assets/', description: 'Static assets' },
          { name: 'styles/', description: 'Global styles' }
        ]
      }
    };

    return frameworks[framework || 'html'] || frameworks['html'];
  }

  // Create React-specific files
  private async createReactFiles(directory: string): Promise<void> {
    const packageJson = {
      name: "react-static-site",
      version: "1.0.0",
      description: "React static site on Achidas hosting",
      scripts: {
        build: "react-scripts build",
        start: "serve -s build"
      }
    };

    await fs.writeFile(path.join(directory, 'package.json'), JSON.stringify(packageJson, null, 2));
  }

  // Create Vue-specific files
  private async createVueFiles(directory: string): Promise<void> {
    const packageJson = {
      name: "vue-static-site",
      version: "1.0.0",
      description: "Vue.js static site on Achidas hosting",
      scripts: {
        build: "vue-cli-service build",
        start: "serve -s dist"
      }
    };

    await fs.writeFile(path.join(directory, 'package.json'), JSON.stringify(packageJson, null, 2));
  }

  // Create Angular-specific files
  private async createAngularFiles(directory: string): Promise<void> {
    const packageJson = {
      name: "angular-static-site",
      version: "1.0.0",
      description: "Angular static site on Achidas hosting",
      scripts: {
        build: "ng build --prod",
        start: "serve -s dist"
      }
    };

    await fs.writeFile(path.join(directory, 'package.json'), JSON.stringify(packageJson, null, 2));
  }

  // Set up web service environment based on framework
  private async setupWebService(directory: string, appData: CreateApplicationRequest): Promise<void> {
    const framework = appData.framework || 'nodejs';

    // Install runtime/packages for the framework
    await this.installFrameworkRuntime(framework);

    switch (framework) {
      case 'nodejs':
        await this.setupNodeJSService(directory, appData);
        break;
      case 'python':
        await this.setupPythonService(directory, appData);
        break;
      case 'rust':
        await this.setupRustService(directory, appData);
        break;
      case 'php':
        await this.setupPHPService(directory, appData);
        break;
      case 'go':
        await this.setupGoService(directory, appData);
        break;
      case 'java':
        await this.setupJavaService(directory, appData);
        break;
      case 'dotnet':
        await this.setupDotNetService(directory, appData);
        break;
      case 'ruby':
        await this.setupRubyService(directory, appData);
        break;
      default:
        await this.setupNodeJSService(directory, appData);
    }
  }

  // Install runtime and packages for programming languages dynamically
  private async installFrameworkRuntime(framework: string): Promise<void> {
    try {
      logger.info(`Installing runtime for framework: ${framework}`);

      switch (framework) {
        case 'nodejs':
          await this.installNodeJS();
          break;
        case 'python':
          await this.installPython();
          break;
        case 'rust':
          await this.installRust();
          break;
        case 'php':
          await this.installPHP();
          break;
        case 'go':
          await this.installGo();
          break;
        case 'java':
          await this.installJava();
          break;
        case 'dotnet':
          await this.installDotNet();
          break;
        case 'ruby':
          await this.installRuby();
          break;
        default:
          logger.warn(`Unknown framework: ${framework}, skipping runtime installation`);
      }
    } catch (error) {
      logger.error(`Failed to install runtime for ${framework}:`, error);
      throw error;
    }
  }

  // Install Node.js runtime
  private async installNodeJS(): Promise<void> {
    try {
      // Check if Node.js is already installed
      const nodeCheck = await execAsync('node --version').catch(() => null);
      if (nodeCheck) {
        logger.info('Node.js already installed:', nodeCheck.stdout.trim());
        return;
      }

      logger.info('Installing Node.js...');
      await execAsync('curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -');
      await execAsync('sudo apt-get install -y nodejs');

      // Install global packages
      await execAsync('sudo npm install -g pm2 serve nodemon');

      logger.info('Node.js installation completed');
    } catch (error) {
      logger.error('Failed to install Node.js:', error);
      throw error;
    }
  }

  // Install Python runtime
  private async installPython(): Promise<void> {
    try {
      // Check if Python 3 is already installed
      const pythonCheck = await execAsync('python3 --version').catch(() => null);
      if (pythonCheck) {
        logger.info('Python already installed:', pythonCheck.stdout.trim());
        return;
      }

      logger.info('Installing Python...');
      await execAsync('sudo apt-get update');
      await execAsync('sudo apt-get install -y python3 python3-pip python3-venv python3-dev');

      // Install common packages
      await execAsync('sudo pip3 install fastapi uvicorn gunicorn flask django');

      logger.info('Python installation completed');
    } catch (error) {
      logger.error('Failed to install Python:', error);
      throw error;
    }
  }

  // Install Rust runtime
  private async installRust(): Promise<void> {
    try {
      // Check if Rust is already installed
      const rustCheck = await execAsync('rustc --version').catch(() => null);
      if (rustCheck) {
        logger.info('Rust already installed:', rustCheck.stdout.trim());
        return;
      }

      logger.info('Installing Rust...');
      await execAsync('curl --proto "=https" --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y');
      await execAsync('source ~/.cargo/env');

      // Install common tools
      await execAsync('~/.cargo/bin/cargo install cargo-watch');

      logger.info('Rust installation completed');
    } catch (error) {
      logger.error('Failed to install Rust:', error);
      throw error;
    }
  }

  // Install PHP runtime
  private async installPHP(): Promise<void> {
    try {
      // Check if PHP is already installed
      const phpCheck = await execAsync('php --version').catch(() => null);
      if (phpCheck) {
        logger.info('PHP already installed:', phpCheck.stdout.trim());
        return;
      }

      logger.info('Installing PHP...');
      await execAsync('sudo apt-get update');
      await execAsync('sudo apt-get install -y php php-cli php-fpm php-json php-common php-mysql php-zip php-gd php-mbstring php-curl php-xml php-pear php-bcmath');

      // Install Composer
      await execAsync('curl -sS https://getcomposer.org/installer | php');
      await execAsync('sudo mv composer.phar /usr/local/bin/composer');

      logger.info('PHP installation completed');
    } catch (error) {
      logger.error('Failed to install PHP:', error);
      throw error;
    }
  }

  // Install Go runtime
  private async installGo(): Promise<void> {
    try {
      // Check if Go is already installed
      const goCheck = await execAsync('go version').catch(() => null);
      if (goCheck) {
        logger.info('Go already installed:', goCheck.stdout.trim());
        return;
      }

      logger.info('Installing Go...');
      await execAsync('wget https://go.dev/dl/go1.21.5.linux-amd64.tar.gz');
      await execAsync('sudo rm -rf /usr/local/go && sudo tar -C /usr/local -xzf go1.21.5.linux-amd64.tar.gz');
      await execAsync('echo "export PATH=$PATH:/usr/local/go/bin" | sudo tee -a /etc/profile');
      await execAsync('rm go1.21.5.linux-amd64.tar.gz');

      logger.info('Go installation completed');
    } catch (error) {
      logger.error('Failed to install Go:', error);
      throw error;
    }
  }

  // Install Java runtime
  private async installJava(): Promise<void> {
    try {
      // Check if Java is already installed
      const javaCheck = await execAsync('java --version').catch(() => null);
      if (javaCheck) {
        logger.info('Java already installed:', javaCheck.stdout.trim());
        return;
      }

      logger.info('Installing Java...');
      await execAsync('sudo apt-get update');
      await execAsync('sudo apt-get install -y openjdk-17-jdk maven gradle');

      logger.info('Java installation completed');
    } catch (error) {
      logger.error('Failed to install Java:', error);
      throw error;
    }
  }

  // Install .NET runtime
  private async installDotNet(): Promise<void> {
    try {
      // Check if .NET is already installed
      const dotnetCheck = await execAsync('dotnet --version').catch(() => null);
      if (dotnetCheck) {
        logger.info('.NET already installed:', dotnetCheck.stdout.trim());
        return;
      }

      logger.info('Installing .NET...');
      await execAsync('wget https://packages.microsoft.com/config/ubuntu/22.04/packages-microsoft-prod.deb -O packages-microsoft-prod.deb');
      await execAsync('sudo dpkg -i packages-microsoft-prod.deb');
      await execAsync('rm packages-microsoft-prod.deb');
      await execAsync('sudo apt-get update');
      await execAsync('sudo apt-get install -y dotnet-sdk-8.0');

      logger.info('.NET installation completed');
    } catch (error) {
      logger.error('Failed to install .NET:', error);
      throw error;
    }
  }

  // Install Ruby runtime
  private async installRuby(): Promise<void> {
    try {
      // Check if Ruby is already installed
      const rubyCheck = await execAsync('ruby --version').catch(() => null);
      if (rubyCheck) {
        logger.info('Ruby already installed:', rubyCheck.stdout.trim());
        return;
      }

      logger.info('Installing Ruby...');
      await execAsync('sudo apt-get update');
      await execAsync('sudo apt-get install -y ruby-full build-essential zlib1g-dev');

      // Install bundler and rails
      await execAsync('sudo gem install bundler rails');

      logger.info('Ruby installation completed');
    } catch (error) {
      logger.error('Failed to install Ruby:', error);
      throw error;
    }
  }

  // Set up Node.js web service
  private async setupNodeJSService(directory: string, appData: CreateApplicationRequest): Promise<void> {
    const packageJsonPath = path.join(directory, 'package.json');
    const hasUserPackageJson = require('fs').existsSync(packageJsonPath);

    // Only create package.json if user hasn't provided one
    if (!hasUserPackageJson) {
      const packageJson = {
        name: appData.name,
        version: "1.0.0",
        description: "Node.js web service on Achidas shared hosting",
        main: "index.js",
        scripts: {
          start: appData.start_command || "node index.js",
          build: appData.build_command || "npm install"
        }
      };

      await fs.writeFile(packageJsonPath, JSON.stringify(packageJson, null, 2));

      // Create basic index.js only if user hasn't provided their own
      const indexPath = path.join(directory, 'index.js');
      const hasUserIndex = require('fs').existsSync(indexPath);

      if (!hasUserIndex) {
        const indexJs = `
const express = require('express');
const app = express();
const port = process.env.PORT || 3000;

app.use(express.json());

app.get('/', (req, res) => {
  res.json({ message: 'Hello from ${appData.name}!' });
});

app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: '${appData.name}'
  });
});

app.listen(port, () => {
  console.log(\`🚀 ${appData.name} running on port \${port}\`);
});
`;
        await fs.writeFile(indexPath, indexJs);
      }
    }

    // Install dependencies from user's package.json (or the one we created)
    try {
      await execAsync(`cd ${directory} && npm install`);
      logger.info(`Installed Node.js dependencies from package.json for ${appData.name}`);
    } catch (error) {
      logger.warn(`Failed to install Node.js dependencies for ${appData.name}:`, error);
    }

    // Create .gitignore if it doesn't exist
    const gitignorePath = path.join(directory, '.gitignore');
    const hasGitignore = require('fs').existsSync(gitignorePath);
    if (!hasGitignore) {
      await fs.writeFile(gitignorePath, 'node_modules/\n.env\n*.log\n');
    }
  }

  // Set up Python web service
  private async setupPythonService(directory: string, appData: CreateApplicationRequest): Promise<void> {
    const requirementsPath = path.join(directory, 'requirements.txt');
    const hasUserRequirements = require('fs').existsSync(requirementsPath);

    // Only create requirements.txt if user hasn't provided one
    if (!hasUserRequirements) {
      const requirementsTxt = `fastapi==0.104.1
uvicorn==0.24.0
pydantic==2.5.0`;

      await fs.writeFile(requirementsPath, requirementsTxt);

      // Create basic main.py only if user hasn't provided their own
      const mainPath = path.join(directory, 'main.py');
      const hasUserMain = require('fs').existsSync(mainPath);

      if (!hasUserMain) {
        const mainPy = `
from fastapi import FastAPI
import uvicorn
from datetime import datetime

app = FastAPI(title="${appData.name}", version="1.0.0")

@app.get("/")
async def root():
    return {"message": "Hello from ${appData.name}!"}

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "service": "${appData.name}"
    }

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
`;
        await fs.writeFile(mainPath, mainPy.trim());
      }
    }

    // Install dependencies from user's requirements.txt (or the one we created)
    try {
      await execAsync(`cd ${directory} && python3 -m pip install -r requirements.txt`);
      logger.info(`Installed Python dependencies from requirements.txt for ${appData.name}`);
    } catch (error) {
      logger.warn(`Failed to install Python dependencies for ${appData.name}:`, error);
    }

    // Create start script
    const startSh = `#!/bin/bash
${appData.start_command || 'uvicorn main:app --host 0.0.0.0 --port 8000'}
`;
    await fs.writeFile(path.join(directory, 'start.sh'), startSh.trim());
    await execAsync(`chmod +x ${path.join(directory, 'start.sh')}`);

    // Create .gitignore if it doesn't exist
    const gitignorePath = path.join(directory, '.gitignore');
    const hasGitignore = require('fs').existsSync(gitignorePath);
    if (!hasGitignore) {
      await fs.writeFile(gitignorePath, '__pycache__/\n*.pyc\n.env\n*.log\nvenv/\n');
    }
  }

  // Set up Rust web service
  private async setupRustService(directory: string, appData: CreateApplicationRequest): Promise<void> {
    const cargoTomlPath = path.join(directory, 'Cargo.toml');
    const hasUserCargo = require('fs').existsSync(cargoTomlPath);

    // Only create Cargo.toml if user hasn't provided one
    if (!hasUserCargo) {
      const cargoToml = `[package]
name = "${appData.name.replace(/[^a-z0-9_]/g, '_')}"
version = "0.1.0"
edition = "2021"

[dependencies]
actix-web = "4.4"
serde = { version = "1.0", features = ["derive"] }
tokio = { version = "1.0", features = ["full"] }`;

      await fs.writeFile(cargoTomlPath, cargoToml);

      // Create src directory and main.rs only if user hasn't provided their own
      await execAsync(`mkdir -p ${directory}/src`);
      const mainRsPath = path.join(directory, 'src', 'main.rs');
      const hasUserMain = require('fs').existsSync(mainRsPath);

      if (!hasUserMain) {
        const mainRs = `
use actix_web::{web, App, HttpResponse, HttpServer, Result};
use serde::Serialize;

#[derive(Serialize)]
struct HealthResponse {
    status: String,
    service: String,
}

async fn health() -> Result<HttpResponse> {
    let response = HealthResponse {
        status: "healthy".to_string(),
        service: "${appData.name}".to_string(),
    };
    Ok(HttpResponse::Ok().json(response))
}

async fn hello() -> Result<HttpResponse> {
    Ok(HttpResponse::Ok().json(serde_json::json!({
        "message": format!("Hello from {}!", "${appData.name}")
    })))
}

#[actix_web::main]
async fn main() -> std::io::Result<()> {
    println!("🚀 ${appData.name} starting on port 8080");

    HttpServer::new(|| {
        App::new()
            .route("/", web::get().to(hello))
            .route("/health", web::get().to(health))
    })
    .bind("0.0.0.0:8080")?
    .run()
    .await
}
`;
        await fs.writeFile(mainRsPath, mainRs.trim());
      }
    }

    // Build the Rust project using user's Cargo.toml (or the one we created)
    try {
      await execAsync(`cd ${directory} && cargo build --release`);
      logger.info(`Built Rust project from Cargo.toml for ${appData.name}`);
    } catch (error) {
      logger.warn(`Failed to build Rust project for ${appData.name}:`, error);
    }

    // Create .gitignore if it doesn't exist
    const gitignorePath = path.join(directory, '.gitignore');
    const hasGitignore = require('fs').existsSync(gitignorePath);
    if (!hasGitignore) {
      await fs.writeFile(gitignorePath, '/target\nCargo.lock\n.env\n*.log\n');
    }
  }

  // Set up PHP web service
  private async setupPHPService(directory: string, appData: CreateApplicationRequest): Promise<void> {
    const composerJsonPath = path.join(directory, 'composer.json');
    const hasUserComposer = require('fs').existsSync(composerJsonPath);

    // Only create composer.json if user hasn't provided one
    if (!hasUserComposer) {
      const composerJson = {
        name: `achidas/${appData.name}`,
        description: `${appData.name} PHP web service`,
        type: "project",
        require: {
          "php": ">=8.0"
        },
        autoload: {
          "psr-4": {
            "App\\": "src/"
          }
        }
      };

      await fs.writeFile(composerJsonPath, JSON.stringify(composerJson, null, 2));

      // Create basic index.php only if user hasn't provided their own
      const indexPath = path.join(directory, 'index.php');
      const hasUserIndex = require('fs').existsSync(indexPath);

      if (!hasUserIndex) {
        const indexPhp = `
<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');

$request_uri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);

switch ($request_uri) {
    case '/':
        echo json_encode(['message' => 'Hello from ${appData.name}!']);
        break;
    case '/health':
        echo json_encode([
            'status' => 'healthy',
            'timestamp' => date('c'),
            'service' => '${appData.name}'
        ]);
        break;
    default:
        http_response_code(404);
        echo json_encode(['error' => 'Not found']);
}
?>`;
        await fs.writeFile(indexPath, indexPhp.trim());
      }
    }

    // Install dependencies from user's composer.json (or the one we created)
    try {
      await execAsync(`cd ${directory} && composer install --no-dev`);
      logger.info(`Installed PHP dependencies from composer.json for ${appData.name}`);
    } catch (error) {
      logger.warn(`Failed to install PHP dependencies for ${appData.name}:`, error);
    }

    // Create .gitignore if it doesn't exist
    const gitignorePath = path.join(directory, '.gitignore');
    const hasGitignore = require('fs').existsSync(gitignorePath);
    if (!hasGitignore) {
      await fs.writeFile(gitignorePath, '/vendor\n.env\n*.log\n');
    }
  }

  // Set up Go web service
  private async setupGoService(directory: string, appData: CreateApplicationRequest): Promise<void> {
    const goModPath = path.join(directory, 'go.mod');
    const hasUserGoMod = require('fs').existsSync(goModPath);

    // Only create go.mod if user hasn't provided one
    if (!hasUserGoMod) {
      const goMod = `module ${appData.name}

go 1.21

require (
    github.com/gin-gonic/gin v1.9.1
)`;

      await fs.writeFile(goModPath, goMod);

      // Create basic main.go only if user hasn't provided their own
      const mainGoPath = path.join(directory, 'main.go');
      const hasUserMain = require('fs').existsSync(mainGoPath);

      if (!hasUserMain) {
        const mainGo = `
package main

import (
    "net/http"
    "github.com/gin-gonic/gin"
)

func main() {
    r := gin.Default()

    r.GET("/", func(c *gin.Context) {
        c.JSON(http.StatusOK, gin.H{
            "message": "Hello from ${appData.name}!",
        })
    })

    r.GET("/health", func(c *gin.Context) {
        c.JSON(http.StatusOK, gin.H{
            "status":  "healthy",
            "service": "${appData.name}",
        })
    })

    r.Run(":8080")
}
`;
        await fs.writeFile(mainGoPath, mainGo.trim());
      }
    }

    // Install dependencies from user's go.mod (or the one we created)
    try {
      await execAsync(`cd ${directory} && go mod tidy`);
      await execAsync(`cd ${directory} && go build`);
      logger.info(`Built Go project from go.mod for ${appData.name}`);
    } catch (error) {
      logger.warn(`Failed to build Go project for ${appData.name}:`, error);
    }

    // Create .gitignore if it doesn't exist
    const gitignorePath = path.join(directory, '.gitignore');
    const hasGitignore = require('fs').existsSync(gitignorePath);
    if (!hasGitignore) {
      await fs.writeFile(gitignorePath, '*.exe\n*.log\n.env\n');
    }
  }

  // Set up Java web service
  private async setupJavaService(directory: string, appData: CreateApplicationRequest): Promise<void> {
    const pomXmlPath = path.join(directory, 'pom.xml');
    const hasUserPom = require('fs').existsSync(pomXmlPath);

    // Only create pom.xml if user hasn't provided one
    if (!hasUserPom) {
      const pomXml = `<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.achidas</groupId>
    <artifactId>${appData.name}</artifactId>
    <version>1.0.0</version>
    <packaging>jar</packaging>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <version>3.1.0</version>
        </dependency>
    </dependencies>
</project>`;

      await fs.writeFile(pomXmlPath, pomXml);

      // Create basic Java application only if user hasn't provided their own
      await execAsync(`mkdir -p ${directory}/src/main/java/com/achidas`);
      const mainJavaPath = path.join(directory, 'src/main/java/com/achidas/Application.java');
      const hasUserMain = require('fs').existsSync(mainJavaPath);

      if (!hasUserMain) {
        const mainJava = `package com.achidas;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@SpringBootApplication
@RestController
public class Application {

    @GetMapping("/")
    public String hello() {
        return "Hello from ${appData.name}!";
    }

    @GetMapping("/health")
    public String health() {
        return "{\\"status\\": \\"healthy\\", \\"service\\": \\"${appData.name}\\"}";
    }

    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}`;
        await fs.writeFile(mainJavaPath, mainJava);
      }
    }

    // Build with Maven using user's pom.xml (or the one we created)
    try {
      await execAsync(`cd ${directory} && mvn clean compile`);
      logger.info(`Built Java project from pom.xml for ${appData.name}`);
    } catch (error) {
      logger.warn(`Failed to build Java project for ${appData.name}:`, error);
    }

    // Create .gitignore if it doesn't exist
    const gitignorePath = path.join(directory, '.gitignore');
    const hasGitignore = require('fs').existsSync(gitignorePath);
    if (!hasGitignore) {
      await fs.writeFile(gitignorePath, '/target\n*.class\n.env\n*.log\n');
    }
  }

  // Set up .NET web service
  private async setupDotNetService(directory: string, appData: CreateApplicationRequest): Promise<void> {
    const csprojPath = path.join(directory, `${appData.name}.csproj`);
    const hasUserCsproj = require('fs').existsSync(csprojPath);

    // Only create .csproj if user hasn't provided one
    if (!hasUserCsproj) {
      const csproj = `<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>
</Project>`;

      await fs.writeFile(csprojPath, csproj);

      // Create basic Program.cs only if user hasn't provided their own
      const programPath = path.join(directory, 'Program.cs');
      const hasUserProgram = require('fs').existsSync(programPath);

      if (!hasUserProgram) {
        const programCs = `var builder = WebApplication.CreateBuilder(args);
var app = builder.Build();

app.MapGet("/", () => "Hello from ${appData.name}!");
app.MapGet("/health", () => new { status = "healthy", service = "${appData.name}" });

app.Run();`;
        await fs.writeFile(programPath, programCs);
      }
    }

    // Build with dotnet using user's .csproj (or the one we created)
    try {
      await execAsync(`cd ${directory} && dotnet build`);
      logger.info(`Built .NET project from .csproj for ${appData.name}`);
    } catch (error) {
      logger.warn(`Failed to build .NET project for ${appData.name}:`, error);
    }

    // Create .gitignore if it doesn't exist
    const gitignorePath = path.join(directory, '.gitignore');
    const hasGitignore = require('fs').existsSync(gitignorePath);
    if (!hasGitignore) {
      await fs.writeFile(gitignorePath, '/bin\n/obj\n.env\n*.log\n');
    }
  }

  // Set up Ruby web service
  private async setupRubyService(directory: string, appData: CreateApplicationRequest): Promise<void> {
    const gemfilePath = path.join(directory, 'Gemfile');
    const hasUserGemfile = require('fs').existsSync(gemfilePath);

    // Only create Gemfile if user hasn't provided one
    if (!hasUserGemfile) {
      const gemfile = `source 'https://rubygems.org'

gem 'sinatra'
gem 'puma'`;

      await fs.writeFile(gemfilePath, gemfile);

      // Create basic app.rb only if user hasn't provided their own
      const appRbPath = path.join(directory, 'app.rb');
      const hasUserApp = require('fs').existsSync(appRbPath);

      if (!hasUserApp) {
        const appRb = `require 'sinatra'
require 'json'

get '/' do
  content_type :json
  { message: 'Hello from ${appData.name}!' }.to_json
end

get '/health' do
  content_type :json
  { status: 'healthy', service: '${appData.name}' }.to_json
end`;
        await fs.writeFile(appRbPath, appRb);
      }
    }

    // Install dependencies from user's Gemfile (or the one we created)
    try {
      await execAsync(`cd ${directory} && bundle install`);
      logger.info(`Installed Ruby dependencies from Gemfile for ${appData.name}`);
    } catch (error) {
      logger.warn(`Failed to install Ruby dependencies for ${appData.name}:`, error);
    }

    // Create .gitignore if it doesn't exist
    const gitignorePath = path.join(directory, '.gitignore');
    const hasGitignore = require('fs').existsSync(gitignorePath);
    if (!hasGitignore) {
      await fs.writeFile(gitignorePath, '.bundle\nvendor\n.env\n*.log\n');
    }
  }



  // =============================================================================
  // USER CRUD OPERATIONS
  // =============================================================================

  // Get all users with filtering and pagination
  async getUsers(filters: {
    page: number;
    limit: number;
    status?: string;
    plan?: string;
    server_id?: string;
    search?: string;
  }): Promise<{
    users: SharedHostingUser[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    try {
      // Build MongoDB query
      const query: any = {};

      if (filters.status) {
        query.status = filters.status;
      }

      if (filters.plan) {
        query.plan = filters.plan;
      }

      if (filters.server_id) {
        query.server_id = filters.server_id;
      }

      if (filters.search) {
        query.$or = [
          { username: { $regex: filters.search, $options: 'i' } },
          { linux_username: { $regex: filters.search, $options: 'i' } },
          { email: { $regex: filters.search, $options: 'i' } }
        ];
      }

      // Apply pagination
      const skip = (filters.page - 1) * filters.limit;

      // Execute query with pagination
      const [users, total] = await Promise.all([
        SharedHostingUser.find(query)
          .populate('applications')
          .sort({ created_at: -1 })
          .skip(skip)
          .limit(filters.limit)
          .lean(),
        SharedHostingUser.countDocuments(query)
      ]);

      const totalPages = Math.ceil(total / filters.limit);

      logger.info(`Retrieved ${users.length} users (page ${filters.page}/${totalPages})`);

      return {
        users: users.map((user: any) => {
          // Map MongoDB status to service interface status
          const mapStatus = (dbStatus: string): 'active' | 'suspended' | 'disabled' => {
            switch (dbStatus) {
              case 'active': return 'active';
              case 'suspended': return 'suspended';
              case 'deleted':
              case 'pending':
              default: return 'disabled';
            }
          };

          return {
            id: user._id.toString(),
            user_id: user.user_id,
            username: user.username,
            linux_username: user.linux_username,
            home_directory: user.home_directory,
            plan: user.plan,
            status: mapStatus(user.status),
            server_id: user.server_id,
            server_ip: user.server_ip,
            port: user.port,
            ssh_port: user.ssh_port,
            ftp_port: user.ftp_port,
            resource_limits: user.resource_limits,
            usage: user.usage,
            created_at: user.created_at.toISOString(),
            applications: (user.applications || []).map((app: any) => ({
              id: app._id.toString(),
              name: app.name,
              type: app.type,
              framework: app.framework,
              domain: app.domain,
              subdomain: app.subdomain,
              directory: app.directory,
              status: app.status,
              port: app.port,
              ssl_enabled: app.ssl_enabled,
              created_at: app.created_at.toISOString(),
              last_deployed: app.last_deployed?.toISOString()
            }))
          };
        }),
        total,
        page: filters.page,
        limit: filters.limit,
        totalPages
      };
    } catch (error) {
      logger.error('Failed to get users:', error);
      throw error;
    }
  }

  // Get specific user
  async getUser(userId: string): Promise<SharedHostingUser | null> {
    try {
      const user = await SharedHostingUser.findOne({ user_id: userId })
        .populate('applications')
        .lean();

      if (!user) {
        return null;
      }

      // Map MongoDB status to service interface status
      const mapStatus = (dbStatus: string): 'active' | 'suspended' | 'disabled' => {
        switch (dbStatus) {
          case 'active': return 'active';
          case 'suspended': return 'suspended';
          case 'deleted':
          case 'pending':
          default: return 'disabled';
        }
      };

      return {
        id: user._id.toString(),
        user_id: user.user_id,
        username: user.username,
        linux_username: user.linux_username,
        home_directory: user.home_directory,
        plan: user.plan,
        status: mapStatus(user.status),
        server_id: user.server_id,
        server_ip: user.server_ip,
        port: user.port,
        ssh_port: user.ssh_port,
        ftp_port: user.ftp_port,
        resource_limits: user.resource_limits,
        usage: user.usage,
        created_at: user.created_at.toISOString(),
        applications: (user.applications || []).map((app: any) => ({
          id: app._id.toString(),
          name: app.name,
          type: app.type,
          framework: app.framework,
          domain: app.domain,
          subdomain: app.subdomain,
          directory: app.directory,
          status: app.status,
          port: app.port,
          ssl_enabled: app.ssl_enabled,
          created_at: app.created_at.toISOString(),
          last_deployed: app.last_deployed?.toISOString()
        }))
      };
    } catch (error) {
      logger.error(`Failed to get user ${userId}:`, error);
      throw error;
    }
  }

  // Update user
  async updateUser(userId: string, updateData: {
    plan?: string;
    status?: 'active' | 'suspended' | 'disabled';
    resource_limits?: {
      cpu_quota?: number;
      memory_max?: number;
      bandwidth_limit?: number;
      storage_limit?: number;
    };
  }): Promise<SharedHostingUser> {
    try {
      const user = await this.getUser(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Update user data
      if (updateData.plan) {
        user.plan = updateData.plan;
        // Update resource limits based on new plan
        const limits = this.getPlanLimits(updateData.plan);
        user.resource_limits = {
          cpu_quota: limits.cpu_quota,
          memory_max: limits.memory_max,
          bandwidth_limit: limits.bandwidth_limit,
          storage_limit: limits.storage_limit
        };

        // Apply new resource limits via SSH
        await this.setupResourceLimits(user.linux_username, updateData.plan);
      }

      if (updateData.status) {
        user.status = updateData.status;
      }

      if (updateData.resource_limits) {
        user.resource_limits = { ...user.resource_limits, ...updateData.resource_limits };
      }

      logger.info(`Updated user ${userId}`);
      return user;
    } catch (error) {
      logger.error(`Failed to update user ${userId}:`, error);
      throw error;
    }
  }

  // Delete user
  async deleteUser(userId: string): Promise<void> {
    const ssh = new SSHConnection();
    try {
      const user = await this.getUser(userId);
      if (!user) {
        throw new Error('User not found');
      }

      await ssh.connect();

      // Stop all user processes
      await ssh.executeCommand(`pkill -u ${user.linux_username} || true`);

      // Remove user and home directory
      await ssh.executeCommand(`userdel -r ${user.linux_username} || true`);

      // Remove systemd slice
      await ssh.executeCommand(`rm -rf /etc/systemd/system/${user.linux_username}.slice.d || true`);
      await ssh.executeCommand('systemctl daemon-reexec');

      logger.info(`Deleted user ${userId} and cleaned up resources`);
    } catch (error) {
      logger.error(`Failed to delete user ${userId}:`, error);
      throw error;
    } finally {
      await ssh.disconnect();
    }
  }

  // =============================================================================
  // ANALYTICS AND MONITORING
  // =============================================================================

  // Get analytics data
  async getAnalytics(period: 'day' | 'week' | 'month' | 'year', serverId?: string): Promise<any> {
    try {
      // Calculate date range based on period
      const now = new Date();
      let startDate: Date;

      switch (period) {
        case 'day':
          startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
          break;
        case 'week':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case 'month':
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          break;
        case 'year':
          startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
          break;
      }

      // Build query for server filtering
      const userQuery = serverId ? { server_id: serverId } : {};

      // Get user statistics
      const [totalUsers, activeUsers, suspendedUsers] = await Promise.all([
        SharedHostingUser.countDocuments(userQuery),
        SharedHostingUser.countDocuments({ ...userQuery, status: 'active' }),
        SharedHostingUser.countDocuments({ ...userQuery, status: 'suspended' })
      ]);

      // Get application statistics
      const userIds = serverId ? await SharedHostingUser.find(userQuery).distinct('user_id') : [];
      const appQuery = serverId ? { user_id: { $in: userIds } } : {};

      const [totalApplications, runningApplications, stoppedApplications] = await Promise.all([
        SharedHostingApplication.countDocuments(appQuery),
        SharedHostingApplication.countDocuments({ ...appQuery, status: 'running' }),
        SharedHostingApplication.countDocuments({ ...appQuery, status: 'stopped' })
      ]);

      // Get usage analytics
      const usageQuery = serverId ? { user_id: { $in: userIds }, date: { $gte: startDate } } : { date: { $gte: startDate } };
      const usageAnalytics = await UserUsageAnalytics.find(usageQuery);

      // Calculate aggregated usage
      const totalBandwidth = usageAnalytics.reduce((sum: any, u: { bandwidth_used: any; }) => sum + u.bandwidth_used, 0);
      const totalStorage = usageAnalytics.reduce((sum: any, u: { storage_used: any; }) => sum + u.storage_used, 0);
      const avgCpu = usageAnalytics.length > 0 ? usageAnalytics.reduce((sum: any, u: { cpu_usage_avg: any; }) => sum + u.cpu_usage_avg, 0) / usageAnalytics.length : 0;
      const avgMemory = usageAnalytics.length > 0 ? usageAnalytics.reduce((sum: any, u: { memory_usage_avg: any; }) => sum + u.memory_usage_avg, 0) / usageAnalytics.length : 0;

      // Get plan distribution
      const planDistribution = await SharedHostingUser.aggregate([
        { $match: userQuery },
        { $group: { _id: '$plan', count: { $sum: 1 } } }
      ]);

      const analytics = {
        period,
        server_id: serverId || 'all',
        total_users: totalUsers,
        active_users: activeUsers,
        suspended_users: suspendedUsers,
        inactive_users: totalUsers - activeUsers - suspendedUsers,
        total_applications: totalApplications,
        running_applications: runningApplications,
        stopped_applications: stoppedApplications,
        resource_usage: {
          cpu_average: avgCpu,
          memory_average: avgMemory,
          bandwidth_total: totalBandwidth,
          storage_total: totalStorage
        },
        plan_distribution: planDistribution.reduce((acc: { [x: string]: any; }, p: { _id: string | number; count: any; }) => {
          acc[p._id] = p.count;
          return acc;
        }, {} as Record<string, number>),
        growth: {
          new_users_this_period: await SharedHostingUser.countDocuments({
            ...userQuery,
            created_at: { $gte: startDate }
          }),
          churn_rate: 0, // Calculate based on deleted users
          upgrade_rate: 0 // Calculate based on plan changes
        }
      };

      logger.info(`Retrieved analytics for period: ${period}`);
      return analytics;
    } catch (error) {
      logger.error('Failed to get analytics:', error);
      throw error;
    }
  }

  // Get user resource usage
  async getUserResourceUsage(userId: string, period: 'day' | 'week' | 'month'): Promise<any> {
    try {
      const user = await this.getUser(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Get real usage data from database
      const startDate = this.getStartDateForPeriod(period);
      const endDate = new Date();

      // Query usage analytics from database
      const usageAnalytics = await UserUsageAnalytics.find({
        user_id: userId,
        date: { $gte: startDate, $lte: endDate }
      }).sort({ date: 1 });

      // Transform database data to API format
      const usage = {
        user_id: userId,
        period,
        cpu_usage: usageAnalytics.map(u => ({
          timestamp: u.date.toISOString(),
          value: u.cpu_usage_avg
        })),
        memory_usage: usageAnalytics.map(u => ({
          timestamp: u.date.toISOString(),
          value: u.memory_usage_avg
        })),
        bandwidth_usage: usageAnalytics.map(u => ({
          timestamp: u.date.toISOString(),
          value: u.bandwidth_used
        })),
        storage_usage: user.usage.storage_used,
        limits: user.resource_limits,
        alerts: []
      };

      logger.info(`Retrieved resource usage for user: ${userId}, period: ${period}`);
      return usage;
    } catch (error) {
      logger.error(`Failed to get user resource usage for ${userId}:`, error);
      throw error;
    }
  }

  // Get server capacity
  async getServerCapacity(serverId?: string): Promise<any> {
    try {
      const targetServerId = serverId || appConfig.ssh.host;

      // Get latest server metrics
      const latestMetrics = await ServerMetrics.findOne({
        server_id: targetServerId
      }).sort({ timestamp: -1 });

      // Get user count for this server
      const userCount = await SharedHostingUser.countDocuments({
        server_id: targetServerId,
        status: { $in: ['active', 'suspended'] }
      });

      // Get server configuration from pool
      const serverConfig = this.serverPool.find(s => s.id === targetServerId || s.ip_address === targetServerId);
      const maxUsers = serverConfig?.max_users || this.MAX_USERS_PER_SERVER;

      // Calculate capacity based on real metrics or defaults
      const cpuUsage = latestMetrics?.cpu_usage || 0;
      const memoryUsage = latestMetrics?.memory_usage || 0;
      const diskUsage = latestMetrics?.disk_usage || 0;

      // Get total bandwidth usage from user analytics
      const today = new Date();
      const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());

      const dailyBandwidth = await UserUsageAnalytics.aggregate([
        {
          $match: {
            date: { $gte: startOfDay },
            user_id: { $in: await SharedHostingUser.find({ server_id: targetServerId }).distinct('user_id') }
          }
        },
        {
          $group: {
            _id: null,
            totalBandwidth: { $sum: '$bandwidth_used' }
          }
        }
      ]);

      const bandwidthUsed = dailyBandwidth[0]?.totalBandwidth || 0;
      const bandwidthLimit = 1000; // Default 1TB bandwidth limit
      const bandwidthPercent = (bandwidthUsed / bandwidthLimit) * 100;

      const capacity = {
        server_id: targetServerId,
        total_capacity: {
          cpu_cores: 4, // Default server specs
          memory_gb: 8,
          storage_gb: 160,
          bandwidth_gb: bandwidthLimit
        },
        used_capacity: {
          cpu_percent: cpuUsage,
          memory_percent: memoryUsage,
          storage_percent: diskUsage,
          bandwidth_percent: bandwidthPercent
        },
        available_capacity: {
          cpu_percent: 100 - cpuUsage,
          memory_percent: 100 - memoryUsage,
          storage_percent: 100 - diskUsage,
          bandwidth_percent: 100 - bandwidthPercent
        },
        user_count: userCount,
        max_users: maxUsers,
        status: cpuUsage > 90 || memoryUsage > 90 || diskUsage > 90 ? 'critical' :
                cpuUsage > 70 || memoryUsage > 70 || diskUsage > 70 ? 'warning' : 'healthy',
        last_updated: latestMetrics?.timestamp?.toISOString() || new Date().toISOString()
      };

      logger.info(`Retrieved server capacity${serverId ? ` for server: ${serverId}` : ' for all servers'}`);
      return capacity;
    } catch (error) {
      logger.error('Failed to get server capacity:', error);
      throw error;
    }
  }

  // =============================================================================
  // USER MANAGEMENT ACTIONS
  // =============================================================================

  // Suspend user
  async suspendUser(userId: string, reason?: string): Promise<void> {
    const ssh = new SSHConnection();
    try {
      const user = await this.getUser(userId);
      if (!user) {
        throw new Error('User not found');
      }

      await ssh.connect();

      // Stop all user processes
      await ssh.executeCommand(`pkill -u ${user.linux_username} || true`);

      // Lock the user account
      await ssh.executeCommand(`usermod -L ${user.linux_username}`);

      // Update user status (in production, this would update the database)
      user.status = 'suspended';

      logger.info(`Suspended user ${userId}, reason: ${reason || 'No reason provided'}`);
    } catch (error) {
      logger.error(`Failed to suspend user ${userId}:`, error);
      throw error;
    } finally {
      await ssh.disconnect();
    }
  }

  // Reactivate user
  async reactivateUser(userId: string): Promise<void> {
    const ssh = new SSHConnection();
    try {
      const user = await this.getUser(userId);
      if (!user) {
        throw new Error('User not found');
      }

      await ssh.connect();

      // Unlock the user account
      await ssh.executeCommand(`usermod -U ${user.linux_username}`);

      // Update user status (in production, this would update the database)
      user.status = 'active';

      logger.info(`Reactivated user ${userId}`);
    } catch (error) {
      logger.error(`Failed to reactivate user ${userId}:`, error);
      throw error;
    } finally {
      await ssh.disconnect();
    }
  }

  // Reset user password
  async resetUserPassword(userId: string, newPassword?: string): Promise<{ password: string }> {
    const ssh = new SSHConnection();
    try {
      const user = await this.getUser(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Generate random password if not provided
      const password = newPassword || Math.random().toString(36).slice(-12);

      await ssh.connect();

      // Set new password
      await ssh.executeCommand(`echo "${user.linux_username}:${password}" | chpasswd`);

      logger.info(`Reset password for user ${userId}`);
      return { password };
    } catch (error) {
      logger.error(`Failed to reset password for user ${userId}:`, error);
      throw error;
    } finally {
      await ssh.disconnect();
    }
  }

  // =============================================================================
  // APPLICATION MANAGEMENT
  // =============================================================================

  // Get user applications
  async getUserApplications(userId: string, filters: {
    page: number;
    limit: number;
    status?: string;
    type?: string;
  }): Promise<{
    applications: any[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    try {
      const user = await this.getUser(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Build query for applications
      const query: any = { user_id: userId };

      if (filters.status) {
        query.status = filters.status;
      }

      if (filters.type) {
        query.type = filters.type;
      }

      // Get total count for pagination
      const total = await SharedHostingApplication.countDocuments(query);
      const totalPages = Math.ceil(total / filters.limit);
      const skip = (filters.page - 1) * filters.limit;

      // Get applications from database with pagination
      const applications = await SharedHostingApplication.find(query)
        .sort({ created_at: -1 })
        .skip(skip)
        .limit(filters.limit)
        .lean();

      // Transform to API format
      const paginatedApps = applications.map(app => ({
        id: app._id.toString(),
        name: app.name,
        type: app.type,
        framework: app.framework,
        domain: app.domain,
        subdomain: app.subdomain,
        directory: app.directory,
        status: app.status,
        port: app.port,
        ssl_enabled: app.ssl_enabled,
        created_at: app.created_at.toISOString(),
        last_deployed: app.last_deployed?.toISOString()
      }));

      logger.info(`Retrieved ${paginatedApps.length} applications for user: ${userId}`);

      return {
        applications: paginatedApps,
        total,
        page: filters.page,
        limit: filters.limit,
        totalPages
      };
    } catch (error) {
      logger.error(`Failed to get applications for user ${userId}:`, error);
      throw error;
    }
  }

  // Update application
  async updateApplication(userId: string, appId: string, updateData: {
    name?: string;
    domain?: string;
    git_repo?: string;
    build_command?: string;
    start_command?: string;
    status?: 'running' | 'stopped';
  }): Promise<any> {
    try {
      const user = await this.getUser(userId);
      if (!user) {
        throw new Error('User not found');
      }

      const app = user.applications?.find(a => a.id === appId);
      if (!app) {
        throw new Error('Application not found');
      }

      // Update application data
      Object.assign(app, updateData);

      logger.info(`Updated application ${appId} for user: ${userId}`);
      return app;
    } catch (error) {
      logger.error(`Failed to update application ${appId} for user ${userId}:`, error);
      throw error;
    }
  }

  // Delete application
  async deleteApplication(userId: string, appId: string): Promise<void> {
    const ssh = new SSHConnection();
    try {
      const user = await this.getUser(userId);
      if (!user) {
        throw new Error('User not found');
      }

      const appIndex = user.applications?.findIndex(a => a.id === appId);
      if (appIndex === -1 || appIndex === undefined) {
        throw new Error('Application not found');
      }

      await ssh.connect();

      // Stop application processes
      await ssh.executeCommand(`pkill -f "${appId}" || true`);

      // Remove application directory
      await ssh.executeCommand(`rm -rf ${user.home_directory}/apps/${appId} || true`);

      // Remove from user applications list
      user.applications?.splice(appIndex, 1);

      logger.info(`Deleted application ${appId} for user: ${userId}`);
    } catch (error) {
      logger.error(`Failed to delete application ${appId} for user ${userId}:`, error);
      throw error;
    } finally {
      await ssh.disconnect();
    }
  }
}

let sharedHostingService: SharedHostingService;

export function getSharedHostingService(): SharedHostingService {
  if (!sharedHostingService) {
    sharedHostingService = new SharedHostingService();
  }
  return sharedHostingService;
}
