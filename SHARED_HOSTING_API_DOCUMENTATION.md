# Shared Hosting API Documentation

## Overview
This document provides comprehensive API documentation for all shared hosting endpoints, including user management, analytics, and application operations.

## Base URL
All endpoints are prefixed with: `/api/v1/services`

## Authentication
All endpoints require authentication via <PERSON><PERSON><PERSON> token in the Authorization header:
```
Authorization: Bearer <your-jwt-token>
```

## Role-Based Access Control
- **User**: Can access their own data and applications
- **Admin**: Can access all user data and perform management operations
- **Super Admin**: Can perform destructive operations like user deletion

---

## 🏠 Hosting Plans

### Get All Hosting Plans
```http
GET /hosting/plans
```
Returns all available hosting plans with pricing and features.

### Get Recommended Plan
```http
GET /hosting/plans/recommended?requirements=<requirements>
```
Get plan recommendation based on user requirements.

### Get Specific Plan
```http
GET /hosting/plans/:planId
```
Get details of a specific hosting plan.

---

## 👥 Shared Hosting User Management

### Create Shared Hosting User
```http
POST /hosting/shared/users
```
**Body:**
```json
{
  "user_id": "string",
  "username": "string", 
  "plan": "free|starter|basic|standard|pro"
}
```

### Get All Users (Admin Only)
```http
GET /hosting/shared/users?page=1&limit=20&status=active&plan=starter&search=username
```
**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 20)
- `status`: Filter by status (active|suspended|inactive)
- `plan`: Filter by plan type
- `server_id`: Filter by server
- `search`: Search by username

### Get Specific User
```http
GET /hosting/shared/users/:userId
```
Users can only access their own data unless they're admin.

### Update User (Admin Only)
```http
PUT /hosting/shared/users/:userId
```
**Body:**
```json
{
  "plan": "string",
  "status": "active|suspended|inactive",
  "resource_limits": {
    "cpu_quota": 10,
    "memory_max": 512,
    "bandwidth_limit": 50,
    "storage_limit": 10
  }
}
```

### Delete User (Super Admin Only)
```http
DELETE /hosting/shared/users/:userId
```

---

## 📊 Analytics & Monitoring

### Get Shared Hosting Analytics (Admin Only)
```http
GET /hosting/shared/analytics?period=month&server_id=*************
```
**Query Parameters:**
- `period`: day|week|month|year (default: month)
- `server_id`: Specific server ID (optional)

**Response:**
```json
{
  "total_users": 25,
  "active_users": 22,
  "resource_usage": {
    "cpu_average": 15.5,
    "memory_average": 68.2,
    "bandwidth_total": 1250.5
  },
  "plan_distribution": {
    "free": 8,
    "starter": 12,
    "basic": 3
  },
  "revenue": {
    "monthly": 245.50,
    "projected_annual": 2946.00
  }
}
```

### Get User Resource Usage
```http
GET /hosting/shared/users/:userId/usage?period=week
```
**Query Parameters:**
- `period`: day|week|month (default: week)

### Get Server Capacity (Admin Only)
```http
GET /hosting/shared/servers/capacity
GET /hosting/shared/servers/:serverId/capacity
```

---

## ⚙️ User Management Actions (Admin Only)

### Suspend User
```http
POST /hosting/shared/users/:userId/suspend
```
**Body:**
```json
{
  "reason": "Violation of terms of service"
}
```

### Reactivate User
```http
POST /hosting/shared/users/:userId/reactivate
```

### Reset User Password
```http
POST /hosting/shared/users/:userId/reset-password
```
**Body:**
```json
{
  "new_password": "optional-custom-password"
}
```
If no password provided, a random one will be generated.

---

## 🚀 Application Management

### Get User Applications
```http
GET /hosting/shared/users/:userId/applications?page=1&limit=10&status=running&type=static-website
```
**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `status`: running|stopped|building|failed
- `type`: static-website|web-service

### Create Application
```http
POST /hosting/shared/users/:userId/applications
```
**Body:**
```json
{
  "name": "My Website",
  "type": "static-website|web-service",
  "framework": "html|react|vue|angular|nodejs|python|rust|php|go|java|dotnet|ruby",
  "domain": "mywebsite.com",
  "git_repo": "https://github.com/user/repo.git",
  "build_command": "npm run build",
  "start_command": "npm start"
}
```

### Update Application
```http
PUT /hosting/shared/users/:userId/applications/:appId
```
**Body:**
```json
{
  "name": "Updated Website Name",
  "domain": "newdomain.com",
  "git_repo": "https://github.com/user/new-repo.git",
  "build_command": "yarn build",
  "start_command": "yarn start",
  "status": "running|stopped"
}
```

### Delete Application
```http
DELETE /hosting/shared/users/:userId/applications/:appId
```

---

## 📋 Response Format

All API responses follow this consistent format:

### Success Response
```json
{
  "success": true,
  "data": { ... },
  "metadata": {
    "timestamp": "2024-01-15T10:30:00Z",
    "request_id": "req_123456789"
  }
}
```

### Error Response
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Missing required fields: name, type"
  },
  "metadata": {
    "timestamp": "2024-01-15T10:30:00Z",
    "request_id": "req_123456789"
  }
}
```

---

## 🔒 Security Notes

1. **Authentication Required**: All endpoints require valid JWT token
2. **Role-Based Access**: Users can only access their own resources unless they have admin privileges
3. **Rate Limiting**: API calls are rate-limited to prevent abuse
4. **Input Validation**: All inputs are validated and sanitized
5. **Audit Logging**: All administrative actions are logged for security auditing

---

## 🚀 Getting Started

1. **Authenticate**: Obtain JWT token via login endpoint
2. **Create User**: Use POST `/hosting/shared/users` to create a shared hosting user
3. **Deploy App**: Use POST `/hosting/shared/users/:userId/applications` to deploy applications
4. **Monitor**: Use analytics endpoints to monitor usage and performance
5. **Manage**: Use admin endpoints for user and resource management

For more detailed examples and testing, see the API testing scripts in the project repository.
