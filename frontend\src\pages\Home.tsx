import { Link } from 'react-router-dom';
import Layout from '../components/layout/Layout';
import PricingTable from '../components/ui/PricingTable';
import { LiquidGlass } from '@specy/liquid-glass-react';
import {
  ServerIcon,
  GlobeAltIcon,
  ShieldCheckIcon,
  BoltIcon,
  CpuChipIcon,
  ClockIcon,
  SparklesIcon,
  RocketLaunchIcon,
  CloudIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline';

const features = [
  {
    name: 'Lightning Fast Deployment',
    description: 'Deploy your applications in seconds with our streamlined workflow.',
    icon: BoltIcon,
  },
  {
    name: 'Global CDN',
    description: 'Deliver content to your users with low latency from our global edge network.',
    icon: GlobeAltIcon,
  },
  {
    name: 'Automatic SSL',
    description: 'Every site gets free SSL certificates automatically provisioned and renewed.',
    icon: ShieldCheckIcon,
  },
  {
    name: 'Scalable Infrastructure',
    description: 'Scale your applications effortlessly as your traffic grows.',
    icon: CpuChipIcon,
  },
  {
    name: 'Continuous Deployment',
    description: 'Automatically deploy your code when you push to your repository.',
    icon: ServerIcon,
  },
  {
    name: '24/7 Monitoring',
    description: 'Keep track of your application\'s health with real-time monitoring.',
    icon: ClockIcon,
  },
];

const pricingTiers = [
  {
    name: 'Free',
    id: 'tier-free',
    price: '$0',
    description: 'Get started with basic hosting for personal projects.',
    features: [
      '512MB RAM',
      '1GB Storage',
      '0.1 CPU',
      'Unlimited Deployments',
      'Automatic SSL',
      'Community Support',
    ],
  },
  {
    name: 'Starter',
    id: 'tier-starter',
    price: '$3.99',
    description: 'Perfect for small projects and personal websites.',
    features: [
      '512MB RAM',
      '2GB Storage',
      '0.5 CPU',
      'Unlimited Deployments',
      'Automatic SSL',
      'Community Support',
    ],
  },
  {
    name: 'Tiny',
    id: 'tier-tiny',
    price: '$5.99',
    description: 'For small production applications.',
    features: [
      '1GB RAM',
      '5GB Storage',
      '1 CPU',
      'Unlimited Deployments',
      'Automatic SSL',
      'Email Support',
      'Custom Domains',
    ],
  },
  {
    name: 'Standard',
    id: 'tier-standard',
    price: '$9.99',
    description: 'For growing applications with more resource needs.',
    features: [
      '2GB RAM',
      '10GB Storage',
      '1 CPU',
      'Unlimited Deployments',
      'Automatic SSL',
      'Priority Support',
      'Custom Domains',
      'Background Workers',
    ],
    mostPopular: true,
  },
  {
    name: 'Pro',
    id: 'tier-pro',
    price: '$19.99',
    description: 'For high-traffic applications and business needs.',
    features: [
      '4GB RAM',
      '20GB Storage',
      '2 CPU',
      'Unlimited Deployments',
      'Automatic SSL',
      'Priority Support',
      'Custom Domains',
      'Background Workers',
      'Dedicated Instances',
    ],
  },
  {
    name: 'Pro Plus',
    id: 'tier-pro-plus',
    price: '$34.99',
    description: 'For resource-intensive applications.',
    features: [
      '8GB RAM',
      '40GB Storage',
      '4 CPU',
      'Unlimited Deployments',
      'Automatic SSL',
      'Priority Support',
      'Custom Domains',
      'Background Workers',
      'Dedicated Instances',
      'Advanced Monitoring',
    ],
  },
  {
    name: 'Pro Max',
    id: 'tier-pro-max',
    price: '$64.99',
    description: 'For high-performance applications.',
    features: [
      '16GB RAM',
      '80GB Storage',
      '4 CPU',
      'Unlimited Deployments',
      'Automatic SSL',
      'Priority Support',
      'Custom Domains',
      'Background Workers',
      'Dedicated Instances',
      'Advanced Monitoring',
      'Auto-scaling',
    ],
  },
  {
    name: 'Pro Ultra',
    id: 'tier-pro-ultra',
    price: '$109.99',
    description: 'For enterprise applications with high demands.',
    features: [
      '32GB RAM',
      '160GB Storage',
      '8 CPU',
      'Unlimited Deployments',
      'Automatic SSL',
      'Priority Support',
      'Custom Domains',
      'Background Workers',
      'Dedicated Instances',
      'Advanced Monitoring',
      'Auto-scaling',
      'SLA Guarantee',
    ],
  },
  {
    name: 'Custom',
    id: 'tier-custom',
    price: 'Contact',
    description: 'For large-scale enterprise applications with specific requirements.',
    features: [
      '64-512GB RAM',
      '500GB+ Storage',
      '16-64 CPU',
      'Unlimited Deployments',
      'Automatic SSL',
      'Priority Support',
      'Custom Domains',
      'Background Workers',
      'Dedicated Instances',
      'Advanced Monitoring',
      'Auto-scaling',
      'SLA Guarantee',
      'Dedicated Account Manager',
    ],
  },
];

export default function Home() {
  return (
    <Layout>
      {/* Hero section */}
      <div className="relative isolate overflow-hidden min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900">
        {/* Animated background elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-40 -right-32 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob"></div>
          <div className="absolute -bottom-40 -left-32 w-80 h-80 bg-yellow-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000"></div>
          <div className="absolute top-40 left-40 w-80 h-80 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-4000"></div>
        </div>

        <div className="relative mx-auto max-w-7xl px-6 pb-24 pt-16 sm:pb-32 lg:flex lg:px-8 lg:py-40">
          <div className="mx-auto max-w-2xl lg:mx-0 lg:max-w-xl lg:flex-shrink-0 lg:pt-8">
            <div className="mt-24 sm:mt-32 lg:mt-16">
              <LiquidGlass
                className="inline-flex space-x-6 p-4 rounded-2xl backdrop-blur-sm"
                style={{
                  background: 'rgba(255, 255, 255, 0.1)',
                  border: '1px solid rgba(255, 255, 255, 0.2)',
                }}
              >
                <span className="rounded-full bg-gradient-to-r from-purple-400 to-pink-400 px-3 py-1 text-sm font-semibold leading-6 text-white">
                  ✨ New features
                </span>
                <span className="inline-flex items-center space-x-2 text-sm font-medium leading-6 text-gray-200">
                  <span>Just shipped v2.0</span>
                  <SparklesIcon className="h-4 w-4" />
                </span>
              </LiquidGlass>
            </div>

            <h1 className="mt-10 text-4xl font-bold tracking-tight text-white sm:text-7xl">
              <span className="block">Your fastest path to</span>
              <span className="block bg-gradient-to-r from-purple-400 via-pink-400 to-yellow-400 bg-clip-text text-transparent">
                production
              </span>
            </h1>

            <p className="mt-6 text-xl leading-8 text-gray-300 max-w-2xl">
              Build, deploy, and scale your apps with unparalleled ease — from your first user to your billionth.
              Experience the future of cloud hosting.
            </p>

            <div className="mt-10 flex items-center gap-x-6">
              <LiquidGlass
                className="group relative overflow-hidden rounded-2xl p-0.5"
                style={{
                  background: 'linear-gradient(45deg, #8b5cf6, #ec4899, #f59e0b)',
                }}
              >
                <Link
                  to="/signup"
                  className="relative block rounded-2xl bg-gray-900 px-8 py-4 text-lg font-semibold text-white transition-all duration-300 hover:bg-gray-800"
                >
                  <RocketLaunchIcon className="inline h-5 w-5 mr-2" />
                  Get started for free
                </Link>
              </LiquidGlass>

              <Link
                to="/docs"
                className="group text-lg font-semibold leading-6 text-white hover:text-purple-300 transition-colors duration-300"
              >
                Learn more
                <span aria-hidden="true" className="inline-block transition-transform group-hover:translate-x-1 ml-2">→</span>
              </Link>
            </div>
          </div>

          <div className="mx-auto mt-16 flex max-w-2xl sm:mt-24 lg:ml-10 lg:mr-0 lg:mt-0 lg:max-w-none lg:flex-none xl:ml-32">
            <div className="max-w-3xl flex-none sm:max-w-5xl lg:max-w-none">
              <LiquidGlass
                className="relative rounded-3xl p-4"
                style={{
                  background: 'rgba(255, 255, 255, 0.1)',
                  border: '1px solid rgba(255, 255, 255, 0.2)',
                }}
              >
                <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-900/50 to-pink-900/50 p-8">
                  <div className="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-pink-500/20"></div>
                  <div className="relative">
                    <div className="grid grid-cols-3 gap-4 mb-6">
                      <div className="h-4 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full"></div>
                      <div className="h-4 bg-gradient-to-r from-pink-400 to-yellow-400 rounded-full"></div>
                      <div className="h-4 bg-gradient-to-r from-yellow-400 to-purple-400 rounded-full"></div>
                    </div>
                    <div className="space-y-4">
                      <div className="h-8 bg-white/20 rounded-lg"></div>
                      <div className="h-6 bg-white/15 rounded-lg w-3/4"></div>
                      <div className="h-6 bg-white/15 rounded-lg w-1/2"></div>
                      <div className="grid grid-cols-2 gap-4 mt-6">
                        <div className="h-20 bg-gradient-to-br from-purple-500/30 to-pink-500/30 rounded-xl"></div>
                        <div className="h-20 bg-gradient-to-br from-pink-500/30 to-yellow-500/30 rounded-xl"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </LiquidGlass>
            </div>
          </div>
        </div>
      </div>

      {/* Feature section */}
      <div className="relative bg-gray-900 py-24 sm:py-32 overflow-hidden">
        {/* Background gradient */}
        <div className="absolute inset-0 bg-gradient-to-b from-gray-900 via-purple-900/20 to-gray-900"></div>

        <div className="relative mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl lg:text-center">
            <LiquidGlass
              className="inline-block px-4 py-2 rounded-full mb-4"
              style={{
                background: 'rgba(139, 92, 246, 0.2)',
                border: '1px solid rgba(139, 92, 246, 0.3)',
              }}
            >
              <h2 className="text-base font-semibold leading-7 text-purple-300">⚡ Deploy faster</h2>
            </LiquidGlass>

            <p className="mt-2 text-3xl font-bold tracking-tight text-white sm:text-5xl">
              Everything you need to
              <span className="block bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                deploy your app
              </span>
            </p>

            <p className="mt-6 text-xl leading-8 text-gray-300 max-w-3xl mx-auto">
              Focus on your code, not your infrastructure. Our platform handles the heavy lifting so you can build what matters.
            </p>
          </div>

          <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
            <dl className="grid max-w-xl grid-cols-1 gap-8 lg:max-w-none lg:grid-cols-3">
              {features.map((feature, index) => (
                <LiquidGlass
                  key={feature.name}
                  className="group relative p-8 rounded-2xl transition-all duration-500 hover:scale-105"
                  style={{
                    background: 'rgba(255, 255, 255, 0.05)',
                    border: '1px solid rgba(255, 255, 255, 0.1)',
                    animationDelay: `${index * 100}ms`,
                  }}
                >
                  <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-pink-500/10 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                  <dt className="relative flex items-center gap-x-3 text-lg font-semibold leading-7 text-white mb-4">
                    <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-br from-purple-500 to-pink-500">
                      <feature.icon className="h-6 w-6 text-white" aria-hidden="true" />
                    </div>
                    {feature.name}
                  </dt>

                  <dd className="relative mt-4 flex flex-auto flex-col text-base leading-7 text-gray-300">
                    <p className="flex-auto">{feature.description}</p>
                  </dd>
                </LiquidGlass>
              ))}
            </dl>
          </div>
        </div>
      </div>

      {/* Pricing section */}
      <div className="relative bg-gradient-to-b from-gray-900 to-black py-24 sm:py-32 overflow-hidden">
        {/* Background effects */}
        <div className="absolute inset-0">
          <div className="absolute top-0 left-1/4 w-96 h-96 bg-purple-500 rounded-full mix-blend-multiply filter blur-3xl opacity-10 animate-pulse"></div>
          <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-pink-500 rounded-full mix-blend-multiply filter blur-3xl opacity-10 animate-pulse animation-delay-2000"></div>
        </div>

        <div className="relative mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl text-center">
            <LiquidGlass
              className="inline-block px-4 py-2 rounded-full mb-4"
              style={{
                background: 'rgba(236, 72, 153, 0.2)',
                border: '1px solid rgba(236, 72, 153, 0.3)',
              }}
            >
              <h2 className="text-base font-semibold leading-7 text-pink-300">💰 Pricing</h2>
            </LiquidGlass>

            <p className="mt-2 text-3xl font-bold tracking-tight text-white sm:text-5xl">
              Simple, transparent
              <span className="block bg-gradient-to-r from-pink-400 to-yellow-400 bg-clip-text text-transparent">
                pricing
              </span>
            </p>

            <p className="mt-6 text-xl leading-8 text-gray-300 max-w-3xl mx-auto">
              Choose the plan that's right for you. All plans include a 14-day free trial and can be upgraded anytime.
            </p>
          </div>

          <div className="mt-16">
            <PricingTable tiers={pricingTiers} />

            <LiquidGlass
              className="mt-12 p-8 rounded-2xl text-center"
              style={{
                background: 'rgba(255, 255, 255, 0.05)',
                border: '1px solid rgba(255, 255, 255, 0.1)',
              }}
            >
              <h3 className="text-xl font-semibold text-white mb-2">Need more resources?</h3>
              <p className="text-gray-300 mb-6">
                For custom requirements or resources beyond Pro Ultra (64-512GB RAM, 16-64 CPU, 500GB+ storage),
                <br />please contact our support team for a tailored solution.
              </p>
              <div className="mt-6">
                <LiquidGlass
                  className="inline-block p-0.5 rounded-xl"
                  style={{
                    background: 'linear-gradient(45deg, #8b5cf6, #ec4899)',
                  }}
                >
                  <a
                    href="#"
                    className="block rounded-xl bg-gray-900 px-6 py-3 text-sm font-semibold text-white hover:bg-gray-800 transition-colors duration-300"
                  >
                    Contact Support
                  </a>
                </LiquidGlass>
              </div>
            </LiquidGlass>
          </div>
        </div>
      </div>

      {/* CTA section */}
      <div className="relative bg-black py-24 sm:py-32 overflow-hidden">
        {/* Background effects */}
        <div className="absolute inset-0">
          <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-full blur-3xl"></div>
        </div>

        <div className="relative mx-auto max-w-7xl py-24 sm:px-6 sm:py-32 lg:px-8">
          <LiquidGlass
            className="relative isolate overflow-hidden px-6 py-24 text-center shadow-2xl sm:rounded-3xl sm:px-16"
            style={{
              background: 'rgba(255, 255, 255, 0.05)',
              border: '1px solid rgba(255, 255, 255, 0.1)',
            }}
          >
            <div className="absolute inset-0 bg-gradient-to-br from-purple-900/30 to-pink-900/30 rounded-3xl"></div>

            <div className="relative">
              <h2 className="mx-auto max-w-2xl text-3xl font-bold tracking-tight text-white sm:text-5xl">
                Ready to get
                <span className="block bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                  started?
                </span>
              </h2>

              <p className="mx-auto mt-6 max-w-xl text-xl leading-8 text-gray-300">
                Join thousands of developers who are already building and deploying with Achidas.
              </p>

              <div className="mt-10 flex items-center justify-center gap-x-6">
                <LiquidGlass
                  className="group relative overflow-hidden rounded-2xl p-0.5"
                  style={{
                    background: 'linear-gradient(45deg, #ffffff, #f3f4f6)',
                  }}
                >
                  <Link
                    to="/signup"
                    className="relative block rounded-2xl bg-white px-8 py-4 text-lg font-semibold text-gray-900 transition-all duration-300 hover:bg-gray-100"
                  >
                    <RocketLaunchIcon className="inline h-5 w-5 mr-2" />
                    Get started
                  </Link>
                </LiquidGlass>

                <Link
                  to="/contact"
                  className="group text-lg font-semibold leading-6 text-white hover:text-purple-300 transition-colors duration-300"
                >
                  Contact sales
                  <span aria-hidden="true" className="inline-block transition-transform group-hover:translate-x-1 ml-2">→</span>
                </Link>
              </div>
            </div>
          </LiquidGlass>
        </div>
      </div>

      {/* Domains section */}
      <section className="py-16 bg-gray-900/80 backdrop-blur-xl">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold text-white mb-4">Domains</h2>
          <p className="text-lg text-gray-300 mb-6">
            Find, register, transfer, and manage your domains with ease. Enjoy instant domain search, WHOIS lookup, and seamless transfers.
          </p>
          <a
            href="/dashboard/domains"
            className="inline-block px-8 py-3 bg-secondary-500 text-white font-semibold rounded-xl shadow-lg hover:bg-secondary-400 transition-colors duration-200"
          >
            Manage Domains
          </a>
        </div>
      </section>
    </Layout>
  );
}
