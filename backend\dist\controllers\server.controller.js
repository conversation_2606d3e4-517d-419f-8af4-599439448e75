"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getServersController = getServersController;
exports.getServerController = getServerController;
exports.getServerMetricsController = getServerMetricsController;
exports.provisionServerController = provisionServerController;
exports.getSharedServerStatusController = getSharedServerStatusController;
exports.getServerRegionsController = getServerRegionsController;
exports.getServerPlansController = getServerPlansController;
const response_1 = require("../utils/response");
const logger_1 = require("../utils/logger");
const server_management_1 = require("../services/server-management");
async function getServersController(request, reply) {
    try {
        if (!request.user) {
            return response_1.ResponseHelper.unauthorized(reply, 'Authentication required');
        }
        const { include_metrics } = request.query;
        const includeMetrics = include_metrics === 'true';
        const servers = await (0, server_management_1.getServerManagementService)().listAllServers();
        if (includeMetrics) {
            for (const server of servers) {
                try {
                    const metrics = await (0, server_management_1.getServerManagementService)().getServerMetrics(server.ip_address);
                    server.load_average = metrics.load_average;
                    server.disk_usage_percent = metrics.disk_usage;
                    server.memory_usage_percent = metrics.memory_usage;
                    server.network_usage = {
                        incoming_mb: metrics.network_in,
                        outgoing_mb: metrics.network_out
                    };
                }
                catch (error) {
                    logger_1.logger.warn(`Failed to get metrics for server ${server.id}:`, error);
                }
            }
        }
        logger_1.logger.info(`Retrieved ${servers.length} servers${includeMetrics ? ' with metrics' : ''}`);
        return response_1.ResponseHelper.success(reply, servers);
    }
    catch (error) {
        logger_1.logger.error('Get servers controller error:', error);
        return response_1.ResponseHelper.internalError(reply, 'Failed to fetch servers');
    }
}
async function getServerController(request, reply) {
    try {
        if (!request.user) {
            return response_1.ResponseHelper.unauthorized(reply, 'Authentication required');
        }
        const { serverId } = request.params;
        if (serverId === 'shared-server-main') {
            const server = await (0, server_management_1.getServerManagementService)().getSharedServerInfo();
            return response_1.ResponseHelper.success(reply, server);
        }
        const servers = await (0, server_management_1.getServerManagementService)().listAllServers();
        const server = servers.find(s => s.id === serverId);
        if (!server) {
            return response_1.ResponseHelper.notFound(reply, `Server '${serverId}' not found`);
        }
        logger_1.logger.info(`Retrieved server: ${serverId}`);
        return response_1.ResponseHelper.success(reply, server);
    }
    catch (error) {
        logger_1.logger.error('Get server controller error:', error);
        return response_1.ResponseHelper.internalError(reply, 'Failed to fetch server');
    }
}
async function getServerMetricsController(request, reply) {
    try {
        if (!request.user) {
            return response_1.ResponseHelper.unauthorized(reply, 'Authentication required');
        }
        const { serverId } = request.params;
        let serverIp;
        if (serverId === 'shared-server-main') {
            const server = await (0, server_management_1.getServerManagementService)().getSharedServerInfo();
            serverIp = server.ip_address;
        }
        else {
            const servers = await (0, server_management_1.getServerManagementService)().listAllServers();
            const server = servers.find(s => s.id === serverId);
            if (!server) {
                return response_1.ResponseHelper.notFound(reply, `Server '${serverId}' not found`);
            }
            serverIp = server.ip_address;
        }
        const metrics = await (0, server_management_1.getServerManagementService)().getServerMetrics(serverIp);
        logger_1.logger.info(`Retrieved metrics for server: ${serverId}`);
        return response_1.ResponseHelper.success(reply, metrics);
    }
    catch (error) {
        logger_1.logger.error('Get server metrics controller error:', error);
        return response_1.ResponseHelper.internalError(reply, 'Failed to fetch server metrics');
    }
}
async function provisionServerController(request, reply) {
    try {
        if (!request.user) {
            return response_1.ResponseHelper.unauthorized(reply, 'Authentication required');
        }
        const provisionData = request.body;
        if (!provisionData.plan || !provisionData.region || !provisionData.hostname || !provisionData.tier) {
            return response_1.ResponseHelper.validationError(reply, 'Missing required fields: plan, region, hostname, tier');
        }
        if (!['dedicated', 'enterprise'].includes(provisionData.tier)) {
            return response_1.ResponseHelper.validationError(reply, 'Invalid tier. Must be "dedicated" or "enterprise"');
        }
        const provisionRequest = {
            ...provisionData,
            user_id: request.user.sub
        };
        const server = await (0, server_management_1.getServerManagementService)().provisionServer(provisionRequest);
        logger_1.logger.info(`Provisioned ${provisionData.tier} server: ${server.id} for user: ${request.user.sub}`);
        return response_1.ResponseHelper.success(reply, server, 201);
    }
    catch (error) {
        logger_1.logger.error('Provision server controller error:', error);
        if (error instanceof Error) {
            if (error.message.includes('API key')) {
                return response_1.ResponseHelper.internalError(reply, 'Server provisioning service not configured');
            }
            if (error.message.includes('Vultr API error')) {
                return response_1.ResponseHelper.validationError(reply, error.message);
            }
        }
        return response_1.ResponseHelper.internalError(reply, 'Failed to provision server');
    }
}
async function getSharedServerStatusController(request, reply) {
    try {
        if (!request.user) {
            return response_1.ResponseHelper.unauthorized(reply, 'Authentication required');
        }
        const server = await (0, server_management_1.getServerManagementService)().getSharedServerInfo();
        const metrics = await (0, server_management_1.getServerManagementService)().getServerMetrics(server.ip_address);
        const status = {
            server_info: server,
            current_metrics: metrics,
            capacity: {
                users_current: server.users_count || 0,
                users_max: server.max_users || 50,
                users_available: (server.max_users || 50) - (server.users_count || 0),
                capacity_percentage: ((server.users_count || 0) / (server.max_users || 50)) * 100
            },
            health: {
                status: metrics.cpu_usage < 80 && metrics.memory_usage < 80 && metrics.disk_usage < 90 ? 'healthy' : 'warning',
                cpu_status: metrics.cpu_usage < 80 ? 'normal' : 'high',
                memory_status: metrics.memory_usage < 80 ? 'normal' : 'high',
                disk_status: metrics.disk_usage < 90 ? 'normal' : 'high'
            }
        };
        logger_1.logger.info('Retrieved shared server status');
        return response_1.ResponseHelper.success(reply, status);
    }
    catch (error) {
        logger_1.logger.error('Get shared server status controller error:', error);
        return response_1.ResponseHelper.internalError(reply, 'Failed to fetch shared server status');
    }
}
async function getServerRegionsController(_request, reply) {
    try {
        const regions = [
            {
                id: 'jnb',
                name: 'South Africa (Johannesburg)',
                country: 'South Africa',
                city: 'Johannesburg',
                continent: 'Africa',
                available: true
            },
            {
                id: 'ewr',
                name: 'United States (New Jersey)',
                country: 'United States',
                city: 'New Jersey',
                continent: 'North America',
                available: true
            },
            {
                id: 'lhr',
                name: 'United Kingdom (London)',
                country: 'United Kingdom',
                city: 'London',
                continent: 'Europe',
                available: true
            },
            {
                id: 'fra',
                name: 'Germany (Frankfurt)',
                country: 'Germany',
                city: 'Frankfurt',
                continent: 'Europe',
                available: true
            },
            {
                id: 'sgp',
                name: 'Singapore',
                country: 'Singapore',
                city: 'Singapore',
                continent: 'Asia',
                available: true
            }
        ];
        logger_1.logger.info('Retrieved server regions');
        return response_1.ResponseHelper.success(reply, regions);
    }
    catch (error) {
        logger_1.logger.error('Get server regions controller error:', error);
        return response_1.ResponseHelper.internalError(reply, 'Failed to fetch server regions');
    }
}
async function getServerPlansController(request, reply) {
    try {
        const { tier } = request.query;
        const plans = [
            {
                id: 'vc2-1c-1gb',
                name: 'Small Dedicated',
                tier: 'dedicated',
                cpu: 1,
                ram_mb: 1024,
                disk_gb: 25,
                bandwidth_gb: 1024,
                price_per_hour: 0.007,
                price_per_month: 5.00,
                recommended_for: ['Small businesses', 'Development', 'Testing']
            },
            {
                id: 'vc2-2c-4gb',
                name: 'Medium Dedicated',
                tier: 'dedicated',
                cpu: 2,
                ram_mb: 4096,
                disk_gb: 80,
                bandwidth_gb: 3072,
                price_per_hour: 0.024,
                price_per_month: 17.28,
                recommended_for: ['Medium businesses', 'E-commerce', 'Web applications']
            },
            {
                id: 'vc2-4c-8gb',
                name: 'Enterprise Standard',
                tier: 'enterprise',
                cpu: 4,
                ram_mb: 8192,
                disk_gb: 160,
                bandwidth_gb: 4096,
                price_per_hour: 0.048,
                price_per_month: 34.56,
                recommended_for: ['Large businesses', 'High-traffic applications', 'SaaS platforms']
            },
            {
                id: 'vc2-8c-16gb',
                name: 'Enterprise Premium',
                tier: 'enterprise',
                cpu: 8,
                ram_mb: 16384,
                disk_gb: 320,
                bandwidth_gb: 5120,
                price_per_hour: 0.095,
                price_per_month: 68.40,
                recommended_for: ['Enterprise applications', 'High-performance computing', 'Large SaaS']
            }
        ];
        let filteredPlans = plans;
        if (tier) {
            filteredPlans = plans.filter(plan => plan.tier === tier);
        }
        logger_1.logger.info(`Retrieved ${filteredPlans.length} server plans${tier ? ` for tier: ${tier}` : ''}`);
        return response_1.ResponseHelper.success(reply, filteredPlans);
    }
    catch (error) {
        logger_1.logger.error('Get server plans controller error:', error);
        return response_1.ResponseHelper.internalError(reply, 'Failed to fetch server plans');
    }
}
//# sourceMappingURL=server.controller.js.map