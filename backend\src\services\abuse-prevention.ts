import { logger } from '../utils/logger';
import { appConfig } from '../config';
import { SharedHostingUser, SharedHostingApplication, UserUsageAnalytics, ServerMetrics } from '../models/shared-hosting';
import { SSHConnection } from './shared-hosting';

interface AbuseThresholds {
  cpu: { warning: number; critical: number; suspend: number };
  memory: { warning: number; critical: number; suspend: number };
  bandwidth: { warning: number; critical: number; suspend: number };
  storage: { warning: number; critical: number; suspend: number };
  requests: { warning: number; critical: number; suspend: number };
}

interface AbuseAlert {
  userId: string;
  type: 'cpu' | 'memory' | 'bandwidth' | 'storage' | 'requests';
  severity: 'warning' | 'critical' | 'suspend';
  value: number;
  threshold: number;
  timestamp: Date;
  action: string;
}

export class AbusePreventionService {
  private monitoringInterval: NodeJS.Timeout | null = null;
  private isMonitoring = false;
  
  private thresholds: AbuseThresholds = {
    cpu: { warning: 70, critical: 85, suspend: 95 },
    memory: { warning: 80, critical: 90, suspend: 95 },
    bandwidth: { warning: 80, critical: 90, suspend: 95 }, // Percentage of plan limit
    storage: { warning: 85, critical: 95, suspend: 98 },
    requests: { warning: 1000, critical: 2000, suspend: 5000 } // Requests per minute
  };

  constructor() {
    this.startMonitoring();
  }

  // Start abuse monitoring
  startMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
    }

    const intervalMs = appConfig.security.abuseCheckIntervalMinutes * 60 * 1000;
    
    this.monitoringInterval = setInterval(async () => {
      if (!this.isMonitoring) {
        this.isMonitoring = true;
        try {
          await this.checkForAbuse();
        } catch (error) {
          logger.error('Abuse monitoring failed:', error);
        } finally {
          this.isMonitoring = false;
        }
      }
    }, intervalMs);

    logger.info(`Abuse prevention monitoring started with ${appConfig.security.abuseCheckIntervalMinutes}min interval`);
  }

  // Stop monitoring
  stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
    logger.info('Abuse prevention monitoring stopped');
  }

  // Check for abuse across all users
  async checkForAbuse(): Promise<void> {
    try {
      const activeUsers = await SharedHostingUser.find({ 
        status: 'active',
        server_id: appConfig.ssh.host 
      });

      logger.debug(`Checking ${activeUsers.length} users for abuse`);

      for (const user of activeUsers) {
        try {
          await this.checkUserAbuse(user);
        } catch (error) {
          logger.error(`Failed to check abuse for user ${user.username}:`, error);
        }
      }

      // Check server-level abuse
      await this.checkServerAbuse();
      
    } catch (error) {
      logger.error('Failed to check for abuse:', error);
      throw error;
    }
  }

  // Check individual user for abuse
  async checkUserAbuse(user: any): Promise<void> {
    const alerts: AbuseAlert[] = [];
    
    // Get user's current usage
    const usage = user.usage;
    const limits = user.resource_limits;

    // Check CPU usage
    const cpuPercent = (usage.cpu_usage / limits.cpu_quota) * 100;
    if (cpuPercent >= this.thresholds.cpu.suspend) {
      alerts.push(this.createAlert(user.user_id, 'cpu', 'suspend', cpuPercent, this.thresholds.cpu.suspend));
    } else if (cpuPercent >= this.thresholds.cpu.critical) {
      alerts.push(this.createAlert(user.user_id, 'cpu', 'critical', cpuPercent, this.thresholds.cpu.critical));
    } else if (cpuPercent >= this.thresholds.cpu.warning) {
      alerts.push(this.createAlert(user.user_id, 'cpu', 'warning', cpuPercent, this.thresholds.cpu.warning));
    }

    // Check memory usage
    const memoryPercent = (usage.memory_usage / limits.memory_max) * 100;
    if (memoryPercent >= this.thresholds.memory.suspend) {
      alerts.push(this.createAlert(user.user_id, 'memory', 'suspend', memoryPercent, this.thresholds.memory.suspend));
    } else if (memoryPercent >= this.thresholds.memory.critical) {
      alerts.push(this.createAlert(user.user_id, 'memory', 'critical', memoryPercent, this.thresholds.memory.critical));
    } else if (memoryPercent >= this.thresholds.memory.warning) {
      alerts.push(this.createAlert(user.user_id, 'memory', 'warning', memoryPercent, this.thresholds.memory.warning));
    }

    // Check bandwidth usage
    const bandwidthPercent = (usage.bandwidth_used / limits.bandwidth_limit) * 100;
    if (bandwidthPercent >= this.thresholds.bandwidth.suspend) {
      alerts.push(this.createAlert(user.user_id, 'bandwidth', 'suspend', bandwidthPercent, this.thresholds.bandwidth.suspend));
    } else if (bandwidthPercent >= this.thresholds.bandwidth.critical) {
      alerts.push(this.createAlert(user.user_id, 'bandwidth', 'critical', bandwidthPercent, this.thresholds.bandwidth.critical));
    } else if (bandwidthPercent >= this.thresholds.bandwidth.warning) {
      alerts.push(this.createAlert(user.user_id, 'bandwidth', 'warning', bandwidthPercent, this.thresholds.bandwidth.warning));
    }

    // Check storage usage
    const storagePercent = (usage.storage_used / limits.storage_limit) * 100;
    if (storagePercent >= this.thresholds.storage.suspend) {
      alerts.push(this.createAlert(user.user_id, 'storage', 'suspend', storagePercent, this.thresholds.storage.suspend));
    } else if (storagePercent >= this.thresholds.storage.critical) {
      alerts.push(this.createAlert(user.user_id, 'storage', 'critical', storagePercent, this.thresholds.storage.critical));
    } else if (storagePercent >= this.thresholds.storage.warning) {
      alerts.push(this.createAlert(user.user_id, 'storage', 'warning', storagePercent, this.thresholds.storage.warning));
    }

    // Check request rate (from recent analytics)
    const recentAnalytics = await UserUsageAnalytics.findOne({
      user_id: user.user_id,
      date: { $gte: new Date(Date.now() - 60 * 60 * 1000) } // Last hour
    });

    if (recentAnalytics) {
      const requestsPerMinute = recentAnalytics.requests_count / 60;
      if (requestsPerMinute >= this.thresholds.requests.suspend) {
        alerts.push(this.createAlert(user.user_id, 'requests', 'suspend', requestsPerMinute, this.thresholds.requests.suspend));
      } else if (requestsPerMinute >= this.thresholds.requests.critical) {
        alerts.push(this.createAlert(user.user_id, 'requests', 'critical', requestsPerMinute, this.thresholds.requests.critical));
      } else if (requestsPerMinute >= this.thresholds.requests.warning) {
        alerts.push(this.createAlert(user.user_id, 'requests', 'warning', requestsPerMinute, this.thresholds.requests.warning));
      }
    }

    // Process alerts
    for (const alert of alerts) {
      await this.processAlert(alert);
    }
  }

  // Check server-level abuse
  async checkServerAbuse(): Promise<void> {
    try {
      const latestMetrics = await ServerMetrics.findOne({ 
        server_id: appConfig.ssh.host 
      }).sort({ timestamp: -1 });

      if (!latestMetrics) {
        return;
      }

      // Check server CPU usage
      if (latestMetrics.cpu_usage > 90) {
        await this.handleServerOverload('cpu', latestMetrics.cpu_usage);
      }

      // Check server memory usage
      if (latestMetrics.memory_usage > 90) {
        await this.handleServerOverload('memory', latestMetrics.memory_usage);
      }

      // Check server disk usage
      if (latestMetrics.disk_usage > 95) {
        await this.handleServerOverload('disk', latestMetrics.disk_usage);
      }

    } catch (error) {
      logger.error('Failed to check server abuse:', error);
    }
  }

  // Create abuse alert
  private createAlert(userId: string, type: any, severity: any, value: number, threshold: number): AbuseAlert {
    return {
      userId,
      type,
      severity,
      value,
      threshold,
      timestamp: new Date(),
      action: this.getActionForSeverity(severity)
    };
  }

  // Get action based on severity
  private getActionForSeverity(severity: string): string {
    switch (severity) {
      case 'warning':
        return 'log_and_notify';
      case 'critical':
        return 'throttle_resources';
      case 'suspend':
        return 'suspend_user';
      default:
        return 'log_only';
    }
  }

  // Process abuse alert
  async processAlert(alert: AbuseAlert): Promise<void> {
    try {
      logger.warn(`Abuse detected for user ${alert.userId}: ${alert.type} usage ${alert.value.toFixed(2)}% exceeds ${alert.threshold}% threshold`);

      switch (alert.action) {
        case 'log_and_notify':
          await this.logAndNotify(alert);
          break;
        case 'throttle_resources':
          await this.throttleUserResources(alert);
          break;
        case 'suspend_user':
          await this.suspendUser(alert);
          break;
        default:
          logger.info(`Alert logged for user ${alert.userId}`);
      }

      // Store alert in database (could be implemented)
      // await this.storeAlert(alert);

    } catch (error) {
      logger.error(`Failed to process alert for user ${alert.userId}:`, error);
    }
  }

  // Log and notify about abuse
  async logAndNotify(alert: AbuseAlert): Promise<void> {
    logger.warn(`WARNING: User ${alert.userId} ${alert.type} usage at ${alert.value.toFixed(2)}%`);
    
    // Could send email notification to user
    // await this.sendNotificationEmail(alert);
  }

  // Throttle user resources
  async throttleUserResources(alert: AbuseAlert): Promise<void> {
    const ssh = new SSHConnection();
    
    try {
      await ssh.connect();
      
      const user = await SharedHostingUser.findOne({ user_id: alert.userId });
      if (!user) {
        return;
      }

      logger.warn(`THROTTLING: User ${alert.userId} ${alert.type} usage at ${alert.value.toFixed(2)}%`);

      // Apply resource throttling based on abuse type
      switch (alert.type) {
        case 'cpu':
          await this.throttleCPU(ssh, user, 50); // Reduce to 50% of limit
          break;
        case 'memory':
          await this.throttleMemory(ssh, user, 80); // Reduce to 80% of limit
          break;
        case 'bandwidth':
          await this.throttleBandwidth(ssh, user, 50); // Reduce to 50% of limit
          break;
        case 'requests':
          await this.throttleRequests(ssh, user);
          break;
      }

    } catch (error) {
      logger.error(`Failed to throttle user ${alert.userId}:`, error);
    } finally {
      await ssh.disconnect();
    }
  }

  // Suspend user for severe abuse
  async suspendUser(alert: AbuseAlert): Promise<void> {
    try {
      logger.error(`SUSPENDING: User ${alert.userId} ${alert.type} usage at ${alert.value.toFixed(2)}%`);

      // Update user status
      await SharedHostingUser.findOneAndUpdate(
        { user_id: alert.userId },
        { 
          status: 'suspended',
          suspension_reason: `Abuse detected: ${alert.type} usage exceeded ${alert.threshold}%`,
          suspended_at: new Date()
        }
      );

      // Stop user services
      await this.stopUserServices(alert.userId);

    } catch (error) {
      logger.error(`Failed to suspend user ${alert.userId}:`, error);
    }
  }

  // Throttle CPU usage
  async throttleCPU(ssh: SSHConnection, user: any, percentage: number): Promise<void> {
    const newQuota = Math.floor(user.resource_limits.cpu_quota * (percentage / 100));
    await ssh.exec(`systemctl set-property user-${user.linux_username}.slice CPUQuota=${newQuota}%`);
  }

  // Throttle memory usage
  async throttleMemory(ssh: SSHConnection, user: any, percentage: number): Promise<void> {
    const newLimit = Math.floor(user.resource_limits.memory_max * (percentage / 100));
    await ssh.exec(`systemctl set-property user-${user.linux_username}.slice MemoryMax=${newLimit}M`);
  }

  // Throttle bandwidth
  async throttleBandwidth(ssh: SSHConnection, user: any, percentage: number): Promise<void> {
    const newLimit = Math.floor(user.resource_limits.bandwidth_limit * (percentage / 100));
    await ssh.exec(`tc class change dev eth0 parent 1: classid 1:${user.port} htb rate ${newLimit}mbit`);
  }

  // Throttle request rate
  async throttleRequests(ssh: SSHConnection, user: any): Promise<void> {
    // Implement request rate limiting via iptables or nginx
    await ssh.exec(`iptables -A INPUT -p tcp --dport ${user.port} -m limit --limit 10/min -j ACCEPT`);
  }

  // Stop user services
  async stopUserServices(userId: string): Promise<void> {
    const ssh = new SSHConnection();
    
    try {
      await ssh.connect();
      
      const user = await SharedHostingUser.findOne({ user_id: userId });
      if (!user) {
        return;
      }

      // Stop user systemd services
      await ssh.exec(`systemctl stop user-${user.linux_username}.slice`);
      
      // Stop user applications
      const applications = await SharedHostingApplication.find({ user_id: userId });
      for (const app of applications) {
        if (app.status === 'running') {
          await ssh.exec(`systemctl stop ${app.name}-${user.linux_username}`);
          await SharedHostingApplication.findOneAndUpdate(
            { application_id: app.application_id },
            { status: 'stopped' }
          );
        }
      }

    } catch (error) {
      logger.error(`Failed to stop services for user ${userId}:`, error);
    } finally {
      await ssh.disconnect();
    }
  }

  // Handle server overload
  async handleServerOverload(type: string, usage: number): Promise<void> {
    logger.error(`SERVER OVERLOAD: ${type} usage at ${usage.toFixed(2)}%`);
    
    // Get top resource consumers
    const topUsers = await this.getTopResourceConsumers(type, 5);
    
    // Throttle top consumers
    for (const user of topUsers) {
      const alert = this.createAlert(user.user_id, type as any, 'critical', usage, 90);
      await this.throttleUserResources(alert);
    }
  }

  // Get top resource consumers
  async getTopResourceConsumers(type: string, limit: number): Promise<any[]> {
    const sortField = type === 'cpu' ? 'usage.cpu_usage' : 
                     type === 'memory' ? 'usage.memory_usage' : 
                     'usage.storage_used';

    return await SharedHostingUser.find({ 
      status: 'active',
      server_id: appConfig.ssh.host 
    })
    .sort({ [sortField]: -1 })
    .limit(limit);
  }

  // Get abuse statistics
  async getAbuseStats(): Promise<any> {
    // This could be implemented to return abuse statistics
    return {
      totalAlerts: 0,
      suspendedUsers: await SharedHostingUser.countDocuments({ status: 'suspended' }),
      throttledUsers: 0, // Could track this
      serverOverloads: 0 // Could track this
    };
  }
}

// Export singleton instance
export const abusePreventionService = new AbusePreventionService();
