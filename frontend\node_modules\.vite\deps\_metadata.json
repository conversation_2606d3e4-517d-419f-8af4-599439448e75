{"hash": "bce2ae5c", "configHash": "091ab3e2", "lockfileHash": "99f98f03", "browserHash": "4d952f0f", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "27c60416", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "9e0e8ba2", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "a7b09f63", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "25ec62ba", "needsInterop": true}, "@headlessui/react": {"src": "../../@headlessui/react/dist/headlessui.esm.js", "file": "@headlessui_react.js", "fileHash": "3ff9176f", "needsInterop": false}, "@heroicons/react/20/solid": {"src": "../../@heroicons/react/20/solid/esm/index.js", "file": "@heroicons_react_20_solid.js", "fileHash": "3d7f78af", "needsInterop": false}, "@heroicons/react/24/outline": {"src": "../../@heroicons/react/24/outline/esm/index.js", "file": "@heroicons_react_24_outline.js", "fileHash": "7f6d4acf", "needsInterop": false}, "@monaco-editor/react": {"src": "../../@monaco-editor/react/dist/index.mjs", "file": "@monaco-editor_react.js", "fileHash": "6fbb3609", "needsInterop": false}, "@xterm/addon-fit": {"src": "../../@xterm/addon-fit/lib/addon-fit.js", "file": "@xterm_addon-fit.js", "fileHash": "756c9cf1", "needsInterop": true}, "@xterm/addon-search": {"src": "../../@xterm/addon-search/lib/addon-search.js", "file": "@xterm_addon-search.js", "fileHash": "43c53d96", "needsInterop": true}, "@xterm/addon-web-links": {"src": "../../@xterm/addon-web-links/lib/addon-web-links.js", "file": "@xterm_addon-web-links.js", "fileHash": "fa38efa1", "needsInterop": true}, "@xterm/xterm": {"src": "../../@xterm/xterm/lib/xterm.js", "file": "@xterm_xterm.js", "fileHash": "91d79a34", "needsInterop": true}, "axios": {"src": "../../axios/index.js", "file": "axios.js", "fileHash": "037e166e", "needsInterop": false}, "chart.js": {"src": "../../chart.js/dist/chart.js", "file": "chart__js.js", "fileHash": "e236dfb8", "needsInterop": false}, "react-chartjs-2": {"src": "../../react-chartjs-2/dist/index.js", "file": "react-chartjs-2.js", "fileHash": "1ae4165c", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "036cc534", "needsInterop": true}, "react-hotkeys-hook": {"src": "../../react-hotkeys-hook/packages/react-hotkeys-hook/dist/index.js", "file": "react-hotkeys-hook.js", "fileHash": "87b2bf49", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "7647b97d", "needsInterop": false}, "zustand": {"src": "../../zustand/esm/index.mjs", "file": "zustand.js", "fileHash": "29526bb2", "needsInterop": false}, "zustand/middleware": {"src": "../../zustand/esm/middleware.mjs", "file": "zustand_middleware.js", "fileHash": "2178e854", "needsInterop": false}, "@specy/liquid-glass-react": {"src": "../../../../node_modules/@specy/liquid-glass-react/dist/index.js", "file": "@specy_liquid-glass-react.js", "fileHash": "84e39237", "needsInterop": false}}, "chunks": {"chunk-V3UIB5FC": {"file": "chunk-V3UIB5FC.js"}, "chunk-JNNNAK6O": {"file": "chunk-JNNNAK6O.js"}, "chunk-A7ECLLTJ": {"file": "chunk-A7ECLLTJ.js"}, "chunk-HSUUC2QV": {"file": "chunk-HSUUC2QV.js"}, "chunk-DC5AMYBS": {"file": "chunk-DC5AMYBS.js"}}}