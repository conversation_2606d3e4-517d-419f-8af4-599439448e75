import { logger } from '../utils/logger';
import { appConfig } from '../config';
import { ServerMetrics, UserUsageAnalytics, SharedHostingUser } from '../models/shared-hosting';
import { SSHConnection } from './shared-hosting';

export class MetricsCollectorService {
  private collectionInterval: NodeJS.Timeout | null = null;
  private isCollecting = false;

  constructor() {
    this.startCollection();
  }

  // Start metrics collection
  startCollection(): void {
    if (this.collectionInterval) {
      clearInterval(this.collectionInterval);
    }

    const intervalMs = appConfig.monitoring.intervalSeconds * 1000;
    
    this.collectionInterval = setInterval(async () => {
      if (!this.isCollecting) {
        this.isCollecting = true;
        try {
          await this.collectServerMetrics();
          await this.collectUserUsageMetrics();
        } catch (error) {
          logger.error('Error collecting metrics:', error);
        } finally {
          this.isCollecting = false;
        }
      }
    }, intervalMs);

    logger.info(`Started metrics collection with ${appConfig.monitoring.intervalSeconds}s interval`);
  }

  // Stop metrics collection
  stopCollection(): void {
    if (this.collectionInterval) {
      clearInterval(this.collectionInterval);
      this.collectionInterval = null;
    }
    logger.info('Stopped metrics collection');
  }

  // Collect server metrics via SSH
  async collectServerMetrics(): Promise<void> {
    const ssh = new SSHConnection();
    
    try {
      await ssh.connect();

      // Get CPU usage
      const cpuUsage = await this.getCpuUsage(ssh);
      
      // Get memory usage
      const memoryUsage = await this.getMemoryUsage(ssh);
      
      // Get disk usage
      const diskUsage = await this.getDiskUsage(ssh);
      
      // Get load average
      const loadAverage = await this.getLoadAverage(ssh);
      
      // Get network stats
      const networkStats = await this.getNetworkStats(ssh);
      
      // Get active user count
      const activeUsers = await SharedHostingUser.countDocuments({ 
        server_id: appConfig.ssh.host,
        status: 'active' 
      });

      // Get active application count
      const activeApplications = await SharedHostingUser.aggregate([
        { $match: { server_id: appConfig.ssh.host, status: 'active' } },
        { $lookup: { from: 'shared_hosting_applications', localField: 'user_id', foreignField: 'user_id', as: 'apps' } },
        { $unwind: '$apps' },
        { $match: { 'apps.status': 'running' } },
        { $count: 'total' }
      ]);

      // Save metrics to database
      const metrics = new ServerMetrics({
        server_id: appConfig.ssh.host,
        server_ip: appConfig.ssh.host,
        timestamp: new Date(),
        cpu_usage: cpuUsage,
        memory_usage: memoryUsage,
        disk_usage: diskUsage,
        load_average: loadAverage,
        network_in: networkStats.bytesIn,
        network_out: networkStats.bytesOut,
        active_users: activeUsers,
        active_applications: activeApplications[0]?.total || 0
      });

      await metrics.save();
      
      logger.debug(`Collected server metrics: CPU ${cpuUsage}%, Memory ${memoryUsage}%, Disk ${diskUsage}%`);
      
    } catch (error) {
      logger.error('Failed to collect server metrics:', error);
    } finally {
      await ssh.disconnect();
    }
  }

  // Collect user usage metrics
  async collectUserUsageMetrics(): Promise<void> {
    try {
      const activeUsers = await SharedHostingUser.find({ 
        status: 'active',
        server_id: appConfig.ssh.host 
      });

      const today = new Date();
      today.setHours(0, 0, 0, 0);

      for (const user of activeUsers) {
        // Check if we already have analytics for today
        const existingAnalytics = await UserUsageAnalytics.findOne({
          user_id: user.user_id,
          date: today
        });

        if (!existingAnalytics) {
          // Create new analytics record
          const analytics = new UserUsageAnalytics({
            user_id: user.user_id,
            date: today,
            cpu_usage_avg: user.usage.cpu_usage,
            cpu_usage_max: user.usage.cpu_usage,
            memory_usage_avg: user.usage.memory_usage,
            memory_usage_max: user.usage.memory_usage,
            bandwidth_used: user.usage.bandwidth_used,
            storage_used: user.usage.storage_used,
            requests_count: 0,
            uptime_percentage: 100
          });

          await analytics.save();
        } else {
          // Update existing analytics
          existingAnalytics.cpu_usage_avg = (existingAnalytics.cpu_usage_avg + user.usage.cpu_usage) / 2;
          existingAnalytics.cpu_usage_max = Math.max(existingAnalytics.cpu_usage_max, user.usage.cpu_usage);
          existingAnalytics.memory_usage_avg = (existingAnalytics.memory_usage_avg + user.usage.memory_usage) / 2;
          existingAnalytics.memory_usage_max = Math.max(existingAnalytics.memory_usage_max, user.usage.memory_usage);
          existingAnalytics.bandwidth_used = user.usage.bandwidth_used;
          existingAnalytics.storage_used = user.usage.storage_used;

          await existingAnalytics.save();
        }
      }

      logger.debug(`Updated usage analytics for ${activeUsers.length} users`);
      
    } catch (error) {
      logger.error('Failed to collect user usage metrics:', error);
    }
  }

  // Get CPU usage percentage
  private async getCpuUsage(ssh: SSHConnection): Promise<number> {
    try {
      const result = await ssh.exec("top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | awk -F'%' '{print $1}'");
      return parseFloat(result.trim()) || 0;
    } catch (error) {
      logger.error('Failed to get CPU usage:', error);
      return 0;
    }
  }

  // Get memory usage percentage
  private async getMemoryUsage(ssh: SSHConnection): Promise<number> {
    try {
      const result = await ssh.exec("free | grep Mem | awk '{printf \"%.1f\", $3/$2 * 100.0}'");
      return parseFloat(result.trim()) || 0;
    } catch (error) {
      logger.error('Failed to get memory usage:', error);
      return 0;
    }
  }

  // Get disk usage percentage
  private async getDiskUsage(ssh: SSHConnection): Promise<number> {
    try {
      const result = await ssh.exec("df -h / | awk 'NR==2{printf \"%s\", $5}' | sed 's/%//'");
      return parseFloat(result.trim()) || 0;
    } catch (error) {
      logger.error('Failed to get disk usage:', error);
      return 0;
    }
  }

  // Get load average
  private async getLoadAverage(ssh: SSHConnection): Promise<number> {
    try {
      const result = await ssh.exec("uptime | awk -F'load average:' '{print $2}' | awk -F',' '{print $1}' | xargs");
      return parseFloat(result.trim()) || 0;
    } catch (error) {
      logger.error('Failed to get load average:', error);
      return 0;
    }
  }

  // Get network statistics
  private async getNetworkStats(ssh: SSHConnection): Promise<{ bytesIn: number; bytesOut: number }> {
    try {
      const result = await ssh.exec("cat /proc/net/dev | grep eth0 | awk '{print $2, $10}'");
      const [bytesIn, bytesOut] = result.trim().split(' ').map(Number);
      return { 
        bytesIn: bytesIn || 0, 
        bytesOut: bytesOut || 0 
      };
    } catch (error) {
      logger.error('Failed to get network stats:', error);
      return { bytesIn: 0, bytesOut: 0 };
    }
  }

  // Clean up old metrics based on retention policy
  async cleanupOldMetrics(): Promise<void> {
    try {
      const retentionDate = new Date();
      retentionDate.setDate(retentionDate.getDate() - appConfig.monitoring.retentionDays);

      // Clean up server metrics
      const deletedServerMetrics = await ServerMetrics.deleteMany({
        timestamp: { $lt: retentionDate }
      });

      // Clean up user usage analytics
      const deletedUserAnalytics = await UserUsageAnalytics.deleteMany({
        date: { $lt: retentionDate }
      });

      logger.info(`Cleaned up ${deletedServerMetrics.deletedCount} server metrics and ${deletedUserAnalytics.deletedCount} user analytics records older than ${appConfig.monitoring.retentionDays} days`);
      
    } catch (error) {
      logger.error('Failed to cleanup old metrics:', error);
    }
  }
}

// Export singleton instance
export const metricsCollector = new MetricsCollectorService();
