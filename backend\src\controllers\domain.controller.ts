import { FastifyRequest, FastifyReply } from 'fastify';
import { getResellerBizService, DomainAvailabilityRequest, DomainTransferRequest, WhoisLookupRequest } from '../services/resellerbiz';
import { ResponseHelper } from '../utils/response';
import { logger } from '../utils/logger';

// Domain availability check controller
export async function checkDomainAvailabilityController(
  request: FastifyRequest<{ Body: DomainAvailabilityRequest }>,
  reply: FastifyReply
): Promise<FastifyReply> {
  try {
    const { domains, tlds } = request.body;

    // Validation
    if (!domains || !Array.isArray(domains) || domains.length === 0) {
      return ResponseHelper.validationError(reply, 'domains array is required and must not be empty');
    }

    if (!tlds || !Array.isArray(tlds) || tlds.length === 0) {
      return ResponseHelper.validationError(reply, 'tlds array is required and must not be empty');
    }

    // Validate domain names
    const domainRegex = /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?$/;
    for (const domain of domains) {
      if (!domain || typeof domain !== 'string' || !domainRegex.test(domain)) {
        return ResponseHelper.validationError(reply, `Invalid domain name: ${domain}`);
      }
    }

    // Validate TLDs
    const tldRegex = /^[a-zA-Z]{2,}$/;
    for (const tld of tlds) {
      if (!tld || typeof tld !== 'string' || !tldRegex.test(tld)) {
        return ResponseHelper.validationError(reply, `Invalid TLD: ${tld}`);
      }
    }

    const result = await getResellerBizService().checkDomainAvailability({ domains, tlds });

    logger.info(`Domain availability check completed for ${domains.length} domains with ${tlds.length} TLDs`);

    return ResponseHelper.success(reply, result);
  } catch (error) {
    logger.error('Domain availability check controller error:', error);
    return ResponseHelper.internalError(reply, 'Failed to check domain availability');
  }
}

// Domain transfer controller
export async function transferDomainController(
  request: FastifyRequest<{ Body: DomainTransferRequest }>,
  reply: FastifyReply
): Promise<FastifyReply> {
  try {
    const transferData = request.body;

    // Validation
    if (!transferData.domainName || typeof transferData.domainName !== 'string') {
      return ResponseHelper.validationError(reply, 'domainName is required and must be a string');
    }

    if (!transferData.customerId || typeof transferData.customerId !== 'number') {
      return ResponseHelper.validationError(reply, 'customerId is required and must be a number');
    }

    if (!transferData.regContactId || typeof transferData.regContactId !== 'number') {
      return ResponseHelper.validationError(reply, 'regContactId is required and must be a number');
    }

    if (!transferData.adminContactId || typeof transferData.adminContactId !== 'number') {
      return ResponseHelper.validationError(reply, 'adminContactId is required and must be a number');
    }

    if (!transferData.techContactId || typeof transferData.techContactId !== 'number') {
      return ResponseHelper.validationError(reply, 'techContactId is required and must be a number');
    }

    if (!transferData.billingContactId || typeof transferData.billingContactId !== 'number') {
      return ResponseHelper.validationError(reply, 'billingContactId is required and must be a number');
    }

    if (!transferData.invoiceOption || !['NoInvoice', 'PayInvoice', 'KeepInvoice', 'OnlyAdd'].includes(transferData.invoiceOption)) {
      return ResponseHelper.validationError(reply, 'invoiceOption is required and must be one of: NoInvoice, PayInvoice, KeepInvoice, OnlyAdd');
    }

    if (typeof transferData.autoRenew !== 'boolean') {
      return ResponseHelper.validationError(reply, 'autoRenew is required and must be a boolean');
    }

    // Validate domain name format
    const domainRegex = /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.[a-zA-Z]{2,}$/;
    if (!domainRegex.test(transferData.domainName)) {
      return ResponseHelper.validationError(reply, 'Invalid domain name format');
    }

    // Validate name servers if provided
    if (transferData.nameServers && Array.isArray(transferData.nameServers)) {
      const nsRegex = /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.[a-zA-Z]{2,}$/;
      for (const ns of transferData.nameServers) {
        if (!nsRegex.test(ns)) {
          return ResponseHelper.validationError(reply, `Invalid name server format: ${ns}`);
        }
      }
    }

    const result = await getResellerBizService().transferDomain(transferData);

    logger.info(`Domain transfer initiated for ${transferData.domainName}`);

    return ResponseHelper.success(reply, result, 201);
  } catch (error) {
    logger.error('Domain transfer controller error:', error);
    return ResponseHelper.internalError(reply, 'Failed to transfer domain');
  }
}

// WHOIS lookup controller
export async function whoisLookupController(
  request: FastifyRequest<{ Body: WhoisLookupRequest }>,
  reply: FastifyReply
): Promise<FastifyReply> {
  try {
    const { domainName } = request.body;

    // Validation
    if (!domainName || typeof domainName !== 'string') {
      return ResponseHelper.validationError(reply, 'domainName is required and must be a string');
    }

    // Validate domain name format
    const domainRegex = /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.[a-zA-Z]{2,}$/;
    if (!domainRegex.test(domainName)) {
      return ResponseHelper.validationError(reply, 'Invalid domain name format');
    }

    const result = await getResellerBizService().whoisLookup({ domainName });

    logger.info(`WHOIS lookup completed for ${domainName}`);

    return ResponseHelper.success(reply, result);
  } catch (error) {
    logger.error('WHOIS lookup controller error:', error);
    return ResponseHelper.internalError(reply, 'Failed to perform WHOIS lookup');
  }
}

// Get domain search suggestions controller
export async function getDomainSuggestionsController(
  request: FastifyRequest<{ 
    Querystring: { 
      keyword: string; 
      tlds?: string; 
      limit?: string; 
    } 
  }>,
  reply: FastifyReply
): Promise<FastifyReply> {
  try {
    const { keyword, tlds, limit } = request.query;

    // Validation
    if (!keyword || typeof keyword !== 'string' || keyword.trim().length === 0) {
      return ResponseHelper.validationError(reply, 'keyword query parameter is required');
    }

    const keywordClean = keyword.trim().toLowerCase();
    const limitNum = limit ? parseInt(limit, 10) : 10;
    const tldList = tlds ? tlds.split(',').map(t => t.trim()) : ['com', 'net', 'org', 'info', 'biz'];

    // Validate limit
    if (isNaN(limitNum) || limitNum < 1 || limitNum > 50) {
      return ResponseHelper.validationError(reply, 'limit must be a number between 1 and 50');
    }

    // Generate domain suggestions
    const suggestions = [];
    const variations = [
      keywordClean,
      `${keywordClean}app`,
      `${keywordClean}web`,
      `${keywordClean}site`,
      `${keywordClean}online`,
      `my${keywordClean}`,
      `get${keywordClean}`,
      `${keywordClean}hub`,
      `${keywordClean}zone`,
      `${keywordClean}pro`,
    ];

    for (const variation of variations.slice(0, Math.ceil(limitNum / tldList.length))) {
      for (const tld of tldList) {
        if (suggestions.length >= limitNum) break;
        suggestions.push(`${variation}.${tld}`);
      }
      if (suggestions.length >= limitNum) break;
    }

    logger.info(`Generated ${suggestions.length} domain suggestions for keyword: ${keyword}`);

    return ResponseHelper.success(reply, {
      keyword: keywordClean,
      suggestions: suggestions.slice(0, limitNum),
      total: suggestions.length,
    });
  } catch (error) {
    logger.error('Domain suggestions controller error:', error);
    return ResponseHelper.internalError(reply, 'Failed to generate domain suggestions');
  }
}

// Get popular TLDs controller
export async function getPopularTldsController(
  _request: FastifyRequest,
  reply: FastifyReply
): Promise<FastifyReply> {
  try {
    const popularTlds = [
      { tld: 'com', name: 'Commercial', description: 'Most popular and trusted TLD' },
      { tld: 'net', name: 'Network', description: 'Great for tech and networking sites' },
      { tld: 'org', name: 'Organization', description: 'Perfect for non-profits and organizations' },
      { tld: 'info', name: 'Information', description: 'Ideal for informational websites' },
      { tld: 'biz', name: 'Business', description: 'Professional choice for businesses' },
      { tld: 'co', name: 'Company', description: 'Modern alternative to .com' },
      { tld: 'io', name: 'Input/Output', description: 'Popular among tech startups' },
      { tld: 'app', name: 'Application', description: 'Perfect for mobile and web apps' },
      { tld: 'dev', name: 'Developer', description: 'Ideal for developers and tech projects' },
      { tld: 'tech', name: 'Technology', description: 'Great for technology companies' },
    ];

    logger.info('Retrieved popular TLDs list');

    return ResponseHelper.success(reply, popularTlds);
  } catch (error) {
    logger.error('Get popular TLDs controller error:', error);
    return ResponseHelper.internalError(reply, 'Failed to fetch popular TLDs');
  }
}
