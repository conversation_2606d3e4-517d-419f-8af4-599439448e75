import { SSHConnection } from './shared-hosting';
interface BackupConfig {
    type: 'full' | 'incremental' | 'database' | 'files';
    schedule: string;
    retention: number;
    compression: boolean;
    encryption: boolean;
    destination: 'local' | 's3' | 'vultr-object-storage';
}
interface BackupJob {
    id: string;
    userId: string;
    type: string;
    status: 'pending' | 'running' | 'completed' | 'failed';
    startTime: Date;
    endTime?: Date;
    size?: number;
    location: string;
    error?: string;
}
export declare class BackupRecoveryService {
    private backupJobs;
    private scheduledBackups;
    constructor();
    initializeScheduledBackups(): Promise<void>;
    scheduleBackup(jobId: string, config: BackupConfig): void;
    scheduleUserBackup(userId: string, config: BackupConfig): void;
    performServerBackup(config: BackupConfig): Promise<string>;
    performUserBackup(userId: string, config: BackupConfig): Promise<string>;
    backupUserDatabases(ssh: SSHConnection, user: any, backupDir: string): Promise<void>;
    encryptBackup(ssh: SSHConnection, filePath: string): Promise<void>;
    uploadBackup(ssh: SSHConnection, filePath: string, destination: string, prefix?: string): Promise<string>;
    restoreFromBackup(backupLocation: string, userId?: string): Promise<void>;
    downloadBackup(ssh: SSHConnection, backupLocation: string, localPath: string): Promise<void>;
    decryptBackup(ssh: SSHConnection, filePath: string): Promise<void>;
    restoreUserData(ssh: SSHConnection, extractDir: string, userId: string): Promise<void>;
    restoreServerData(ssh: SSHConnection, extractDir: string): Promise<void>;
    private parseCronToInterval;
    private generateJobId;
    getBackupJob(jobId: string): BackupJob | undefined;
    listBackupJobs(userId?: string): BackupJob[];
    cleanupOldBackups(retentionDays: number): Promise<void>;
}
export declare const backupRecovery: BackupRecoveryService;
export {};
//# sourceMappingURL=backup-recovery.d.ts.map