import { Application, ApplicationStatus, CreateApplicationRequest, UpdateApplicationRequest, ApplicationResponse, PaginationOptions, PaginatedResponse } from '../models';
export declare class ApplicationService {
    private applicationsCollection;
    constructor();
    private generateSubdomain;
    private generateUniqueSubdomain;
    private isSubdomainUnique;
    createApplication(userId: string, applicationData: CreateApplicationRequest): Promise<ApplicationResponse>;
    getApplicationById(applicationId: string, userId?: string): Promise<ApplicationResponse>;
    getApplicationsByUser(userId: string, options?: PaginationOptions): Promise<PaginatedResponse<ApplicationResponse>>;
    updateApplication(applicationId: string, userId: string, updateData: UpdateApplicationRequest): Promise<ApplicationResponse>;
    deleteApplication(applicationId: string, userId: string): Promise<void>;
    updateApplicationStatus(applicationId: string, status: ApplicationStatus, userId?: string): Promise<ApplicationResponse>;
    getApplicationStats(userId: string): Promise<any>;
    createApplicationDNS(applicationId: string, serverIP: string): Promise<void>;
    updateApplicationDNS(applicationId: string, newServerIP: string): Promise<void>;
    deleteApplicationDNS(applicationId: string): Promise<void>;
    deployApplication(applicationId: string, userId: string): Promise<Application>;
}
export declare function getApplicationService(): ApplicationService;
//# sourceMappingURL=application.d.ts.map