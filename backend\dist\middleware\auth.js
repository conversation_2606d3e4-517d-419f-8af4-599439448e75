"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.authMiddleware = authMiddleware;
exports.adminMiddleware = adminMiddleware;
exports.superAdminMiddleware = superAdminMiddleware;
exports.optionalAuthMiddleware = optionalAuthMiddleware;
exports.createOwnershipMiddleware = createOwnershipMiddleware;
exports.createUserRateLimitMiddleware = createUserRateLimitMiddleware;
const jwt = __importStar(require("jsonwebtoken"));
const config_1 = require("../config");
const response_1 = require("../utils/response");
const models_1 = require("../models");
const logger_1 = require("../utils/logger");
async function authMiddleware(request, reply) {
    try {
        const authHeader = request.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return response_1.ResponseHelper.unauthorized(reply, 'Authorization token required');
        }
        const token = authHeader.substring(7);
        try {
            const decoded = jwt.verify(token, config_1.appConfig.auth.jwtSecret);
            request.user = decoded;
        }
        catch (jwtError) {
            logger_1.logger.warn('Invalid JWT token:', jwtError);
            return response_1.ResponseHelper.unauthorized(reply, 'Invalid or expired token');
        }
    }
    catch (error) {
        logger_1.logger.error('Auth middleware error:', error);
        return response_1.ResponseHelper.internalError(reply, 'Authentication error');
    }
}
async function adminMiddleware(request, reply) {
    if (!request.user) {
        return response_1.ResponseHelper.unauthorized(reply, 'Authentication required');
    }
    if (request.user.role !== models_1.UserRole.ADMIN && request.user.role !== models_1.UserRole.SUPER_ADMIN) {
        return response_1.ResponseHelper.forbidden(reply, 'Admin access required');
    }
}
async function superAdminMiddleware(request, reply) {
    if (!request.user) {
        return response_1.ResponseHelper.unauthorized(reply, 'Authentication required');
    }
    if (request.user.role !== models_1.UserRole.SUPER_ADMIN) {
        return response_1.ResponseHelper.forbidden(reply, 'Super admin access required');
    }
}
async function optionalAuthMiddleware(request, _reply) {
    try {
        const authHeader = request.headers.authorization;
        if (authHeader && authHeader.startsWith('Bearer ')) {
            const token = authHeader.substring(7);
            try {
                const decoded = jwt.verify(token, config_1.appConfig.auth.jwtSecret);
                request.user = decoded;
            }
            catch (jwtError) {
                logger_1.logger.debug('Optional auth - invalid token:', jwtError);
            }
        }
    }
    catch (error) {
        logger_1.logger.error('Optional auth middleware error:', error);
    }
}
function createOwnershipMiddleware(userIdParam = 'userId') {
    return async function ownershipMiddleware(request, reply) {
        if (!request.user) {
            return response_1.ResponseHelper.unauthorized(reply, 'Authentication required');
        }
        const params = request.params;
        const resourceUserId = params[userIdParam];
        if (request.user.role === models_1.UserRole.SUPER_ADMIN) {
            return;
        }
        if (request.user.sub !== resourceUserId) {
            return response_1.ResponseHelper.forbidden(reply, 'Access denied to this resource');
        }
    };
}
function createUserRateLimitMiddleware(maxRequests = 100, windowMs = 60000) {
    const userRequests = new Map();
    return async function userRateLimitMiddleware(request, reply) {
        if (!request.user) {
            return;
        }
        const userId = request.user.sub;
        const now = Date.now();
        const userLimit = userRequests.get(userId);
        if (!userLimit || now > userLimit.resetTime) {
            userRequests.set(userId, {
                count: 1,
                resetTime: now + windowMs,
            });
            return;
        }
        if (userLimit.count >= maxRequests) {
            return response_1.ResponseHelper.error(reply, 'RATE_LIMIT_EXCEEDED', 'Too many requests. Please try again later.', 429);
        }
        userLimit.count++;
    };
}
//# sourceMappingURL=auth.js.map