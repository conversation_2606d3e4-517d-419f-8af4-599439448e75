export interface DeploymentJob {
    id: string;
    user_id: string;
    application_id: string;
    type: 'provision_user' | 'deploy_app' | 'scale_resources' | 'backup_data' | 'monitor_health';
    status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
    progress: number;
    logs: string[];
    started_at?: string;
    completed_at?: string;
    error_message?: string;
    metadata: Record<string, any>;
}
export interface AutomationMetrics {
    total_jobs: number;
    pending_jobs: number;
    running_jobs: number;
    completed_jobs: number;
    failed_jobs: number;
    success_rate: number;
    average_completion_time: number;
    jobs_last_24h: number;
}
export interface ResourceMonitoringData {
    server_id: string;
    timestamp: string;
    cpu_usage: number;
    memory_usage: number;
    disk_usage: number;
    network_in: number;
    network_out: number;
    active_users: number;
    load_average: number[];
}
declare class DeploymentAutomationService {
    private readonly jobs;
    private readonly monitoringData;
    private readonly SHARED_SERVER_IP;
    createDeploymentJob(type: DeploymentJob['type'], userId: string, applicationId: string, metadata?: Record<string, any>): Promise<DeploymentJob>;
    private executeJob;
    private executeProvisionUser;
    private executeDeployApp;
    private executeScaleResources;
    private executeBackupData;
    private executeMonitorHealth;
    getJob(jobId: string): DeploymentJob | undefined;
    listJobs(userId?: string, status?: DeploymentJob['status']): DeploymentJob[];
    getAutomationMetrics(): AutomationMetrics;
    getMonitoringData(hours?: number): ResourceMonitoringData[];
    cancelJob(jobId: string): Promise<void>;
}
export declare function getDeploymentAutomationService(): DeploymentAutomationService;
export {};
//# sourceMappingURL=deployment-automation.d.ts.map