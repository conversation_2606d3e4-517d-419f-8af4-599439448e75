"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.checkDomainAvailabilityController = checkDomainAvailabilityController;
exports.transferDomainController = transferDomainController;
exports.whoisLookupController = whoisLookupController;
exports.getDomainSuggestionsController = getDomainSuggestionsController;
exports.getPopularTldsController = getPopularTldsController;
const resellerbiz_1 = require("../services/resellerbiz");
const response_1 = require("../utils/response");
const logger_1 = require("../utils/logger");
async function checkDomainAvailabilityController(request, reply) {
    try {
        const { domains, tlds } = request.body;
        if (!domains || !Array.isArray(domains) || domains.length === 0) {
            return response_1.ResponseHelper.validationError(reply, 'domains array is required and must not be empty');
        }
        if (!tlds || !Array.isArray(tlds) || tlds.length === 0) {
            return response_1.ResponseHelper.validationError(reply, 'tlds array is required and must not be empty');
        }
        const domainRegex = /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?$/;
        for (const domain of domains) {
            if (!domain || typeof domain !== 'string' || !domainRegex.test(domain)) {
                return response_1.ResponseHelper.validationError(reply, `Invalid domain name: ${domain}`);
            }
        }
        const tldRegex = /^[a-zA-Z]{2,}$/;
        for (const tld of tlds) {
            if (!tld || typeof tld !== 'string' || !tldRegex.test(tld)) {
                return response_1.ResponseHelper.validationError(reply, `Invalid TLD: ${tld}`);
            }
        }
        const result = await (0, resellerbiz_1.getResellerBizService)().checkDomainAvailability({ domains, tlds });
        logger_1.logger.info(`Domain availability check completed for ${domains.length} domains with ${tlds.length} TLDs`);
        return response_1.ResponseHelper.success(reply, result);
    }
    catch (error) {
        logger_1.logger.error('Domain availability check controller error:', error);
        return response_1.ResponseHelper.internalError(reply, 'Failed to check domain availability');
    }
}
async function transferDomainController(request, reply) {
    try {
        const transferData = request.body;
        if (!transferData.domainName || typeof transferData.domainName !== 'string') {
            return response_1.ResponseHelper.validationError(reply, 'domainName is required and must be a string');
        }
        if (!transferData.customerId || typeof transferData.customerId !== 'number') {
            return response_1.ResponseHelper.validationError(reply, 'customerId is required and must be a number');
        }
        if (!transferData.regContactId || typeof transferData.regContactId !== 'number') {
            return response_1.ResponseHelper.validationError(reply, 'regContactId is required and must be a number');
        }
        if (!transferData.adminContactId || typeof transferData.adminContactId !== 'number') {
            return response_1.ResponseHelper.validationError(reply, 'adminContactId is required and must be a number');
        }
        if (!transferData.techContactId || typeof transferData.techContactId !== 'number') {
            return response_1.ResponseHelper.validationError(reply, 'techContactId is required and must be a number');
        }
        if (!transferData.billingContactId || typeof transferData.billingContactId !== 'number') {
            return response_1.ResponseHelper.validationError(reply, 'billingContactId is required and must be a number');
        }
        if (!transferData.invoiceOption || !['NoInvoice', 'PayInvoice', 'KeepInvoice', 'OnlyAdd'].includes(transferData.invoiceOption)) {
            return response_1.ResponseHelper.validationError(reply, 'invoiceOption is required and must be one of: NoInvoice, PayInvoice, KeepInvoice, OnlyAdd');
        }
        if (typeof transferData.autoRenew !== 'boolean') {
            return response_1.ResponseHelper.validationError(reply, 'autoRenew is required and must be a boolean');
        }
        const domainRegex = /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.[a-zA-Z]{2,}$/;
        if (!domainRegex.test(transferData.domainName)) {
            return response_1.ResponseHelper.validationError(reply, 'Invalid domain name format');
        }
        if (transferData.nameServers && Array.isArray(transferData.nameServers)) {
            const nsRegex = /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.[a-zA-Z]{2,}$/;
            for (const ns of transferData.nameServers) {
                if (!nsRegex.test(ns)) {
                    return response_1.ResponseHelper.validationError(reply, `Invalid name server format: ${ns}`);
                }
            }
        }
        const result = await (0, resellerbiz_1.getResellerBizService)().transferDomain(transferData);
        logger_1.logger.info(`Domain transfer initiated for ${transferData.domainName}`);
        return response_1.ResponseHelper.success(reply, result, 201);
    }
    catch (error) {
        logger_1.logger.error('Domain transfer controller error:', error);
        return response_1.ResponseHelper.internalError(reply, 'Failed to transfer domain');
    }
}
async function whoisLookupController(request, reply) {
    try {
        const { domainName } = request.body;
        if (!domainName || typeof domainName !== 'string') {
            return response_1.ResponseHelper.validationError(reply, 'domainName is required and must be a string');
        }
        const domainRegex = /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.[a-zA-Z]{2,}$/;
        if (!domainRegex.test(domainName)) {
            return response_1.ResponseHelper.validationError(reply, 'Invalid domain name format');
        }
        const result = await (0, resellerbiz_1.getResellerBizService)().whoisLookup({ domainName });
        logger_1.logger.info(`WHOIS lookup completed for ${domainName}`);
        return response_1.ResponseHelper.success(reply, result);
    }
    catch (error) {
        logger_1.logger.error('WHOIS lookup controller error:', error);
        return response_1.ResponseHelper.internalError(reply, 'Failed to perform WHOIS lookup');
    }
}
async function getDomainSuggestionsController(request, reply) {
    try {
        const { keyword, tlds, limit } = request.query;
        if (!keyword || typeof keyword !== 'string' || keyword.trim().length === 0) {
            return response_1.ResponseHelper.validationError(reply, 'keyword query parameter is required');
        }
        const keywordClean = keyword.trim().toLowerCase();
        const limitNum = limit ? parseInt(limit, 10) : 10;
        const tldList = tlds ? tlds.split(',').map(t => t.trim()) : ['com', 'net', 'org', 'info', 'biz'];
        if (isNaN(limitNum) || limitNum < 1 || limitNum > 50) {
            return response_1.ResponseHelper.validationError(reply, 'limit must be a number between 1 and 50');
        }
        const suggestions = [];
        const variations = [
            keywordClean,
            `${keywordClean}app`,
            `${keywordClean}web`,
            `${keywordClean}site`,
            `${keywordClean}online`,
            `my${keywordClean}`,
            `get${keywordClean}`,
            `${keywordClean}hub`,
            `${keywordClean}zone`,
            `${keywordClean}pro`,
        ];
        for (const variation of variations.slice(0, Math.ceil(limitNum / tldList.length))) {
            for (const tld of tldList) {
                if (suggestions.length >= limitNum)
                    break;
                suggestions.push(`${variation}.${tld}`);
            }
            if (suggestions.length >= limitNum)
                break;
        }
        logger_1.logger.info(`Generated ${suggestions.length} domain suggestions for keyword: ${keyword}`);
        return response_1.ResponseHelper.success(reply, {
            keyword: keywordClean,
            suggestions: suggestions.slice(0, limitNum),
            total: suggestions.length,
        });
    }
    catch (error) {
        logger_1.logger.error('Domain suggestions controller error:', error);
        return response_1.ResponseHelper.internalError(reply, 'Failed to generate domain suggestions');
    }
}
async function getPopularTldsController(_request, reply) {
    try {
        const popularTlds = [
            { tld: 'com', name: 'Commercial', description: 'Most popular and trusted TLD' },
            { tld: 'net', name: 'Network', description: 'Great for tech and networking sites' },
            { tld: 'org', name: 'Organization', description: 'Perfect for non-profits and organizations' },
            { tld: 'info', name: 'Information', description: 'Ideal for informational websites' },
            { tld: 'biz', name: 'Business', description: 'Professional choice for businesses' },
            { tld: 'co', name: 'Company', description: 'Modern alternative to .com' },
            { tld: 'io', name: 'Input/Output', description: 'Popular among tech startups' },
            { tld: 'app', name: 'Application', description: 'Perfect for mobile and web apps' },
            { tld: 'dev', name: 'Developer', description: 'Ideal for developers and tech projects' },
            { tld: 'tech', name: 'Technology', description: 'Great for technology companies' },
        ];
        logger_1.logger.info('Retrieved popular TLDs list');
        return response_1.ResponseHelper.success(reply, popularTlds);
    }
    catch (error) {
        logger_1.logger.error('Get popular TLDs controller error:', error);
        return response_1.ResponseHelper.internalError(reply, 'Failed to fetch popular TLDs');
    }
}
//# sourceMappingURL=domain.controller.js.map