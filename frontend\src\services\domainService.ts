import apiClient from './api';

// Domain availability interfaces
export interface DomainAvailabilityRequest {
  domains: string[];
  tlds: string[];
}

export interface DomainAvailabilityResult {
  [domain: string]: {
    status: 'available' | 'regthroughus' | 'regthroughothers' | 'unknown';
    classkey?: string;
    ispremiumname?: boolean;
    premiumcost?: number;
    eapfee?: number;
    trademark?: {
      trademark_name: string;
      trademark_number: string;
      trademark_country: string;
      trademark_date: string;
    };
  };
}

// Domain transfer interfaces
export interface DomainTransferRequest {
  domainName: string;
  authCode?: string;
  customerId: number;
  regContactId: number;
  adminContactId: number;
  techContactId: number;
  billingContactId: number;
  invoiceOption: 'NoInvoice' | 'PayInvoice' | 'KeepInvoice' | 'OnlyAdd';
  autoRenew: boolean;
  purchasePrivacy?: boolean;
  protectPrivacy?: boolean;
  nameServers?: string[];
  attributes?: { [key: string]: string };
  purchasePremiumDns?: boolean;
}

export interface DomainTransferResult {
  description: string;
  entityid: string;
  actiontype: string;
  actiontypedesc: string;
  eaqid: string;
  actionstatus: string;
  actionstatusdesc: string;
  invoiceid?: string;
  sellingcurrencysymbol?: string;
  sellingamount?: number;
  customerid: number;
  privacydetails?: any;
}

// WHOIS lookup interfaces
export interface WhoisLookupRequest {
  domainName: string;
}

export interface WhoisLookupResult {
  domain: string;
  registrar: string;
  registrant: {
    name: string;
    organization?: string;
    email?: string;
    phone?: string;
    address?: string;
    city?: string;
    state?: string;
    country?: string;
    postalCode?: string;
  };
  admin?: {
    name: string;
    organization?: string;
    email?: string;
    phone?: string;
  };
  tech?: {
    name: string;
    organization?: string;
    email?: string;
    phone?: string;
  };
  nameServers: string[];
  creationDate?: string;
  expirationDate?: string;
  updatedDate?: string;
  status: string[];
}

// Domain suggestions interfaces
export interface DomainSuggestionsRequest {
  keyword: string;
  tlds?: string;
  limit?: number;
}

export interface DomainSuggestionsResult {
  keyword: string;
  suggestions: string[];
  total: number;
}

// Popular TLDs interface
export interface PopularTld {
  tld: string;
  name: string;
  description: string;
}

export class DomainService {
  /**
   * Check domain availability
   */
  static async checkAvailability(request: DomainAvailabilityRequest): Promise<DomainAvailabilityResult> {
    const response = await apiClient.post('/domains/availability', request);
    return response.data.data;
  }

  /**
   * Transfer domain
   */
  static async transferDomain(request: DomainTransferRequest): Promise<DomainTransferResult> {
    const response = await apiClient.post('/domains/transfer', request);
    return response.data.data;
  }

  /**
   * WHOIS lookup
   */
  static async whoisLookup(request: WhoisLookupRequest): Promise<WhoisLookupResult> {
    const response = await apiClient.post('/domains/whois', request);
    return response.data.data;
  }

  /**
   * Get domain suggestions
   */
  static async getDomainSuggestions(request: DomainSuggestionsRequest): Promise<DomainSuggestionsResult> {
    const params = new URLSearchParams();
    params.append('keyword', request.keyword);
    if (request.tlds) {
      params.append('tlds', request.tlds);
    }
    if (request.limit) {
      params.append('limit', request.limit.toString());
    }

    const response = await apiClient.get(`/domains/suggestions?${params.toString()}`);
    return response.data.data;
  }

  /**
   * Get popular TLDs
   */
  static async getPopularTlds(): Promise<PopularTld[]> {
    const response = await apiClient.get('/domains/tlds/popular');
    return response.data.data;
  }

  /**
   * Helper method to extract domain name and TLD from full domain
   */
  static parseDomain(fullDomain: string): { name: string; tld: string } {
    const parts = fullDomain.split('.');
    if (parts.length < 2) {
      throw new Error('Invalid domain format');
    }
    
    const tld = parts.slice(-1)[0];
    const name = parts.slice(0, -1).join('.');
    
    return { name, tld };
  }

  /**
   * Helper method to format domain availability status for display
   */
  static formatAvailabilityStatus(status: string): { text: string; color: string; available: boolean } {
    switch (status) {
      case 'available':
        return { text: 'Available', color: 'green', available: true };
      case 'regthroughus':
        return { text: 'Registered through us', color: 'blue', available: false };
      case 'regthroughothers':
        return { text: 'Registered elsewhere', color: 'red', available: false };
      case 'unknown':
        return { text: 'Status unknown', color: 'gray', available: false };
      default:
        return { text: 'Unknown status', color: 'gray', available: false };
    }
  }

  /**
   * Helper method to format price for display
   */
  static formatPrice(amount: number, currency: string = 'USD'): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
    }).format(amount);
  }

  /**
   * Helper method to validate domain name format
   */
  static validateDomainName(domain: string): boolean {
    const domainRegex = /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.[a-zA-Z]{2,}$/;
    return domainRegex.test(domain);
  }

  /**
   * Helper method to validate TLD format
   */
  static validateTld(tld: string): boolean {
    const tldRegex = /^[a-zA-Z]{2,}$/;
    return tldRegex.test(tld);
  }

  /**
   * Helper method to clean domain name (remove protocol, www, etc.)
   */
  static cleanDomainName(input: string): string {
    return input
      .toLowerCase()
      .replace(/^https?:\/\//, '') // Remove protocol
      .replace(/^www\./, '') // Remove www
      .replace(/\/$/, '') // Remove trailing slash
      .trim();
  }

  /**
   * Helper method to generate domain variations for suggestions
   */
  static generateDomainVariations(keyword: string): string[] {
    const cleanKeyword = keyword.toLowerCase().replace(/[^a-z0-9]/g, '');
    
    return [
      cleanKeyword,
      `${cleanKeyword}app`,
      `${cleanKeyword}web`,
      `${cleanKeyword}site`,
      `${cleanKeyword}online`,
      `my${cleanKeyword}`,
      `get${cleanKeyword}`,
      `${cleanKeyword}hub`,
      `${cleanKeyword}zone`,
      `${cleanKeyword}pro`,
      `${cleanKeyword}tech`,
      `${cleanKeyword}dev`,
    ];
  }
}
