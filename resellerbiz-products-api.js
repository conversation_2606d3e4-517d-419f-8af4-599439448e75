const axios = require('axios');

/**
 * ResellerBiz Products API Client
 * Aggregates product details and category mappings from ResellerBiz API
 */
class ResellerBizProductsAPI {
  constructor(authUserId, apiKey, isTestMode = false) {
    this.authUserId = authUserId;
    this.apiKey = apiKey;
    this.baseUrl = isTestMode 
      ? 'https://test.httpapi.com/api' 
      : 'https://httpapi.com/api';
  }

  /**
   * Get authentication parameters for API requests
   * @returns {Object} Auth parameters
   */
  getAuthParams() {
    return {
      'auth-userid': this.authUserId,
      'api-key': this.apiKey
    };
  }

  /**
   * Make authenticated API request
   * @param {string} endpoint - API endpoint
   * @param {Object} additionalParams - Additional parameters
   * @returns {Promise<Object>} API response
   */
  async makeRequest(endpoint, additionalParams = {}) {
    try {
      const params = {
        ...this.getAuthParams(),
        ...additionalParams
      };

      console.log(`🔍 Making request to: ${this.baseUrl}${endpoint}`);
      console.log('Parameters:', params);

      const response = await axios.get(`${this.baseUrl}${endpoint}`, {
        params,
        timeout: 30000,
        validateStatus: function (status) {
          return status < 500;
        }
      });

      console.log(`✅ Response received (${response.status}):`, response.data);

      return {
        success: response.status === 200,
        status: response.status,
        data: response.data,
        headers: response.headers
      };

    } catch (error) {
      console.error(`❌ Request failed for ${endpoint}:`, error.message);
      
      if (error.response) {
        return {
          success: false,
          status: error.response.status,
          error: error.response.data,
          message: error.message
        };
      }

      return {
        success: false,
        error: error.message,
        message: 'Network or request error occurred'
      };
    }
  }

  /**
   * Get details of all active products
   * @returns {Promise<Object>} Product details with productkey as key
   */
  async getProductDetails() {
    try {
      console.log('=== Fetching Product Details ===');
      const result = await this.makeRequest('/products/details.json');
      
      if (result.success) {
        // Process and organize product data
        const products = result.data;
        const processedProducts = {};
        
        Object.keys(products).forEach(productKey => {
          const product = products[productKey];
          processedProducts[productKey] = {
            ...product,
            productKey,
            // Add any additional processing here
            fetchedAt: new Date().toISOString()
          };
        });

        return {
          success: true,
          totalProducts: Object.keys(processedProducts).length,
          products: processedProducts,
          rawData: result.data
        };
      }

      return result;

    } catch (error) {
      console.error('❌ Failed to get product details:', error.message);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get product category to product keys mapping
   * @returns {Promise<Object>} Category mappings
   */
  async getCategoryKeysMapping() {
    try {
      console.log('=== Fetching Category-Keys Mapping ===');
      const result = await this.makeRequest('/products/category-keys-mapping.json');
      
      if (result.success) {
        const mappings = result.data;
        const processedMappings = {};
        
        Object.keys(mappings).forEach(category => {
          processedMappings[category] = {
            category,
            productKeys: mappings[category],
            productCount: Array.isArray(mappings[category]) ? mappings[category].length : 0,
            fetchedAt: new Date().toISOString()
          };
        });

        return {
          success: true,
          totalCategories: Object.keys(processedMappings).length,
          mappings: processedMappings,
          rawData: result.data
        };
      }

      return result;

    } catch (error) {
      console.error('❌ Failed to get category mappings:', error.message);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get aggregated product data (details + category mappings)
   * @returns {Promise<Object>} Complete product information
   */
  async getAggregatedProductData() {
    try {
      console.log('=== Fetching Aggregated Product Data ===');
      
      // Fetch both product details and category mappings concurrently
      const [detailsResult, mappingsResult] = await Promise.all([
        this.getProductDetails(),
        this.getCategoryKeysMapping()
      ]);

      if (!detailsResult.success || !mappingsResult.success) {
        return {
          success: false,
          error: 'Failed to fetch complete product data',
          details: detailsResult,
          mappings: mappingsResult
        };
      }

      // Combine and organize the data
      const aggregatedData = {
        success: true,
        fetchedAt: new Date().toISOString(),
        summary: {
          totalProducts: detailsResult.totalProducts,
          totalCategories: mappingsResult.totalCategories
        },
        productsByCategory: {},
        allProducts: detailsResult.products,
        categoryMappings: mappingsResult.mappings
      };

      // Organize products by category
      Object.keys(mappingsResult.mappings).forEach(category => {
        const categoryData = mappingsResult.mappings[category];
        aggregatedData.productsByCategory[category] = {
          category,
          productCount: categoryData.productCount,
          products: {}
        };

        // Add product details for each product in this category
        if (Array.isArray(categoryData.productKeys)) {
          categoryData.productKeys.forEach(productKey => {
            if (detailsResult.products[productKey]) {
              aggregatedData.productsByCategory[category].products[productKey] = 
                detailsResult.products[productKey];
            }
          });
        }
      });

      return aggregatedData;

    } catch (error) {
      console.error('❌ Failed to get aggregated product data:', error.message);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get products by specific category
   * @param {string} categoryName - Category name to filter by
   * @returns {Promise<Object>} Products in the specified category
   */
  async getProductsByCategory(categoryName) {
    try {
      console.log(`=== Fetching Products for Category: ${categoryName} ===`);
      
      const aggregatedData = await this.getAggregatedProductData();
      
      if (!aggregatedData.success) {
        return aggregatedData;
      }

      const categoryProducts = aggregatedData.productsByCategory[categoryName];
      
      if (!categoryProducts) {
        return {
          success: false,
          error: `Category '${categoryName}' not found`,
          availableCategories: Object.keys(aggregatedData.productsByCategory)
        };
      }

      return {
        success: true,
        category: categoryName,
        productCount: categoryProducts.productCount,
        products: categoryProducts.products,
        fetchedAt: aggregatedData.fetchedAt
      };

    } catch (error) {
      console.error(`❌ Failed to get products for category ${categoryName}:`, error.message);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Search products by name or description
   * @param {string} searchTerm - Term to search for
   * @returns {Promise<Object>} Matching products
   */
  async searchProducts(searchTerm) {
    try {
      console.log(`=== Searching Products: "${searchTerm}" ===`);
      
      const productDetails = await this.getProductDetails();
      
      if (!productDetails.success) {
        return productDetails;
      }

      const matchingProducts = {};
      const searchLower = searchTerm.toLowerCase();

      Object.keys(productDetails.products).forEach(productKey => {
        const product = productDetails.products[productKey];
        
        // Search in product key, name, description, etc.
        const searchableText = [
          productKey,
          product.name || '',
          product.description || '',
          product.type || ''
        ].join(' ').toLowerCase();

        if (searchableText.includes(searchLower)) {
          matchingProducts[productKey] = product;
        }
      });

      return {
        success: true,
        searchTerm,
        matchCount: Object.keys(matchingProducts).length,
        products: matchingProducts,
        fetchedAt: new Date().toISOString()
      };

    } catch (error) {
      console.error(`❌ Failed to search products:`, error.message);
      return {
        success: false,
        error: error.message
      };
    }
  }
}

// Export the class
module.exports = ResellerBizProductsAPI;

// Example usage if run directly
if (require.main === module) {
  async function example() {
    // Initialize with your credentials
    const client = new ResellerBizProductsAPI(
      0, // Replace with your auth-userid
      'your-api-key', // Replace with your API key
      true // Set to false for production
    );

    try {
      // Get all product details
      console.log('=== Product Details ===');
      const details = await client.getProductDetails();
      console.log(JSON.stringify(details, null, 2));

      // Get category mappings
      console.log('\n=== Category Mappings ===');
      const mappings = await client.getCategoryKeysMapping();
      console.log(JSON.stringify(mappings, null, 2));

      // Get aggregated data
      console.log('\n=== Aggregated Data ===');
      const aggregated = await client.getAggregatedProductData();
      console.log(JSON.stringify(aggregated, null, 2));

    } catch (error) {
      console.error('Example failed:', error);
    }
  }

  example();
}
