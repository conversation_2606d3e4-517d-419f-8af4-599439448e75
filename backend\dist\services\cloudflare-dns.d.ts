interface CloudflareRecord {
    id?: string;
    type: 'A' | 'AAAA' | 'CNAME' | 'MX' | 'TXT';
    name: string;
    content: string;
    ttl?: number;
    priority?: number;
    proxied?: boolean;
}
interface LoadBalancerPool {
    id?: string;
    name: string;
    origins: Array<{
        name: string;
        address: string;
        enabled: boolean;
        weight?: number;
    }>;
    monitor?: string;
    enabled: boolean;
}
export declare class CloudflareDNSService {
    private apiToken;
    private zoneId;
    private baseUrl;
    constructor();
    private makeRequest;
    createDNSRecord(record: CloudflareRecord): Promise<string>;
    updateDNSRecord(recordId: string, updates: Partial<CloudflareRecord>): Promise<void>;
    deleteDNSRecord(recordId: string): Promise<void>;
    getDNSRecords(name?: string, type?: string): Promise<CloudflareRecord[]>;
    setupRoundRobinDNS(subdomain: string, serverIPs: string[]): Promise<string[]>;
    createLoadBalancerPool(pool: LoadBalancerPool): Promise<string>;
    createLoadBalancer(name: string, hostname: string, poolIds: string[]): Promise<string>;
    setupLoadBalancing(subdomain: string, servers: Array<{
        ip: string;
        weight?: number;
    }>): Promise<{
        poolId: string;
        loadBalancerId: string;
    }>;
    updateServerHealth(poolId: string, serverAddress: string, enabled: boolean): Promise<void>;
    createHealthMonitor(name: string, path?: string): Promise<string>;
    getLoadBalancerAnalytics(loadBalancerId: string, since?: Date): Promise<any>;
    failoverToBackup(loadBalancerId: string, backupPoolId: string): Promise<void>;
    getZoneAnalytics(since?: Date): Promise<any>;
    purgeCache(urls: string[]): Promise<void>;
    getDNSStatus(): Promise<any>;
}
export declare const cloudflareDNS: CloudflareDNSService;
export {};
//# sourceMappingURL=cloudflare-dns.d.ts.map