import { ObjectId } from 'mongodb';
import { z } from 'zod';
export declare enum ApplicationStatus {
    DRAFT = "draft",
    BUILDING = "building",
    DEPLOYED = "deployed",
    FAILED = "failed",
    STOPPED = "stopped",
    SUSPENDED = "suspended"
}
export declare enum ApplicationType {
    WEB_SERVICE = "web_service",
    STATIC_SITE = "static_site",
    BACKGROUND_WORKER = "background_worker",
    CRON_JOB = "cron_job",
    PRIVATE_SERVICE = "private_service"
}
export interface EnvironmentConfig {
    name: string;
    variables: Record<string, string>;
    secrets: string[];
    build_command?: string;
    start_command?: string;
    dockerfile_path?: string;
    root_directory?: string;
    port?: number;
    health_check_path?: string;
}
export interface DeploymentConfig {
    auto_deploy: boolean;
    branch: string;
    build_command?: string;
    start_command?: string;
    pre_deploy_command?: string;
    post_deploy_command?: string;
    dockerfile_path?: string;
    root_directory?: string;
}
export interface ResourceLimits {
    cpu_shares?: number;
    memory_limit?: string;
    disk_limit?: string;
    bandwidth_limit?: string;
}
export interface Application {
    _id?: ObjectId;
    id?: string;
    user_id: string;
    name: string;
    description?: string;
    type: ApplicationType;
    status: ApplicationStatus;
    repository?: {
        url: string;
        branch: string;
        private: boolean;
        access_token?: string;
    };
    environment: EnvironmentConfig;
    deployment: DeploymentConfig;
    resources: ResourceLimits;
    server_id?: string;
    domain?: string;
    subdomain?: string;
    last_deployed_at?: Date;
    last_build_log?: string;
    build_duration?: number;
    created_at: Date;
    updated_at: Date;
    stats?: {
        total_deployments: number;
        successful_deployments: number;
        failed_deployments: number;
        last_deployment_status: string;
    };
}
export interface ApplicationResponse {
    id: string;
    user_id: string;
    name: string;
    description?: string;
    type: ApplicationType;
    status: ApplicationStatus;
    repository?: {
        url: string;
        branch: string;
        private: boolean;
    };
    environment: EnvironmentConfig;
    deployment: DeploymentConfig;
    resources: ResourceLimits;
    server_id?: string;
    domain?: string;
    subdomain?: string;
    last_deployed_at?: string;
    created_at: string;
    updated_at: string;
    stats?: {
        total_deployments: number;
        successful_deployments: number;
        failed_deployments: number;
        last_deployment_status: string;
    };
}
export declare const createApplicationSchema: z.ZodObject<{
    name: z.ZodString;
    description: z.ZodOptional<z.ZodString>;
    type: z.ZodNativeEnum<typeof ApplicationType>;
    repository: z.ZodOptional<z.ZodObject<{
        url: z.ZodString;
        branch: z.ZodDefault<z.ZodString>;
        private: z.ZodDefault<z.ZodBoolean>;
        access_token: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        url: string;
        branch: string;
        private: boolean;
        access_token?: string | undefined;
    }, {
        url: string;
        branch?: string | undefined;
        private?: boolean | undefined;
        access_token?: string | undefined;
    }>>;
    environment: z.ZodObject<{
        name: z.ZodDefault<z.ZodString>;
        variables: z.ZodDefault<z.ZodRecord<z.ZodString, z.ZodString>>;
        secrets: z.ZodDefault<z.ZodArray<z.ZodString, "many">>;
        build_command: z.ZodOptional<z.ZodString>;
        start_command: z.ZodOptional<z.ZodString>;
        dockerfile_path: z.ZodOptional<z.ZodString>;
        root_directory: z.ZodDefault<z.ZodString>;
        port: z.ZodOptional<z.ZodNumber>;
        health_check_path: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        name: string;
        variables: Record<string, string>;
        secrets: string[];
        root_directory: string;
        port?: number | undefined;
        build_command?: string | undefined;
        start_command?: string | undefined;
        dockerfile_path?: string | undefined;
        health_check_path?: string | undefined;
    }, {
        name?: string | undefined;
        port?: number | undefined;
        build_command?: string | undefined;
        start_command?: string | undefined;
        variables?: Record<string, string> | undefined;
        secrets?: string[] | undefined;
        dockerfile_path?: string | undefined;
        root_directory?: string | undefined;
        health_check_path?: string | undefined;
    }>;
    deployment: z.ZodObject<{
        auto_deploy: z.ZodDefault<z.ZodBoolean>;
        branch: z.ZodDefault<z.ZodString>;
        build_command: z.ZodOptional<z.ZodString>;
        start_command: z.ZodOptional<z.ZodString>;
        pre_deploy_command: z.ZodOptional<z.ZodString>;
        post_deploy_command: z.ZodOptional<z.ZodString>;
        dockerfile_path: z.ZodOptional<z.ZodString>;
        root_directory: z.ZodDefault<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        branch: string;
        root_directory: string;
        auto_deploy: boolean;
        build_command?: string | undefined;
        start_command?: string | undefined;
        dockerfile_path?: string | undefined;
        pre_deploy_command?: string | undefined;
        post_deploy_command?: string | undefined;
    }, {
        build_command?: string | undefined;
        start_command?: string | undefined;
        branch?: string | undefined;
        dockerfile_path?: string | undefined;
        root_directory?: string | undefined;
        auto_deploy?: boolean | undefined;
        pre_deploy_command?: string | undefined;
        post_deploy_command?: string | undefined;
    }>;
    resources: z.ZodDefault<z.ZodObject<{
        cpu_shares: z.ZodOptional<z.ZodNumber>;
        memory_limit: z.ZodOptional<z.ZodString>;
        disk_limit: z.ZodOptional<z.ZodString>;
        bandwidth_limit: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        bandwidth_limit?: string | undefined;
        cpu_shares?: number | undefined;
        memory_limit?: string | undefined;
        disk_limit?: string | undefined;
    }, {
        bandwidth_limit?: string | undefined;
        cpu_shares?: number | undefined;
        memory_limit?: string | undefined;
        disk_limit?: string | undefined;
    }>>;
}, "strip", z.ZodTypeAny, {
    type: ApplicationType;
    name: string;
    environment: {
        name: string;
        variables: Record<string, string>;
        secrets: string[];
        root_directory: string;
        port?: number | undefined;
        build_command?: string | undefined;
        start_command?: string | undefined;
        dockerfile_path?: string | undefined;
        health_check_path?: string | undefined;
    };
    deployment: {
        branch: string;
        root_directory: string;
        auto_deploy: boolean;
        build_command?: string | undefined;
        start_command?: string | undefined;
        dockerfile_path?: string | undefined;
        pre_deploy_command?: string | undefined;
        post_deploy_command?: string | undefined;
    };
    resources: {
        bandwidth_limit?: string | undefined;
        cpu_shares?: number | undefined;
        memory_limit?: string | undefined;
        disk_limit?: string | undefined;
    };
    description?: string | undefined;
    repository?: {
        url: string;
        branch: string;
        private: boolean;
        access_token?: string | undefined;
    } | undefined;
}, {
    type: ApplicationType;
    name: string;
    environment: {
        name?: string | undefined;
        port?: number | undefined;
        build_command?: string | undefined;
        start_command?: string | undefined;
        variables?: Record<string, string> | undefined;
        secrets?: string[] | undefined;
        dockerfile_path?: string | undefined;
        root_directory?: string | undefined;
        health_check_path?: string | undefined;
    };
    deployment: {
        build_command?: string | undefined;
        start_command?: string | undefined;
        branch?: string | undefined;
        dockerfile_path?: string | undefined;
        root_directory?: string | undefined;
        auto_deploy?: boolean | undefined;
        pre_deploy_command?: string | undefined;
        post_deploy_command?: string | undefined;
    };
    description?: string | undefined;
    repository?: {
        url: string;
        branch?: string | undefined;
        private?: boolean | undefined;
        access_token?: string | undefined;
    } | undefined;
    resources?: {
        bandwidth_limit?: string | undefined;
        cpu_shares?: number | undefined;
        memory_limit?: string | undefined;
        disk_limit?: string | undefined;
    } | undefined;
}>;
export declare const updateApplicationSchema: z.ZodObject<{
    name: z.ZodOptional<z.ZodString>;
    description: z.ZodOptional<z.ZodOptional<z.ZodString>>;
    type: z.ZodOptional<z.ZodNativeEnum<typeof ApplicationType>>;
    repository: z.ZodOptional<z.ZodOptional<z.ZodObject<{
        url: z.ZodString;
        branch: z.ZodDefault<z.ZodString>;
        private: z.ZodDefault<z.ZodBoolean>;
        access_token: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        url: string;
        branch: string;
        private: boolean;
        access_token?: string | undefined;
    }, {
        url: string;
        branch?: string | undefined;
        private?: boolean | undefined;
        access_token?: string | undefined;
    }>>>;
    environment: z.ZodOptional<z.ZodObject<{
        name: z.ZodDefault<z.ZodString>;
        variables: z.ZodDefault<z.ZodRecord<z.ZodString, z.ZodString>>;
        secrets: z.ZodDefault<z.ZodArray<z.ZodString, "many">>;
        build_command: z.ZodOptional<z.ZodString>;
        start_command: z.ZodOptional<z.ZodString>;
        dockerfile_path: z.ZodOptional<z.ZodString>;
        root_directory: z.ZodDefault<z.ZodString>;
        port: z.ZodOptional<z.ZodNumber>;
        health_check_path: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        name: string;
        variables: Record<string, string>;
        secrets: string[];
        root_directory: string;
        port?: number | undefined;
        build_command?: string | undefined;
        start_command?: string | undefined;
        dockerfile_path?: string | undefined;
        health_check_path?: string | undefined;
    }, {
        name?: string | undefined;
        port?: number | undefined;
        build_command?: string | undefined;
        start_command?: string | undefined;
        variables?: Record<string, string> | undefined;
        secrets?: string[] | undefined;
        dockerfile_path?: string | undefined;
        root_directory?: string | undefined;
        health_check_path?: string | undefined;
    }>>;
    deployment: z.ZodOptional<z.ZodObject<{
        auto_deploy: z.ZodDefault<z.ZodBoolean>;
        branch: z.ZodDefault<z.ZodString>;
        build_command: z.ZodOptional<z.ZodString>;
        start_command: z.ZodOptional<z.ZodString>;
        pre_deploy_command: z.ZodOptional<z.ZodString>;
        post_deploy_command: z.ZodOptional<z.ZodString>;
        dockerfile_path: z.ZodOptional<z.ZodString>;
        root_directory: z.ZodDefault<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        branch: string;
        root_directory: string;
        auto_deploy: boolean;
        build_command?: string | undefined;
        start_command?: string | undefined;
        dockerfile_path?: string | undefined;
        pre_deploy_command?: string | undefined;
        post_deploy_command?: string | undefined;
    }, {
        build_command?: string | undefined;
        start_command?: string | undefined;
        branch?: string | undefined;
        dockerfile_path?: string | undefined;
        root_directory?: string | undefined;
        auto_deploy?: boolean | undefined;
        pre_deploy_command?: string | undefined;
        post_deploy_command?: string | undefined;
    }>>;
    resources: z.ZodOptional<z.ZodDefault<z.ZodObject<{
        cpu_shares: z.ZodOptional<z.ZodNumber>;
        memory_limit: z.ZodOptional<z.ZodString>;
        disk_limit: z.ZodOptional<z.ZodString>;
        bandwidth_limit: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        bandwidth_limit?: string | undefined;
        cpu_shares?: number | undefined;
        memory_limit?: string | undefined;
        disk_limit?: string | undefined;
    }, {
        bandwidth_limit?: string | undefined;
        cpu_shares?: number | undefined;
        memory_limit?: string | undefined;
        disk_limit?: string | undefined;
    }>>>;
}, "strip", z.ZodTypeAny, {
    type?: ApplicationType | undefined;
    name?: string | undefined;
    description?: string | undefined;
    repository?: {
        url: string;
        branch: string;
        private: boolean;
        access_token?: string | undefined;
    } | undefined;
    environment?: {
        name: string;
        variables: Record<string, string>;
        secrets: string[];
        root_directory: string;
        port?: number | undefined;
        build_command?: string | undefined;
        start_command?: string | undefined;
        dockerfile_path?: string | undefined;
        health_check_path?: string | undefined;
    } | undefined;
    deployment?: {
        branch: string;
        root_directory: string;
        auto_deploy: boolean;
        build_command?: string | undefined;
        start_command?: string | undefined;
        dockerfile_path?: string | undefined;
        pre_deploy_command?: string | undefined;
        post_deploy_command?: string | undefined;
    } | undefined;
    resources?: {
        bandwidth_limit?: string | undefined;
        cpu_shares?: number | undefined;
        memory_limit?: string | undefined;
        disk_limit?: string | undefined;
    } | undefined;
}, {
    type?: ApplicationType | undefined;
    name?: string | undefined;
    description?: string | undefined;
    repository?: {
        url: string;
        branch?: string | undefined;
        private?: boolean | undefined;
        access_token?: string | undefined;
    } | undefined;
    environment?: {
        name?: string | undefined;
        port?: number | undefined;
        build_command?: string | undefined;
        start_command?: string | undefined;
        variables?: Record<string, string> | undefined;
        secrets?: string[] | undefined;
        dockerfile_path?: string | undefined;
        root_directory?: string | undefined;
        health_check_path?: string | undefined;
    } | undefined;
    deployment?: {
        build_command?: string | undefined;
        start_command?: string | undefined;
        branch?: string | undefined;
        dockerfile_path?: string | undefined;
        root_directory?: string | undefined;
        auto_deploy?: boolean | undefined;
        pre_deploy_command?: string | undefined;
        post_deploy_command?: string | undefined;
    } | undefined;
    resources?: {
        bandwidth_limit?: string | undefined;
        cpu_shares?: number | undefined;
        memory_limit?: string | undefined;
        disk_limit?: string | undefined;
    } | undefined;
}>;
export type CreateApplicationRequest = z.infer<typeof createApplicationSchema>;
export type UpdateApplicationRequest = z.infer<typeof updateApplicationSchema>;
export declare function toApplicationResponse(application: Application): ApplicationResponse;
//# sourceMappingURL=application.d.ts.map