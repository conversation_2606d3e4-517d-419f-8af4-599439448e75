import { logger } from '../utils/logger';
import { execAsync } from '../utils/exec';
import { getSharedHostingService } from './shared-hosting';
import { getServerManagementService } from './server-management';

export interface DeploymentJob {
  id: string;
  user_id: string;
  application_id: string;
  type: 'provision_user' | 'deploy_app' | 'scale_resources' | 'backup_data' | 'monitor_health';
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  progress: number;
  logs: string[];
  started_at?: string;
  completed_at?: string;
  error_message?: string;
  metadata: Record<string, any>;
}

export interface AutomationMetrics {
  total_jobs: number;
  pending_jobs: number;
  running_jobs: number;
  completed_jobs: number;
  failed_jobs: number;
  success_rate: number;
  average_completion_time: number;
  jobs_last_24h: number;
}

export interface ResourceMonitoringData {
  server_id: string;
  timestamp: string;
  cpu_usage: number;
  memory_usage: number;
  disk_usage: number;
  network_in: number;
  network_out: number;
  active_users: number;
  load_average: number[];
}

class DeploymentAutomationService {
  private readonly jobs: Map<string, DeploymentJob> = new Map();
  private readonly monitoringData: ResourceMonitoringData[] = [];
  private readonly SHARED_SERVER_IP = '*************';

  // Create and queue deployment job
  async createDeploymentJob(
    type: DeploymentJob['type'],
    userId: string,
    applicationId: string,
    metadata: Record<string, any> = {}
  ): Promise<DeploymentJob> {
    const job: DeploymentJob = {
      id: `job_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      user_id: userId,
      application_id: applicationId,
      type,
      status: 'pending',
      progress: 0,
      logs: [],
      metadata
    };

    this.jobs.set(job.id, job);
    logger.info(`Created deployment job: ${job.id} (${type})`);

    // Start job execution
    this.executeJob(job.id);

    return job;
  }

  // Execute deployment job
  private async executeJob(jobId: string): Promise<void> {
    const job = this.jobs.get(jobId);
    if (!job) {
      logger.error(`Job not found: ${jobId}`);
      return;
    }

    try {
      job.status = 'running';
      job.started_at = new Date().toISOString();
      job.logs.push(`Started ${job.type} job at ${job.started_at}`);

      switch (job.type) {
        case 'provision_user':
          await this.executeProvisionUser(job);
          break;
        case 'deploy_app':
          await this.executeDeployApp(job);
          break;
        case 'scale_resources':
          await this.executeScaleResources(job);
          break;
        case 'backup_data':
          await this.executeBackupData(job);
          break;
        case 'monitor_health':
          await this.executeMonitorHealth(job);
          break;
        default:
          throw new Error(`Unknown job type: ${job.type}`);
      }

      job.status = 'completed';
      job.progress = 100;
      job.completed_at = new Date().toISOString();
      job.logs.push(`Completed ${job.type} job at ${job.completed_at}`);

      logger.info(`Job completed successfully: ${jobId}`);
    } catch (error) {
      job.status = 'failed';
      job.error_message = error instanceof Error ? error.message : 'Unknown error';
      job.completed_at = new Date().toISOString();
      job.logs.push(`Job failed: ${job.error_message}`);

      logger.error(`Job failed: ${jobId}`, error);
    }
  }

  // Execute user provisioning
  private async executeProvisionUser(job: DeploymentJob): Promise<void> {
    const { username, plan_id } = job.metadata;

    job.progress = 10;
    job.logs.push('Creating Linux user account...');
    
    const sharedHostingService = getSharedHostingService();
    await sharedHostingService.createUser({
      user_id: job.user_id,
      username,
      plan: plan_id
    });

    job.progress = 50;
    job.logs.push('Setting up user environment...');

    // Setup user directory structure
    await execAsync(`ssh root@${this.SHARED_SERVER_IP} "mkdir -p /var/www/user_${username}/{public_html,logs,tmp}"`);
    await execAsync(`ssh root@${this.SHARED_SERVER_IP} "chown -R ${username}:${username} /var/www/user_${username}"`);

    job.progress = 80;
    job.logs.push('Configuring security and resource limits...');

    // Apply security settings
    await execAsync(`ssh root@${this.SHARED_SERVER_IP} "chmod 700 /var/www/user_${username}"`);
    
    job.progress = 100;
    job.logs.push('User provisioning completed successfully');
  }

  // Execute application deployment
  private async executeDeployApp(job: DeploymentJob): Promise<void> {
    const { username, app_name, app_type, framework, git_repo, build_command, start_command } = job.metadata;

    job.progress = 10;
    job.logs.push('Preparing application deployment...');

    const sharedHostingService = getSharedHostingService();
    
    job.progress = 30;
    job.logs.push('Creating application structure...');

    await sharedHostingService.createApplication(job.user_id, {
      name: app_name,
      type: app_type,
      framework,
      git_repo,
      build_command,
      start_command
    });

    job.progress = 60;
    job.logs.push('Installing dependencies...');

    // Clone repository if provided
    if (git_repo) {
      await execAsync(`ssh root@${this.SHARED_SERVER_IP} "cd /var/www/user_${username} && sudo -u ${username} git clone ${git_repo} ${app_name}"`);
      job.logs.push(`Cloned repository: ${git_repo}`);
    }

    job.progress = 80;
    job.logs.push('Building application...');

    // Run build command if provided
    if (build_command) {
      await execAsync(`ssh root@${this.SHARED_SERVER_IP} "cd /var/www/user_${username}/${app_name} && sudo -u ${username} ${build_command}"`);
      job.logs.push(`Build completed: ${build_command}`);
    }

    job.progress = 90;
    job.logs.push('Starting application...');

    // Start application if it's a web service
    if (app_type === 'web-service' && start_command) {
      await execAsync(`ssh root@${this.SHARED_SERVER_IP} "cd /var/www/user_${username}/${app_name} && sudo -u ${username} nohup ${start_command} > ../logs/${app_name}.log 2>&1 &"`);
      job.logs.push(`Application started: ${start_command}`);
    }

    job.progress = 100;
    job.logs.push('Application deployment completed successfully');
  }

  // Execute resource scaling
  private async executeScaleResources(job: DeploymentJob): Promise<void> {
    const { username, cpu_limit, memory_limit, bandwidth_limit } = job.metadata;

    job.progress = 20;
    job.logs.push('Updating resource limits...');

    // Update systemd resource limits
    if (cpu_limit) {
      await execAsync(`ssh root@${this.SHARED_SERVER_IP} "systemctl set-property user-${username}.slice CPUQuota=${cpu_limit}%"`);
      job.logs.push(`Updated CPU limit: ${cpu_limit}%`);
    }

    job.progress = 50;
    
    if (memory_limit) {
      await execAsync(`ssh root@${this.SHARED_SERVER_IP} "systemctl set-property user-${username}.slice MemoryMax=${memory_limit}M"`);
      job.logs.push(`Updated memory limit: ${memory_limit}MB`);
    }

    job.progress = 80;

    if (bandwidth_limit) {
      // Update traffic control rules
      await execAsync(`ssh root@${this.SHARED_SERVER_IP} "tc class change dev eth0 parent 1: classid 1:${username} htb rate ${bandwidth_limit}mbit"`);
      job.logs.push(`Updated bandwidth limit: ${bandwidth_limit}Mbps`);
    }

    job.progress = 100;
    job.logs.push('Resource scaling completed successfully');
  }

  // Execute data backup
  private async executeBackupData(job: DeploymentJob): Promise<void> {
    const { username } = job.metadata;

    job.progress = 10;
    job.logs.push('Starting data backup...');

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupDir = `/var/backups/user_${username}_${timestamp}`;

    job.progress = 30;
    job.logs.push('Creating backup directory...');

    await execAsync(`ssh root@${this.SHARED_SERVER_IP} "mkdir -p ${backupDir}"`);

    job.progress = 50;
    job.logs.push('Backing up user data...');

    // Backup user files
    await execAsync(`ssh root@${this.SHARED_SERVER_IP} "tar -czf ${backupDir}/files.tar.gz -C /var/www/user_${username} ."`);

    job.progress = 80;
    job.logs.push('Compressing backup...');

    // Create final backup archive
    await execAsync(`ssh root@${this.SHARED_SERVER_IP} "tar -czf /var/backups/user_${username}_${timestamp}.tar.gz -C /var/backups user_${username}_${timestamp}"`);
    await execAsync(`ssh root@${this.SHARED_SERVER_IP} "rm -rf ${backupDir}"`);

    job.progress = 100;
    job.logs.push(`Backup completed: user_${username}_${timestamp}.tar.gz`);
  }

  // Execute health monitoring
  private async executeMonitorHealth(job: DeploymentJob): Promise<void> {
    job.progress = 20;
    job.logs.push('Collecting server metrics...');

    const serverManagement = getServerManagementService();
    const metrics = await serverManagement.getServerMetrics(this.SHARED_SERVER_IP);

    job.progress = 50;
    job.logs.push('Analyzing system health...');

    // Store monitoring data
    const monitoringData: ResourceMonitoringData = {
      server_id: 'shared-server-main',
      timestamp: new Date().toISOString(),
      cpu_usage: metrics.cpu_usage,
      memory_usage: metrics.memory_usage,
      disk_usage: metrics.disk_usage,
      network_in: metrics.network_in,
      network_out: metrics.network_out,
      active_users: metrics.active_users,
      load_average: metrics.load_average
    };

    this.monitoringData.push(monitoringData);

    // Keep only last 1000 entries
    if (this.monitoringData.length > 1000) {
      this.monitoringData.splice(0, this.monitoringData.length - 1000);
    }

    job.progress = 80;
    job.logs.push('Checking for alerts...');

    // Check for resource alerts
    const alerts = [];
    if (metrics.cpu_usage > 80) alerts.push('High CPU usage detected');
    if (metrics.memory_usage > 80) alerts.push('High memory usage detected');
    if (metrics.disk_usage > 90) alerts.push('High disk usage detected');

    if (alerts.length > 0) {
      job.logs.push(`Alerts generated: ${alerts.join(', ')}`);
    }

    job.progress = 100;
    job.logs.push('Health monitoring completed');
  }

  // Get job status
  getJob(jobId: string): DeploymentJob | undefined {
    return this.jobs.get(jobId);
  }

  // List jobs
  listJobs(userId?: string, status?: DeploymentJob['status']): DeploymentJob[] {
    let jobs = Array.from(this.jobs.values());

    if (userId) {
      jobs = jobs.filter(job => job.user_id === userId);
    }

    if (status) {
      jobs = jobs.filter(job => job.status === status);
    }

    return jobs.sort((a, b) => new Date(b.started_at || '').getTime() - new Date(a.started_at || '').getTime());
  }

  // Get automation metrics
  getAutomationMetrics(): AutomationMetrics {
    const jobs = Array.from(this.jobs.values());
    const completedJobs = jobs.filter(job => job.status === 'completed');
    const failedJobs = jobs.filter(job => job.status === 'failed');
    
    const successRate = jobs.length > 0 ? (completedJobs.length / jobs.length) * 100 : 100;
    
    const completionTimes = completedJobs
      .filter(job => job.started_at && job.completed_at)
      .map(job => new Date(job.completed_at!).getTime() - new Date(job.started_at!).getTime());
    
    const averageCompletionTime = completionTimes.length > 0 
      ? completionTimes.reduce((sum, time) => sum + time, 0) / completionTimes.length / 1000 // Convert to seconds
      : 0;

    const last24h = jobs.filter(job => 
      job.started_at && new Date(job.started_at) > new Date(Date.now() - 24 * 60 * 60 * 1000)
    ).length;

    return {
      total_jobs: jobs.length,
      pending_jobs: jobs.filter(job => job.status === 'pending').length,
      running_jobs: jobs.filter(job => job.status === 'running').length,
      completed_jobs: completedJobs.length,
      failed_jobs: failedJobs.length,
      success_rate: Math.round(successRate),
      average_completion_time: Math.round(averageCompletionTime),
      jobs_last_24h: last24h
    };
  }

  // Get monitoring data
  getMonitoringData(hours: number = 24): ResourceMonitoringData[] {
    const cutoff = new Date(Date.now() - hours * 60 * 60 * 1000);
    return this.monitoringData.filter(data => new Date(data.timestamp) > cutoff);
  }

  // Cancel job
  async cancelJob(jobId: string): Promise<void> {
    const job = this.jobs.get(jobId);
    if (job && (job.status === 'pending' || job.status === 'running')) {
      job.status = 'cancelled';
      job.completed_at = new Date().toISOString();
      job.logs.push('Job cancelled by user');
      logger.info(`Job cancelled: ${jobId}`);
    }
  }
}

// Singleton instance
let deploymentAutomationService: DeploymentAutomationService;

export function getDeploymentAutomationService(): DeploymentAutomationService {
  if (!deploymentAutomationService) {
    deploymentAutomationService = new DeploymentAutomationService();
  }
  return deploymentAutomationService;
}
